"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboard)/map/page",{

/***/ "(app-pages-browser)/./src/app/(dashboard)/map/page.tsx":
/*!******************************************!*\
  !*** ./src/app/(dashboard)/map/page.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ MapPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./src/components/ui/select.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/separator */ \"(app-pages-browser)/./src/components/ui/separator.tsx\");\n/* harmony import */ var _hooks_useApi__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/useApi */ \"(app-pages-browser)/./src/hooks/useApi.ts\");\n/* harmony import */ var _lib_maps_utils__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/maps-utils */ \"(app-pages-browser)/./src/lib/maps-utils.ts\");\n/* harmony import */ var _googlemaps_js_api_loader__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @googlemaps/js-api-loader */ \"(app-pages-browser)/./node_modules/@googlemaps/js-api-loader/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_Download_ExternalLink_Eye_Filter_FolderOpen_Layers_MapPin_Maximize_RefreshCw_Search_Share_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,Clock,Download,ExternalLink,Eye,Filter,FolderOpen,Layers,MapPin,Maximize,RefreshCw,Search,Share,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_Download_ExternalLink_Eye_Filter_FolderOpen_Layers_MapPin_Maximize_RefreshCw_Search_Share_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,Clock,Download,ExternalLink,Eye,Filter,FolderOpen,Layers,MapPin,Maximize,RefreshCw,Search,Share,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_Download_ExternalLink_Eye_Filter_FolderOpen_Layers_MapPin_Maximize_RefreshCw_Search_Share_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,Clock,Download,ExternalLink,Eye,Filter,FolderOpen,Layers,MapPin,Maximize,RefreshCw,Search,Share,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_Download_ExternalLink_Eye_Filter_FolderOpen_Layers_MapPin_Maximize_RefreshCw_Search_Share_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,Clock,Download,ExternalLink,Eye,Filter,FolderOpen,Layers,MapPin,Maximize,RefreshCw,Search,Share,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/layers.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_Download_ExternalLink_Eye_Filter_FolderOpen_Layers_MapPin_Maximize_RefreshCw_Search_Share_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,Clock,Download,ExternalLink,Eye,Filter,FolderOpen,Layers,MapPin,Maximize,RefreshCw,Search,Share,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/maximize.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_Download_ExternalLink_Eye_Filter_FolderOpen_Layers_MapPin_Maximize_RefreshCw_Search_Share_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,Clock,Download,ExternalLink,Eye,Filter,FolderOpen,Layers,MapPin,Maximize,RefreshCw,Search,Share,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_Download_ExternalLink_Eye_Filter_FolderOpen_Layers_MapPin_Maximize_RefreshCw_Search_Share_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,Clock,Download,ExternalLink,Eye,Filter,FolderOpen,Layers,MapPin,Maximize,RefreshCw,Search,Share,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/folder-open.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_Download_ExternalLink_Eye_Filter_FolderOpen_Layers_MapPin_Maximize_RefreshCw_Search_Share_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,Clock,Download,ExternalLink,Eye,Filter,FolderOpen,Layers,MapPin,Maximize,RefreshCw,Search,Share,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_Download_ExternalLink_Eye_Filter_FolderOpen_Layers_MapPin_Maximize_RefreshCw_Search_Share_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,Clock,Download,ExternalLink,Eye,Filter,FolderOpen,Layers,MapPin,Maximize,RefreshCw,Search,Share,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_Download_ExternalLink_Eye_Filter_FolderOpen_Layers_MapPin_Maximize_RefreshCw_Search_Share_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,Clock,Download,ExternalLink,Eye,Filter,FolderOpen,Layers,MapPin,Maximize,RefreshCw,Search,Share,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/funnel.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_Download_ExternalLink_Eye_Filter_FolderOpen_Layers_MapPin_Maximize_RefreshCw_Search_Share_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,Clock,Download,ExternalLink,Eye,Filter,FolderOpen,Layers,MapPin,Maximize,RefreshCw,Search,Share,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_Download_ExternalLink_Eye_Filter_FolderOpen_Layers_MapPin_Maximize_RefreshCw_Search_Share_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,Clock,Download,ExternalLink,Eye,Filter,FolderOpen,Layers,MapPin,Maximize,RefreshCw,Search,Share,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_Download_ExternalLink_Eye_Filter_FolderOpen_Layers_MapPin_Maximize_RefreshCw_Search_Share_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,Clock,Download,ExternalLink,Eye,Filter,FolderOpen,Layers,MapPin,Maximize,RefreshCw,Search,Share,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_Download_ExternalLink_Eye_Filter_FolderOpen_Layers_MapPin_Maximize_RefreshCw_Search_Share_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,Clock,Download,ExternalLink,Eye,Filter,FolderOpen,Layers,MapPin,Maximize,RefreshCw,Search,Share,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_Download_ExternalLink_Eye_Filter_FolderOpen_Layers_MapPin_Maximize_RefreshCw_Search_Share_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,Clock,Download,ExternalLink,Eye,Filter,FolderOpen,Layers,MapPin,Maximize,RefreshCw,Search,Share,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_Download_ExternalLink_Eye_Filter_FolderOpen_Layers_MapPin_Maximize_RefreshCw_Search_Share_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,Clock,Download,ExternalLink,Eye,Filter,FolderOpen,Layers,MapPin,Maximize,RefreshCw,Search,Share,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/external-link.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_Download_ExternalLink_Eye_Filter_FolderOpen_Layers_MapPin_Maximize_RefreshCw_Search_Share_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,Clock,Download,ExternalLink,Eye,Filter,FolderOpen,Layers,MapPin,Maximize,RefreshCw,Search,Share,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/share.js\");\n/* harmony import */ var _barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=format!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/format.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nfunction MapPage() {\n    _s();\n    // Data hooks\n    const { data: clients = [], loading: clientsLoading } = (0,_hooks_useApi__WEBPACK_IMPORTED_MODULE_8__.useClients)();\n    const { data: projects = [], loading: projectsLoading } = (0,_hooks_useApi__WEBPACK_IMPORTED_MODULE_8__.useProjects)();\n    const { data: schedules = [], loading: schedulesLoading } = (0,_hooks_useApi__WEBPACK_IMPORTED_MODULE_8__.useSchedules)();\n    // Calculate loading state early\n    const loading = clientsLoading || projectsLoading || schedulesLoading;\n    // Debug logging\n    console.log('Map page data:', {\n        clients: clients === null || clients === void 0 ? void 0 : clients.length,\n        projects: projects === null || projects === void 0 ? void 0 : projects.length,\n        schedules: schedules === null || schedules === void 0 ? void 0 : schedules.length,\n        clientsLoading,\n        projectsLoading,\n        schedulesLoading,\n        loading\n    });\n    // State\n    const [filters, setFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        clientIds: [],\n        projectIds: [],\n        scheduleIds: [],\n        dateRange: {},\n        searchTerm: ''\n    });\n    const [mapLocations, setMapLocations] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedLocation, setSelectedLocation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [mapType, setMapType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('markers');\n    const [isFullscreen, setIsFullscreen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [mapLoading, setMapLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [mapError, setMapError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Map refs\n    const mapRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const mapInstanceRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const markersRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)([]);\n    const heatmapRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Calculate metrics based on filtered data\n    const metrics = {\n        totalLocations: mapLocations.length,\n        totalValue: mapLocations.reduce((sum, loc)=>sum + (loc.value || 0), 0),\n        clientsCount: new Set(mapLocations.map((loc)=>{\n            var _loc_client;\n            return (_loc_client = loc.client) === null || _loc_client === void 0 ? void 0 : _loc_client.id;\n        }).filter(Boolean)).size,\n        projectsCount: new Set(mapLocations.map((loc)=>{\n            var _loc_project;\n            return (_loc_project = loc.project) === null || _loc_project === void 0 ? void 0 : _loc_project.id;\n        }).filter(Boolean)).size,\n        schedulesCount: mapLocations.filter((loc)=>loc.type === 'schedule').length\n    };\n    // Get filtered projects based on selected clients\n    const filteredProjects = (projects || []).filter((project)=>filters.clientIds.length === 0 || filters.clientIds.includes(project.client_id));\n    // Get filtered schedules based on selected projects\n    const filteredSchedules = (schedules || []).filter((schedule)=>filters.projectIds.length === 0 || filters.projectIds.includes(schedule.project_id));\n    // Initialize Google Maps\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MapPage.useEffect\": ()=>{\n            const initMap = {\n                \"MapPage.useEffect.initMap\": async ()=>{\n                    if (!mapRef.current) return;\n                    try {\n                        setMapLoading(true);\n                        setMapError(null);\n                        // Add a small delay to ensure DOM is ready\n                        await new Promise({\n                            \"MapPage.useEffect.initMap\": (resolve)=>setTimeout(resolve, 100)\n                        }[\"MapPage.useEffect.initMap\"]);\n                        const loader = new _googlemaps_js_api_loader__WEBPACK_IMPORTED_MODULE_10__.Loader({\n                            apiKey: \"AIzaSyDmpk_NV3EKROQpR6aVJkid9rWuxF4O7G4\" || 0,\n                            version: 'weekly',\n                            libraries: [\n                                'visualization',\n                                'places'\n                            ]\n                        });\n                        const google1 = await loader.load();\n                        const map = new google1.maps.Map(mapRef.current, {\n                            center: {\n                                lat: 11.1271,\n                                lng: 78.6569\n                            },\n                            zoom: 7,\n                            mapTypeId: google1.maps.MapTypeId.ROADMAP,\n                            styles: [\n                                {\n                                    featureType: 'poi',\n                                    elementType: 'labels',\n                                    stylers: [\n                                        {\n                                            visibility: 'off'\n                                        }\n                                    ]\n                                }\n                            ]\n                        });\n                        mapInstanceRef.current = map;\n                        setMapLoading(false);\n                    } catch (error) {\n                        console.error('Error loading Google Maps:', error);\n                        setMapError('Failed to load Google Maps. Please check your API key.');\n                        setMapLoading(false);\n                    }\n                }\n            }[\"MapPage.useEffect.initMap\"];\n            // Only initialize map after data is loaded\n            if (!loading && clients && projects && schedules) {\n                initMap();\n            }\n        }\n    }[\"MapPage.useEffect\"], [\n        loading,\n        clients,\n        projects,\n        schedules\n    ]);\n    // Update map markers and heatmap\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MapPage.useEffect\": ()=>{\n            if (!mapInstanceRef.current || mapLoading) return;\n            // Clear existing markers\n            markersRef.current.forEach({\n                \"MapPage.useEffect\": (marker)=>marker.setMap(null)\n            }[\"MapPage.useEffect\"]);\n            markersRef.current = [];\n            // Clear existing heatmap\n            if (heatmapRef.current) {\n                heatmapRef.current.setMap(null);\n                heatmapRef.current = null;\n            }\n            if (mapType === 'markers') {\n                // Add markers for each location\n                mapLocations.forEach({\n                    \"MapPage.useEffect\": (location)=>{\n                        if (!location.coordinates) return;\n                        const marker = new google.maps.Marker({\n                            position: location.coordinates,\n                            map: mapInstanceRef.current,\n                            title: location.name,\n                            icon: {\n                                path: google.maps.SymbolPath.CIRCLE,\n                                scale: 8,\n                                fillColor: getMarkerColor(location),\n                                fillOpacity: 0.8,\n                                strokeColor: '#ffffff',\n                                strokeWeight: 2\n                            }\n                        });\n                        // Add click listener\n                        marker.addListener('click', {\n                            \"MapPage.useEffect\": ()=>{\n                                var // Center map on clicked marker\n                                _mapInstanceRef_current;\n                                setSelectedLocation(location);\n                                (_mapInstanceRef_current = mapInstanceRef.current) === null || _mapInstanceRef_current === void 0 ? void 0 : _mapInstanceRef_current.panTo(location.coordinates);\n                            }\n                        }[\"MapPage.useEffect\"]);\n                        // Add hover info window\n                        const infoWindow = new google.maps.InfoWindow({\n                            content: '\\n            <div class=\"p-2\">\\n              <h3 class=\"font-semibold\">'.concat(location.name, '</h3>\\n              <p class=\"text-sm text-gray-600\">').concat(location.location, \"</p>\\n              \").concat(location.client ? '<p class=\"text-xs\">Client: '.concat(location.client.name, \"</p>\") : '', \"\\n              \").concat(location.value ? '<p class=\"text-xs\">Value: ₹'.concat(location.value.toLocaleString(), \"</p>\") : '', \"\\n              \").concat(location.status ? '<p class=\"text-xs\">Status: '.concat(location.status, \"</p>\") : '', \"\\n            </div>\\n          \")\n                        });\n                        marker.addListener('mouseover', {\n                            \"MapPage.useEffect\": ()=>{\n                                infoWindow.open(mapInstanceRef.current, marker);\n                            }\n                        }[\"MapPage.useEffect\"]);\n                        marker.addListener('mouseout', {\n                            \"MapPage.useEffect\": ()=>{\n                                infoWindow.close();\n                            }\n                        }[\"MapPage.useEffect\"]);\n                        markersRef.current.push(marker);\n                    }\n                }[\"MapPage.useEffect\"]);\n            } else if (mapType === 'heatmap') {\n                // Create heatmap data\n                const heatmapData = mapLocations.filter({\n                    \"MapPage.useEffect.heatmapData\": (location)=>location.coordinates\n                }[\"MapPage.useEffect.heatmapData\"]).map({\n                    \"MapPage.useEffect.heatmapData\": (location)=>({\n                            location: new google.maps.LatLng(location.coordinates.lat, location.coordinates.lng),\n                            weight: location.value ? Math.log(location.value + 1) : 1\n                        })\n                }[\"MapPage.useEffect.heatmapData\"]);\n                if (heatmapData.length > 0) {\n                    heatmapRef.current = new google.maps.visualization.HeatmapLayer({\n                        data: heatmapData,\n                        map: mapInstanceRef.current,\n                        radius: 50,\n                        opacity: 0.8\n                    });\n                }\n            }\n            // Adjust map bounds to fit all markers\n            if (mapLocations.length > 0 && mapLocations.some({\n                \"MapPage.useEffect\": (l)=>l.coordinates\n            }[\"MapPage.useEffect\"])) {\n                const bounds = new google.maps.LatLngBounds();\n                mapLocations.forEach({\n                    \"MapPage.useEffect\": (location)=>{\n                        if (location.coordinates) {\n                            bounds.extend(location.coordinates);\n                        }\n                    }\n                }[\"MapPage.useEffect\"]);\n                mapInstanceRef.current.fitBounds(bounds);\n                // Ensure minimum zoom level\n                const listener = google.maps.event.addListener(mapInstanceRef.current, 'bounds_changed', {\n                    \"MapPage.useEffect.listener\": ()=>{\n                        if (mapInstanceRef.current.getZoom() > 15) {\n                            mapInstanceRef.current.setZoom(15);\n                        }\n                        google.maps.event.removeListener(listener);\n                    }\n                }[\"MapPage.useEffect.listener\"]);\n            }\n        }\n    }[\"MapPage.useEffect\"], [\n        mapLocations,\n        mapType,\n        mapLoading\n    ]);\n    // Get marker color based on location type and status\n    const getMarkerColor = (location)=>{\n        if (location.type === 'schedule') {\n            switch(location.status){\n                case 'completed':\n                    return '#10b981' // green\n                    ;\n                case 'scheduled':\n                    return '#f59e0b' // amber\n                    ;\n                case 'in_progress':\n                    return '#3b82f6' // blue\n                    ;\n                case 'cancelled':\n                    return '#ef4444' // red\n                    ;\n                default:\n                    return '#6b7280' // gray\n                    ;\n            }\n        } else if (location.type === 'project') {\n            switch(location.status){\n                case 'active':\n                    return '#3b82f6' // blue\n                    ;\n                case 'completed':\n                    return '#10b981' // green\n                    ;\n                case 'on_hold':\n                    return '#f59e0b' // amber\n                    ;\n                case 'cancelled':\n                    return '#ef4444' // red\n                    ;\n                default:\n                    return '#6b7280' // gray\n                    ;\n            }\n        }\n        return '#8b5cf6' // purple for clients\n        ;\n    };\n    // Process location data\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MapPage.useEffect\": ()=>{\n            const processLocations = {\n                \"MapPage.useEffect.processLocations\": async ()=>{\n                    const locations = [];\n                    // Add project locations\n                    for (const project of projects || []){\n                        if (project.location && shouldIncludeProject(project)) {\n                            const coordinates = await extractCoordinatesFromLocation(project.location, project.google_maps_link);\n                            locations.push({\n                                id: \"project-\".concat(project.id),\n                                type: 'project',\n                                name: project.name,\n                                location: project.location,\n                                coordinates,\n                                client: project.client,\n                                project,\n                                value: project.total_amount,\n                                status: project.status\n                            });\n                        }\n                    }\n                    // Add schedule locations\n                    for (const schedule of schedules || []){\n                        if (schedule.location && shouldIncludeSchedule(schedule)) {\n                            var _schedule_project, _schedule_project1;\n                            const coordinates = await extractCoordinatesFromLocation(schedule.location, schedule.google_maps_link);\n                            locations.push({\n                                id: \"schedule-\".concat(schedule.id),\n                                type: 'schedule',\n                                name: \"\".concat((_schedule_project = schedule.project) === null || _schedule_project === void 0 ? void 0 : _schedule_project.name, \" - \").concat(schedule.custom_id),\n                                location: schedule.location,\n                                coordinates,\n                                client: (_schedule_project1 = schedule.project) === null || _schedule_project1 === void 0 ? void 0 : _schedule_project1.client,\n                                project: schedule.project,\n                                schedule,\n                                value: schedule.amount,\n                                status: schedule.status,\n                                date: schedule.scheduled_date\n                            });\n                        }\n                    }\n                    setMapLocations(locations);\n                }\n            }[\"MapPage.useEffect.processLocations\"];\n            if (!clientsLoading && !projectsLoading && !schedulesLoading) {\n                processLocations();\n            }\n        }\n    }[\"MapPage.useEffect\"], [\n        clients,\n        projects,\n        schedules,\n        filters,\n        clientsLoading,\n        projectsLoading,\n        schedulesLoading\n    ]);\n    // Helper functions for filtering\n    const shouldIncludeProject = (project)=>{\n        if (filters.clientIds.length > 0 && !filters.clientIds.includes(project.client_id)) return false;\n        if (filters.projectIds.length > 0 && !filters.projectIds.includes(project.id)) return false;\n        if (filters.searchTerm) {\n            var _project_location;\n            const searchLower = filters.searchTerm.toLowerCase();\n            if (!project.name.toLowerCase().includes(searchLower) && !((_project_location = project.location) === null || _project_location === void 0 ? void 0 : _project_location.toLowerCase().includes(searchLower))) return false;\n        }\n        return true;\n    };\n    const shouldIncludeSchedule = (schedule)=>{\n        if (filters.projectIds.length > 0 && !filters.projectIds.includes(schedule.project_id)) return false;\n        if (filters.scheduleIds.length > 0 && !filters.scheduleIds.includes(schedule.id)) return false;\n        if (filters.dateRange.from || filters.dateRange.to) {\n            const scheduleDate = new Date(schedule.scheduled_date);\n            if (filters.dateRange.from && scheduleDate < filters.dateRange.from) return false;\n            if (filters.dateRange.to && scheduleDate > filters.dateRange.to) return false;\n        }\n        if (filters.searchTerm) {\n            var _schedule_location, _schedule_project;\n            const searchLower = filters.searchTerm.toLowerCase();\n            if (!schedule.custom_id.toLowerCase().includes(searchLower) && !((_schedule_location = schedule.location) === null || _schedule_location === void 0 ? void 0 : _schedule_location.toLowerCase().includes(searchLower)) && !((_schedule_project = schedule.project) === null || _schedule_project === void 0 ? void 0 : _schedule_project.name.toLowerCase().includes(searchLower))) return false;\n        }\n        return true;\n    };\n    // Extract coordinates from location string or Google Maps link\n    const extractCoordinatesFromLocation = async (location, mapsLink)=>{\n        if (mapsLink) {\n            try {\n                const locationInfo = await (0,_lib_maps_utils__WEBPACK_IMPORTED_MODULE_9__.extractLocationFromMapsUrl)(mapsLink);\n                if (locationInfo === null || locationInfo === void 0 ? void 0 : locationInfo.coordinates) {\n                    return locationInfo.coordinates;\n                }\n            } catch (error) {\n                console.warn('Failed to extract coordinates from maps link:', error);\n            }\n        }\n        return undefined;\n    };\n    // Show loading state while data is being fetched\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-[600px]\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_Download_ExternalLink_Eye_Filter_FolderOpen_Layers_MapPin_Maximize_RefreshCw_Search_Share_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                        className: \"w-8 h-8 animate-spin mx-auto mb-4 text-muted-foreground\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                        lineNumber: 405,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-muted-foreground\",\n                        children: \"Loading map data...\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                        lineNumber: 406,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                lineNumber: 404,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n            lineNumber: 403,\n            columnNumber: 7\n        }, this);\n    }\n    // Ensure data is available before rendering\n    if (!clients || !projects || !schedules) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-[600px]\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_Download_ExternalLink_Eye_Filter_FolderOpen_Layers_MapPin_Maximize_RefreshCw_Search_Share_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                        className: \"w-8 h-8 mx-auto mb-4 text-muted-foreground\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                        lineNumber: 417,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-muted-foreground\",\n                        children: \"Failed to load data. Please refresh the page.\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                        lineNumber: 418,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                        className: \"mt-4\",\n                        onClick: ()=>window.location.reload(),\n                        children: \"Refresh\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                        lineNumber: 419,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                lineNumber: 416,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n            lineNumber: 415,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-bold\",\n                                children: \"Map & Locations\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                                lineNumber: 432,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-muted-foreground\",\n                                children: \"Visualize clients, projects, and schedules geographically\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                                lineNumber: 433,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                        lineNumber: 431,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: \"outline\",\n                                size: \"sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_Download_ExternalLink_Eye_Filter_FolderOpen_Layers_MapPin_Maximize_RefreshCw_Search_Share_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"w-4 h-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                                        lineNumber: 437,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Export\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                                lineNumber: 436,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: \"outline\",\n                                size: \"sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_Download_ExternalLink_Eye_Filter_FolderOpen_Layers_MapPin_Maximize_RefreshCw_Search_Share_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"w-4 h-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                                        lineNumber: 441,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Layers\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                                lineNumber: 440,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: \"outline\",\n                                size: \"sm\",\n                                onClick: ()=>setIsFullscreen(!isFullscreen),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_Download_ExternalLink_Eye_Filter_FolderOpen_Layers_MapPin_Maximize_RefreshCw_Search_Share_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"w-4 h-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                                        lineNumber: 449,\n                                        columnNumber: 13\n                                    }, this),\n                                    isFullscreen ? 'Exit' : 'Fullscreen'\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                                lineNumber: 444,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                        lineNumber: 435,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                lineNumber: 430,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-5 gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                            className: \"p-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_Download_ExternalLink_Eye_Filter_FolderOpen_Layers_MapPin_Maximize_RefreshCw_Search_Share_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        className: \"w-5 h-5 text-blue-600\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                                        lineNumber: 460,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-muted-foreground\",\n                                                children: \"Total Locations\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                                                lineNumber: 462,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold\",\n                                                children: metrics.totalLocations\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                                                lineNumber: 463,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                                        lineNumber: 461,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                                lineNumber: 459,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                            lineNumber: 458,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                        lineNumber: 457,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                            className: \"p-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_Download_ExternalLink_Eye_Filter_FolderOpen_Layers_MapPin_Maximize_RefreshCw_Search_Share_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        className: \"w-5 h-5 text-green-600\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                                        lineNumber: 472,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-muted-foreground\",\n                                                children: \"Total Value\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                                                lineNumber: 474,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold\",\n                                                children: [\n                                                    \"₹\",\n                                                    (metrics.totalValue / 100000).toFixed(1),\n                                                    \"L\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                                                lineNumber: 475,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                                        lineNumber: 473,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                                lineNumber: 471,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                            lineNumber: 470,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                        lineNumber: 469,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                            className: \"p-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_Download_ExternalLink_Eye_Filter_FolderOpen_Layers_MapPin_Maximize_RefreshCw_Search_Share_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        className: \"w-5 h-5 text-purple-600\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                                        lineNumber: 484,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-muted-foreground\",\n                                                children: \"Clients\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                                                lineNumber: 486,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold\",\n                                                children: metrics.clientsCount\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                                                lineNumber: 487,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                                        lineNumber: 485,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                                lineNumber: 483,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                            lineNumber: 482,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                        lineNumber: 481,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                            className: \"p-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_Download_ExternalLink_Eye_Filter_FolderOpen_Layers_MapPin_Maximize_RefreshCw_Search_Share_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        className: \"w-5 h-5 text-orange-600\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                                        lineNumber: 496,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-muted-foreground\",\n                                                children: \"Projects\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                                                lineNumber: 498,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold\",\n                                                children: metrics.projectsCount\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                                                lineNumber: 499,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                                        lineNumber: 497,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                                lineNumber: 495,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                            lineNumber: 494,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                        lineNumber: 493,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                            className: \"p-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_Download_ExternalLink_Eye_Filter_FolderOpen_Layers_MapPin_Maximize_RefreshCw_Search_Share_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                        className: \"w-5 h-5 text-red-600\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                                        lineNumber: 508,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-muted-foreground\",\n                                                children: \"Schedules\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                                                lineNumber: 510,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold\",\n                                                children: metrics.schedulesCount\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                                                lineNumber: 511,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                                        lineNumber: 509,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                                lineNumber: 507,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                            lineNumber: 506,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                        lineNumber: 505,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                lineNumber: 456,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_Download_ExternalLink_Eye_Filter_FolderOpen_Layers_MapPin_Maximize_RefreshCw_Search_Share_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                    className: \"w-5 h-5\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                                    lineNumber: 522,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"Filters\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                                    lineNumber: 523,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                            lineNumber: 521,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                        lineNumber: 520,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"text-sm font-medium\",\n                                            children: \"Client\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                                            lineNumber: 530,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.Select, {\n                                            value: filters.clientIds.length === 1 ? filters.clientIds[0] : '',\n                                            onValueChange: (value)=>{\n                                                if (value === 'all') {\n                                                    setFilters((prev)=>({\n                                                            ...prev,\n                                                            clientIds: [],\n                                                            projectIds: [],\n                                                            scheduleIds: []\n                                                        }));\n                                                } else {\n                                                    setFilters((prev)=>({\n                                                            ...prev,\n                                                            clientIds: [\n                                                                value\n                                                            ],\n                                                            projectIds: [],\n                                                            scheduleIds: []\n                                                        }));\n                                                }\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectTrigger, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectValue, {\n                                                        placeholder: \"Select client\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                                                        lineNumber: 542,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                                                    lineNumber: 541,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectContent, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                            value: \"all\",\n                                                            children: \"All Clients\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                                                            lineNumber: 545,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        (clients || []).map((client)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                value: client.id,\n                                                                children: client.name\n                                                            }, client.id, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                                                                lineNumber: 547,\n                                                                columnNumber: 21\n                                                            }, this))\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                                                    lineNumber: 544,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                                            lineNumber: 531,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                                    lineNumber: 529,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"text-sm font-medium\",\n                                            children: \"Project\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                                            lineNumber: 557,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.Select, {\n                                            value: filters.projectIds.length === 1 ? filters.projectIds[0] : '',\n                                            onValueChange: (value)=>{\n                                                if (value === 'all') {\n                                                    setFilters((prev)=>({\n                                                            ...prev,\n                                                            projectIds: [],\n                                                            scheduleIds: []\n                                                        }));\n                                                } else {\n                                                    setFilters((prev)=>({\n                                                            ...prev,\n                                                            projectIds: [\n                                                                value\n                                                            ],\n                                                            scheduleIds: []\n                                                        }));\n                                                }\n                                            },\n                                            disabled: filters.clientIds.length === 0,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectTrigger, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectValue, {\n                                                        placeholder: \"Select project\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                                                        lineNumber: 570,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                                                    lineNumber: 569,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectContent, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                            value: \"all\",\n                                                            children: \"All Projects\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                                                            lineNumber: 573,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        filteredProjects.map((project)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                value: project.id,\n                                                                children: project.name\n                                                            }, project.id, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                                                                lineNumber: 575,\n                                                                columnNumber: 21\n                                                            }, this))\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                                                    lineNumber: 572,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                                            lineNumber: 558,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                                    lineNumber: 556,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"text-sm font-medium\",\n                                            children: \"Schedule\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                                            lineNumber: 585,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.Select, {\n                                            value: filters.scheduleIds.length === 1 ? filters.scheduleIds[0] : '',\n                                            onValueChange: (value)=>{\n                                                if (value === 'all') {\n                                                    setFilters((prev)=>({\n                                                            ...prev,\n                                                            scheduleIds: []\n                                                        }));\n                                                } else {\n                                                    setFilters((prev)=>({\n                                                            ...prev,\n                                                            scheduleIds: [\n                                                                value\n                                                            ]\n                                                        }));\n                                                }\n                                            },\n                                            disabled: filters.projectIds.length === 0,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectTrigger, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectValue, {\n                                                        placeholder: \"Select schedule\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                                                        lineNumber: 598,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                                                    lineNumber: 597,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectContent, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                            value: \"all\",\n                                                            children: \"All Schedules\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                                                            lineNumber: 601,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        filteredSchedules.map((schedule)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                value: schedule.id,\n                                                                children: [\n                                                                    schedule.custom_id,\n                                                                    \" - \",\n                                                                    (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_21__.format)(new Date(schedule.scheduled_date), 'MMM dd')\n                                                                ]\n                                                            }, schedule.id, true, {\n                                                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                                                                lineNumber: 603,\n                                                                columnNumber: 21\n                                                            }, this))\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                                                    lineNumber: 600,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                                            lineNumber: 586,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                                    lineNumber: 584,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"text-sm font-medium\",\n                                            children: \"Search\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                                            lineNumber: 613,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_Download_ExternalLink_Eye_Filter_FolderOpen_Layers_MapPin_Maximize_RefreshCw_Search_Share_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                    className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                                                    lineNumber: 615,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                    placeholder: \"Search locations...\",\n                                                    value: filters.searchTerm,\n                                                    onChange: (e)=>setFilters((prev)=>({\n                                                                ...prev,\n                                                                searchTerm: e.target.value\n                                                            })),\n                                                    className: \"pl-10\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                                                    lineNumber: 616,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                                            lineNumber: 614,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                                    lineNumber: 612,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                            lineNumber: 527,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                        lineNumber: 526,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                lineNumber: 519,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-4 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:col-span-3\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_Download_ExternalLink_Eye_Filter_FolderOpen_Layers_MapPin_Maximize_RefreshCw_Search_Share_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        className: \"w-5 h-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                                                        lineNumber: 635,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Map Visualization\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                                                        lineNumber: 636,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                                                lineNumber: 634,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                        variant: mapType === 'markers' ? 'default' : 'outline',\n                                                        size: \"sm\",\n                                                        onClick: ()=>setMapType('markers'),\n                                                        children: \"Markers\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                                                        lineNumber: 639,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                        variant: mapType === 'heatmap' ? 'default' : 'outline',\n                                                        size: \"sm\",\n                                                        onClick: ()=>setMapType('heatmap'),\n                                                        children: \"Heatmap\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                                                        lineNumber: 646,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                                                lineNumber: 638,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                                        lineNumber: 633,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                                    lineNumber: 632,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: [\n                                            mapLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 bg-background/80 backdrop-blur-sm z-10 flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_Download_ExternalLink_Eye_Filter_FolderOpen_Layers_MapPin_Maximize_RefreshCw_Search_Share_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            className: \"w-8 h-8 animate-spin mx-auto mb-2 text-muted-foreground\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                                                            lineNumber: 661,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-muted-foreground\",\n                                                            children: \"Loading Google Maps...\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                                                            lineNumber: 662,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                                                    lineNumber: 660,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                                                lineNumber: 659,\n                                                columnNumber: 19\n                                            }, this),\n                                            mapError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 bg-background/80 backdrop-blur-sm z-10 flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_Download_ExternalLink_Eye_Filter_FolderOpen_Layers_MapPin_Maximize_RefreshCw_Search_Share_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                            className: \"w-8 h-8 mx-auto mb-2 text-destructive\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                                                            lineNumber: 670,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-destructive mb-2\",\n                                                            children: mapError\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                                                            lineNumber: 671,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                            size: \"sm\",\n                                                            onClick: ()=>window.location.reload(),\n                                                            children: \"Retry\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                                                            lineNumber: 672,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                                                    lineNumber: 669,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                                                lineNumber: 668,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                ref: mapRef,\n                                                className: \"w-full h-[600px] rounded-lg border bg-muted\",\n                                                style: {\n                                                    minHeight: '600px'\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                                                lineNumber: 679,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                                        lineNumber: 657,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                                    lineNumber: 656,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                            lineNumber: 631,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                        lineNumber: 630,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                            className: \"text-lg\",\n                                            children: \"Location Details\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                                            lineNumber: 693,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                                        lineNumber: 692,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                        children: selectedLocation ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-semibold text-lg\",\n                                                            children: selectedLocation.name\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                                                            lineNumber: 699,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-muted-foreground flex items-center mt-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_Download_ExternalLink_Eye_Filter_FolderOpen_Layers_MapPin_Maximize_RefreshCw_Search_Share_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                    className: \"w-4 h-4 mr-1\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                                                                    lineNumber: 701,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                selectedLocation.location\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                                                            lineNumber: 700,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                                                    lineNumber: 698,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_7__.Separator, {}, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                                                    lineNumber: 706,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-3\",\n                                                    children: [\n                                                        selectedLocation.client && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"text-sm font-medium text-muted-foreground\",\n                                                                    children: \"Client\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                                                                    lineNumber: 711,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm\",\n                                                                    children: selectedLocation.client.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                                                                    lineNumber: 712,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                                                            lineNumber: 710,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        selectedLocation.project && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"text-sm font-medium text-muted-foreground\",\n                                                                    children: \"Project\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                                                                    lineNumber: 718,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm\",\n                                                                    children: selectedLocation.project.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                                                                    lineNumber: 719,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                                                            lineNumber: 717,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        selectedLocation.schedule && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"text-sm font-medium text-muted-foreground\",\n                                                                    children: \"Schedule\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                                                                    lineNumber: 725,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm\",\n                                                                    children: selectedLocation.schedule.custom_id\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                                                                    lineNumber: 726,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                selectedLocation.date && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-muted-foreground\",\n                                                                    children: (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_21__.format)(new Date(selectedLocation.date), 'PPP')\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                                                                    lineNumber: 728,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                                                            lineNumber: 724,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        selectedLocation.status && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"text-sm font-medium text-muted-foreground\",\n                                                                    children: \"Status\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                                                                    lineNumber: 737,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-2 mt-1\",\n                                                                    children: [\n                                                                        selectedLocation.status === 'completed' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_Download_ExternalLink_Eye_Filter_FolderOpen_Layers_MapPin_Maximize_RefreshCw_Search_Share_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                            className: \"w-4 h-4 text-green-600\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                                                                            lineNumber: 739,\n                                                                            columnNumber: 71\n                                                                        }, this),\n                                                                        selectedLocation.status === 'active' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_Download_ExternalLink_Eye_Filter_FolderOpen_Layers_MapPin_Maximize_RefreshCw_Search_Share_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                            className: \"w-4 h-4 text-blue-600\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                                                                            lineNumber: 740,\n                                                                            columnNumber: 68\n                                                                        }, this),\n                                                                        selectedLocation.status === 'scheduled' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_Download_ExternalLink_Eye_Filter_FolderOpen_Layers_MapPin_Maximize_RefreshCw_Search_Share_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                            className: \"w-4 h-4 text-orange-600\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                                                                            lineNumber: 741,\n                                                                            columnNumber: 71\n                                                                        }, this),\n                                                                        selectedLocation.status === 'cancelled' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_Download_ExternalLink_Eye_Filter_FolderOpen_Layers_MapPin_Maximize_RefreshCw_Search_Share_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                            className: \"w-4 h-4 text-red-600\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                                                                            lineNumber: 742,\n                                                                            columnNumber: 71\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                                                            variant: selectedLocation.status === 'completed' ? 'default' : selectedLocation.status === 'active' ? 'secondary' : selectedLocation.status === 'scheduled' ? 'outline' : 'destructive',\n                                                                            children: selectedLocation.status\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                                                                            lineNumber: 743,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                                                                    lineNumber: 738,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                                                            lineNumber: 736,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        selectedLocation.value && selectedLocation.value > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"text-sm font-medium text-muted-foreground\",\n                                                                    children: \"Value\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                                                                    lineNumber: 756,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm font-semibold\",\n                                                                    children: [\n                                                                        \"₹\",\n                                                                        selectedLocation.value.toLocaleString()\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                                                                    lineNumber: 757,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                                                            lineNumber: 755,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                                                    lineNumber: 708,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_7__.Separator, {}, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                                                    lineNumber: 762,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"text-sm font-medium\",\n                                                            children: \"Quick Actions\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                                                            lineNumber: 766,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-2 gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                    size: \"sm\",\n                                                                    variant: \"outline\",\n                                                                    className: \"text-xs\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_Download_ExternalLink_Eye_Filter_FolderOpen_Layers_MapPin_Maximize_RefreshCw_Search_Share_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                            className: \"w-3 h-3 mr-1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                                                                            lineNumber: 769,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        \"View Details\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                                                                    lineNumber: 768,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                    size: \"sm\",\n                                                                    variant: \"outline\",\n                                                                    className: \"text-xs\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_Download_ExternalLink_Eye_Filter_FolderOpen_Layers_MapPin_Maximize_RefreshCw_Search_Share_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                                            className: \"w-3 h-3 mr-1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                                                                            lineNumber: 773,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        \"Open Maps\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                                                                    lineNumber: 772,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                    size: \"sm\",\n                                                                    variant: \"outline\",\n                                                                    className: \"text-xs\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_Download_ExternalLink_Eye_Filter_FolderOpen_Layers_MapPin_Maximize_RefreshCw_Search_Share_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                                            className: \"w-3 h-3 mr-1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                                                                            lineNumber: 777,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        \"Share\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                                                                    lineNumber: 776,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                    size: \"sm\",\n                                                                    variant: \"outline\",\n                                                                    className: \"text-xs\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_Download_ExternalLink_Eye_Filter_FolderOpen_Layers_MapPin_Maximize_RefreshCw_Search_Share_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                            className: \"w-3 h-3 mr-1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                                                                            lineNumber: 781,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        \"Calendar\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                                                                    lineNumber: 780,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                                                            lineNumber: 767,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                                                    lineNumber: 765,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                                            lineNumber: 697,\n                                            columnNumber: 17\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center py-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_Download_ExternalLink_Eye_Filter_FolderOpen_Layers_MapPin_Maximize_RefreshCw_Search_Share_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    className: \"w-12 h-12 text-muted-foreground mx-auto mb-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                                                    lineNumber: 789,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-muted-foreground\",\n                                                    children: \"Click on a location marker to view details\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                                                    lineNumber: 790,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                                            lineNumber: 788,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                                        lineNumber: 695,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                                lineNumber: 691,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                            className: \"text-lg\",\n                                            children: \"Summary\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                                            lineNumber: 801,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                                        lineNumber: 800,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm text-muted-foreground\",\n                                                            children: \"Visible Locations\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                                                            lineNumber: 806,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm font-medium\",\n                                                            children: mapLocations.length\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                                                            lineNumber: 807,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                                                    lineNumber: 805,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm text-muted-foreground\",\n                                                            children: \"Projects\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                                                            lineNumber: 810,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm font-medium\",\n                                                            children: mapLocations.filter((l)=>l.type === 'project').length\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                                                            lineNumber: 811,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                                                    lineNumber: 809,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm text-muted-foreground\",\n                                                            children: \"Schedules\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                                                            lineNumber: 816,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm font-medium\",\n                                                            children: mapLocations.filter((l)=>l.type === 'schedule').length\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                                                            lineNumber: 817,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                                                    lineNumber: 815,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_7__.Separator, {}, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                                                    lineNumber: 821,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm text-muted-foreground\",\n                                                            children: \"Total Value\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                                                            lineNumber: 823,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm font-semibold\",\n                                                            children: [\n                                                                \"₹\",\n                                                                metrics.totalValue.toLocaleString()\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                                                            lineNumber: 824,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                                                    lineNumber: 822,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                                            lineNumber: 804,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                                        lineNumber: 803,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                                lineNumber: 799,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                        lineNumber: 690,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n                lineNumber: 629,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/(dashboard)/map/page.tsx\",\n        lineNumber: 428,\n        columnNumber: 5\n    }, this);\n}\n_s(MapPage, \"jYR9koXmu8WMqImEuIyuRPDDc7I=\", false, function() {\n    return [\n        _hooks_useApi__WEBPACK_IMPORTED_MODULE_8__.useClients,\n        _hooks_useApi__WEBPACK_IMPORTED_MODULE_8__.useProjects,\n        _hooks_useApi__WEBPACK_IMPORTED_MODULE_8__.useSchedules\n    ];\n});\n_c = MapPage;\nvar _c;\n$RefreshReg$(_c, \"MapPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/(dashboard)/map/page.tsx\n"));

/***/ })

});