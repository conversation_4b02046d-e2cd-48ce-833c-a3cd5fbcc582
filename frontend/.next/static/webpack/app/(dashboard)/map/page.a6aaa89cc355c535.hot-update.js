"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboard)/map/page",{

/***/ "(app-pages-browser)/./src/hooks/useApi.ts":
/*!*****************************!*\
  !*** ./src/hooks/useApi.ts ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useApi: () => (/* binding */ useApi),\n/* harmony export */   useClient: () => (/* binding */ useClient),\n/* harmony export */   useClients: () => (/* binding */ useClients),\n/* harmony export */   useContactPersons: () => (/* binding */ useContactPersons),\n/* harmony export */   useCreateClient: () => (/* binding */ useCreateClient),\n/* harmony export */   useCreateExpense: () => (/* binding */ useCreateExpense),\n/* harmony export */   useCreatePayment: () => (/* binding */ useCreatePayment),\n/* harmony export */   useCreateProject: () => (/* binding */ useCreateProject),\n/* harmony export */   useCreateSchedule: () => (/* binding */ useCreateSchedule),\n/* harmony export */   useCreateShoot: () => (/* binding */ useCreateShoot),\n/* harmony export */   useCreateTask: () => (/* binding */ useCreateTask),\n/* harmony export */   useDashboardStats: () => (/* binding */ useDashboardStats),\n/* harmony export */   useDeleteClient: () => (/* binding */ useDeleteClient),\n/* harmony export */   useDeleteExpense: () => (/* binding */ useDeleteExpense),\n/* harmony export */   useDeletePayment: () => (/* binding */ useDeletePayment),\n/* harmony export */   useDeleteProject: () => (/* binding */ useDeleteProject),\n/* harmony export */   useDeleteSchedule: () => (/* binding */ useDeleteSchedule),\n/* harmony export */   useDeleteShoot: () => (/* binding */ useDeleteShoot),\n/* harmony export */   useDeleteTask: () => (/* binding */ useDeleteTask),\n/* harmony export */   useExpenses: () => (/* binding */ useExpenses),\n/* harmony export */   useMyTasks: () => (/* binding */ useMyTasks),\n/* harmony export */   usePayments: () => (/* binding */ usePayments),\n/* harmony export */   useProject: () => (/* binding */ useProject),\n/* harmony export */   useProjects: () => (/* binding */ useProjects),\n/* harmony export */   useSchedule: () => (/* binding */ useSchedule),\n/* harmony export */   useSchedules: () => (/* binding */ useSchedules),\n/* harmony export */   useShoot: () => (/* binding */ useShoot),\n/* harmony export */   useShoots: () => (/* binding */ useShoots),\n/* harmony export */   useTasks: () => (/* binding */ useTasks),\n/* harmony export */   useUpcomingSchedules: () => (/* binding */ useUpcomingSchedules),\n/* harmony export */   useUpcomingShoots: () => (/* binding */ useUpcomingShoots),\n/* harmony export */   useUpdateClient: () => (/* binding */ useUpdateClient),\n/* harmony export */   useUpdateExpense: () => (/* binding */ useUpdateExpense),\n/* harmony export */   useUpdatePayment: () => (/* binding */ useUpdatePayment),\n/* harmony export */   useUpdateProject: () => (/* binding */ useUpdateProject),\n/* harmony export */   useUpdateSchedule: () => (/* binding */ useUpdateSchedule),\n/* harmony export */   useUpdateShoot: () => (/* binding */ useUpdateShoot),\n/* harmony export */   useUpdateTask: () => (/* binding */ useUpdateTask),\n/* harmony export */   useUsers: () => (/* binding */ useUsers),\n/* harmony export */   useUsersByRole: () => (/* binding */ useUsersByRole)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n\n\n// Common error message extractor\nfunction getErrorMessage(error, fallback) {\n    if (error instanceof Error) return error.message;\n    if (typeof error === 'string') return error;\n    try {\n        return JSON.stringify(error);\n    } catch (e) {\n        return fallback;\n    }\n}\n// Generic hook for API calls with fallback data\nfunction useApiCall(apiCall) {\n    let dependencies = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : [], fallbackData = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : null;\n    const [data, setData] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(fallbackData);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const refetch = async ()=>{\n        try {\n            setLoading(true);\n            setError(null);\n            const result = await apiCall();\n            setData(result);\n        } catch (error) {\n            const message = getErrorMessage(error, 'API call failed, using fallback data');\n            console.warn('API call failed, using fallback data:', message);\n            setData(fallbackData);\n            setError(null); // Don't show error if we have fallback data\n        } finally{\n            setLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useApiCall.useEffect\": ()=>{\n            refetch();\n        }\n    }[\"useApiCall.useEffect\"], dependencies);\n    return {\n        data,\n        loading,\n        error,\n        refetch\n    };\n}\n// Generic hook for API calls with URL\nfunction useApi(url) {\n    const [data, setData] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const refetch = async ()=>{\n        try {\n            setLoading(true);\n            setError(null);\n            const response = await fetch(url);\n            if (!response.ok) {\n                throw new Error(\"HTTP error! status: \".concat(response.status));\n            }\n            const result = await response.json();\n            setData(result);\n        } catch (error) {\n            setError(getErrorMessage(error, 'An error occurred'));\n        } finally{\n            setLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useApi.useEffect\": ()=>{\n            refetch();\n        }\n    }[\"useApi.useEffect\"], [\n        url\n    ]);\n    return {\n        data,\n        loading,\n        error,\n        refetch\n    };\n}\n// Users hooks\nfunction useUsers() {\n    return useApiCall({\n        \"useUsers.useApiCall\": ()=>_lib_api__WEBPACK_IMPORTED_MODULE_1__.usersApi.getAll()\n    }[\"useUsers.useApiCall\"]);\n}\nfunction useUsersByRole(role) {\n    return useApiCall({\n        \"useUsersByRole.useApiCall\": ()=>_lib_api__WEBPACK_IMPORTED_MODULE_1__.usersApi.getByRole(role)\n    }[\"useUsersByRole.useApiCall\"], [\n        role\n    ]);\n}\n// Clients hooks\nfunction useClients() {\n    return useApiCall({\n        \"useClients.useApiCall\": ()=>_lib_api__WEBPACK_IMPORTED_MODULE_1__.clientsApi.getAll()\n    }[\"useClients.useApiCall\"], [], []);\n}\nfunction useClient(id) {\n    return useApiCall({\n        \"useClient.useApiCall\": ()=>_lib_api__WEBPACK_IMPORTED_MODULE_1__.clientsApi.getById(id)\n    }[\"useClient.useApiCall\"], [\n        id\n    ]);\n}\n// Contact Persons hooks\nfunction useContactPersons(clientId) {\n    return useApiCall({\n        \"useContactPersons.useApiCall\": ()=>_lib_api__WEBPACK_IMPORTED_MODULE_1__.contactPersonsApi.getByClientId(clientId)\n    }[\"useContactPersons.useApiCall\"], [\n        clientId\n    ]);\n}\n// Projects hooks\nfunction useProjects() {\n    return useApiCall({\n        \"useProjects.useApiCall\": ()=>_lib_api__WEBPACK_IMPORTED_MODULE_1__.projectsApi.getAll()\n    }[\"useProjects.useApiCall\"], [], []);\n}\nfunction useProject(id) {\n    return useApiCall({\n        \"useProject.useApiCall\": ()=>_lib_api__WEBPACK_IMPORTED_MODULE_1__.projectsApi.getById(id)\n    }[\"useProject.useApiCall\"], [\n        id\n    ]);\n}\n// Schedules hooks\nfunction useSchedules() {\n    return useApiCall({\n        \"useSchedules.useApiCall\": ()=>_lib_api__WEBPACK_IMPORTED_MODULE_1__.schedulesApi.getAll()\n    }[\"useSchedules.useApiCall\"], [], []);\n}\nfunction useSchedule(id) {\n    return useApiCall({\n        \"useSchedule.useApiCall\": ()=>_lib_api__WEBPACK_IMPORTED_MODULE_1__.schedulesApi.getById(id)\n    }[\"useSchedule.useApiCall\"], [\n        id\n    ]);\n}\nfunction useUpcomingSchedules() {\n    return useApiCall({\n        \"useUpcomingSchedules.useApiCall\": ()=>_lib_api__WEBPACK_IMPORTED_MODULE_1__.schedulesApi.getUpcoming()\n    }[\"useUpcomingSchedules.useApiCall\"], [], []);\n}\n// Shoots hooks (backward compatibility)\nfunction useShoots() {\n    return useApiCall({\n        \"useShoots.useApiCall\": ()=>_lib_api__WEBPACK_IMPORTED_MODULE_1__.shootsApi.getAll()\n    }[\"useShoots.useApiCall\"]);\n}\nfunction useShoot(id) {\n    return useApiCall({\n        \"useShoot.useApiCall\": ()=>_lib_api__WEBPACK_IMPORTED_MODULE_1__.shootsApi.getById(id)\n    }[\"useShoot.useApiCall\"], [\n        id\n    ]);\n}\nfunction useUpcomingShoots() {\n    return useApiCall({\n        \"useUpcomingShoots.useApiCall\": ()=>_lib_api__WEBPACK_IMPORTED_MODULE_1__.shootsApi.getUpcoming()\n    }[\"useUpcomingShoots.useApiCall\"]);\n}\n// Tasks hooks\nfunction useTasks() {\n    return useApiCall({\n        \"useTasks.useApiCall\": ()=>_lib_api__WEBPACK_IMPORTED_MODULE_1__.tasksApi.getAll()\n    }[\"useTasks.useApiCall\"], [], []);\n}\nfunction useMyTasks() {\n    return useApiCall({\n        \"useMyTasks.useApiCall\": ()=>_lib_api__WEBPACK_IMPORTED_MODULE_1__.tasksApi.getMyTasks()\n    }[\"useMyTasks.useApiCall\"]);\n}\n// Dashboard hooks\nfunction useDashboardStats() {\n    return useApiCall({\n        \"useDashboardStats.useApiCall\": ()=>_lib_api__WEBPACK_IMPORTED_MODULE_1__.dashboardApi.getStats()\n    }[\"useDashboardStats.useApiCall\"]);\n}\n// Mutation hooks for create/update/delete operations\nfunction useCreateClient() {\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const createClient = async (clientData)=>{\n        try {\n            setLoading(true);\n            setError(null);\n            const result = await _lib_api__WEBPACK_IMPORTED_MODULE_1__.clientsApi.create(clientData);\n            return result;\n        } catch (error) {\n            setError(getErrorMessage(error, 'Failed to create client'));\n            throw error;\n        } finally{\n            setLoading(false);\n        }\n    };\n    return {\n        createClient,\n        loading,\n        error\n    };\n}\nfunction useUpdateClient() {\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const updateClient = async (id, updates)=>{\n        try {\n            setLoading(true);\n            setError(null);\n            const result = await _lib_api__WEBPACK_IMPORTED_MODULE_1__.clientsApi.update(id, updates);\n            return result;\n        } catch (error) {\n            setError(getErrorMessage(error, 'Failed to update client'));\n            throw error;\n        } finally{\n            setLoading(false);\n        }\n    };\n    return {\n        updateClient,\n        loading,\n        error\n    };\n}\nfunction useDeleteClient() {\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const deleteClient = async (id)=>{\n        try {\n            setLoading(true);\n            setError(null);\n            await _lib_api__WEBPACK_IMPORTED_MODULE_1__.clientsApi.delete(id);\n        } catch (error) {\n            setError(getErrorMessage(error, 'Failed to delete client'));\n            throw error;\n        } finally{\n            setLoading(false);\n        }\n    };\n    return {\n        deleteClient,\n        loading,\n        error\n    };\n}\nfunction useCreateProject() {\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const createProject = async (projectData)=>{\n        try {\n            setLoading(true);\n            setError(null);\n            const result = await _lib_api__WEBPACK_IMPORTED_MODULE_1__.projectsApi.create(projectData);\n            return result;\n        } catch (error) {\n            setError(getErrorMessage(error, 'Failed to create project'));\n            throw error;\n        } finally{\n            setLoading(false);\n        }\n    };\n    return {\n        createProject,\n        loading,\n        error\n    };\n}\nfunction useUpdateProject() {\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const updateProject = async (id, updates)=>{\n        try {\n            setLoading(true);\n            setError(null);\n            const result = await _lib_api__WEBPACK_IMPORTED_MODULE_1__.projectsApi.update(id, updates);\n            return result;\n        } catch (error) {\n            setError(getErrorMessage(error, 'Failed to update project'));\n            throw error;\n        } finally{\n            setLoading(false);\n        }\n    };\n    return {\n        updateProject,\n        loading,\n        error\n    };\n}\nfunction useCreateSchedule() {\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const createScheduleWithVendors = async (scheduleData, vendors)=>{\n        try {\n            setLoading(true);\n            setError(null);\n            // Call the server-side API endpoint instead of client-side method\n            const response = await fetch('/api/schedules', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    schedule: scheduleData,\n                    vendors: vendors\n                })\n            });\n            if (!response.ok) {\n                const errorData = await response.json();\n                throw new Error(errorData.error || 'Failed to create schedule');\n            }\n            const result = await response.json();\n            return result;\n        } catch (error) {\n            setError(getErrorMessage(error, 'Failed to create schedule'));\n            throw error;\n        } finally{\n            setLoading(false);\n        }\n    };\n    return {\n        createScheduleWithVendors,\n        loading,\n        error\n    };\n}\n// Keep for backward compatibility\nfunction useCreateShoot() {\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const createShoot = async (shootData)=>{\n        try {\n            setLoading(true);\n            setError(null);\n            const result = await _lib_api__WEBPACK_IMPORTED_MODULE_1__.shootsApi.create(shootData);\n            return result;\n        } catch (error) {\n            setError(getErrorMessage(error, 'Failed to create shoot'));\n            throw error;\n        } finally{\n            setLoading(false);\n        }\n    };\n    return {\n        createShoot,\n        loading,\n        error\n    };\n}\nfunction useUpdateSchedule() {\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const updateSchedule = async (id, updates)=>{\n        try {\n            setLoading(true);\n            setError(null);\n            const result = await _lib_api__WEBPACK_IMPORTED_MODULE_1__.schedulesApi.update(id, updates);\n            return result;\n        } catch (error) {\n            setError(getErrorMessage(error, 'Failed to update schedule'));\n            throw error;\n        } finally{\n            setLoading(false);\n        }\n    };\n    const updateScheduleWithVendors = async (id, updates, vendors)=>{\n        try {\n            setLoading(true);\n            setError(null);\n            const result = await _lib_api__WEBPACK_IMPORTED_MODULE_1__.schedulesApi.updateWithVendors(id, updates, vendors);\n            return result;\n        } catch (error) {\n            setError(getErrorMessage(error, 'Failed to update schedule'));\n            throw error;\n        } finally{\n            setLoading(false);\n        }\n    };\n    return {\n        updateSchedule,\n        updateScheduleWithVendors,\n        loading,\n        error\n    };\n}\n// Keep for backward compatibility\nfunction useUpdateShoot() {\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const updateShoot = async (id, updates)=>{\n        try {\n            setLoading(true);\n            setError(null);\n            const result = await _lib_api__WEBPACK_IMPORTED_MODULE_1__.shootsApi.update(id, updates);\n            return result;\n        } catch (error) {\n            setError(getErrorMessage(error, 'Failed to update shoot'));\n            throw error;\n        } finally{\n            setLoading(false);\n        }\n    };\n    return {\n        updateShoot,\n        loading,\n        error\n    };\n}\nfunction useCreateExpense() {\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const createExpense = async (expenseData)=>{\n        try {\n            setLoading(true);\n            setError(null);\n            const result = await _lib_api__WEBPACK_IMPORTED_MODULE_1__.expensesApi.create(expenseData);\n            return result;\n        } catch (error) {\n            setError(getErrorMessage(error, 'Failed to create expense'));\n            throw error;\n        } finally{\n            setLoading(false);\n        }\n    };\n    return {\n        createExpense,\n        loading,\n        error\n    };\n}\nfunction useCreateTask() {\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const createTask = async (taskData)=>{\n        try {\n            setLoading(true);\n            setError(null);\n            const result = await _lib_api__WEBPACK_IMPORTED_MODULE_1__.tasksApi.create(taskData);\n            return result;\n        } catch (error) {\n            setError(getErrorMessage(error, 'Failed to create task'));\n            throw error;\n        } finally{\n            setLoading(false);\n        }\n    };\n    return {\n        createTask,\n        loading,\n        error\n    };\n}\nfunction useUpdateTask() {\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const updateTask = async (id, updates)=>{\n        try {\n            setLoading(true);\n            setError(null);\n            const result = await _lib_api__WEBPACK_IMPORTED_MODULE_1__.tasksApi.update(id, updates);\n            return result;\n        } catch (error) {\n            setError(getErrorMessage(error, 'Failed to update task'));\n            throw error;\n        } finally{\n            setLoading(false);\n        }\n    };\n    return {\n        updateTask,\n        loading,\n        error\n    };\n}\nfunction usePayments() {\n    const [data, setData] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const fetchPayments = async ()=>{\n        try {\n            setLoading(true);\n            setError(null);\n            const result = await _lib_api__WEBPACK_IMPORTED_MODULE_1__.paymentsApi.getAll();\n            setData(result);\n        } catch (error) {\n            console.warn('Payments API failed, using empty array:', getErrorMessage(error, 'Unknown error'));\n            setData([]);\n            setError(null);\n        } finally{\n            setLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"usePayments.useEffect\": ()=>{\n            fetchPayments();\n        }\n    }[\"usePayments.useEffect\"], []);\n    return {\n        data,\n        loading,\n        error,\n        refetch: fetchPayments\n    };\n}\nfunction useExpenses() {\n    const [data, setData] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const fetchExpenses = async ()=>{\n        try {\n            setLoading(true);\n            setError(null);\n            const result = await _lib_api__WEBPACK_IMPORTED_MODULE_1__.expensesApi.getAll();\n            setData(result);\n        } catch (error) {\n            console.warn('Expenses API failed, using empty array:', getErrorMessage(error, 'Unknown error'));\n            setData([]);\n            setError(null);\n        } finally{\n            setLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useExpenses.useEffect\": ()=>{\n            fetchExpenses();\n        }\n    }[\"useExpenses.useEffect\"], []);\n    return {\n        data,\n        loading,\n        error,\n        refetch: fetchExpenses\n    };\n}\nfunction useCreatePayment() {\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const createPayment = async (paymentData)=>{\n        try {\n            setLoading(true);\n            setError(null);\n            const result = await _lib_api__WEBPACK_IMPORTED_MODULE_1__.paymentsApi.create(paymentData);\n            return result;\n        } catch (error) {\n            setError(getErrorMessage(error, 'Failed to create payment'));\n            throw error;\n        } finally{\n            setLoading(false);\n        }\n    };\n    return {\n        createPayment,\n        loading,\n        error\n    };\n}\nfunction useUpdatePayment() {\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const updatePayment = async (id, updates)=>{\n        try {\n            setLoading(true);\n            setError(null);\n            const result = await _lib_api__WEBPACK_IMPORTED_MODULE_1__.paymentsApi.update(id, updates);\n            return result;\n        } catch (error) {\n            setError(getErrorMessage(error, 'Failed to update payment'));\n            throw error;\n        } finally{\n            setLoading(false);\n        }\n    };\n    return {\n        updatePayment,\n        loading,\n        error\n    };\n}\nfunction useUpdateExpense() {\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const updateExpense = async (id, updates)=>{\n        try {\n            setLoading(true);\n            setError(null);\n            const result = await _lib_api__WEBPACK_IMPORTED_MODULE_1__.expensesApi.update(id, updates);\n            return result;\n        } catch (error) {\n            setError(getErrorMessage(error, 'Failed to update expense'));\n            throw error;\n        } finally{\n            setLoading(false);\n        }\n    };\n    return {\n        updateExpense,\n        loading,\n        error\n    };\n}\nfunction useDeletePayment() {\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const deletePayment = async (id)=>{\n        try {\n            setLoading(true);\n            setError(null);\n            await _lib_api__WEBPACK_IMPORTED_MODULE_1__.paymentsApi.delete(id);\n        } catch (error) {\n            setError(getErrorMessage(error, 'Failed to delete payment'));\n            throw error;\n        } finally{\n            setLoading(false);\n        }\n    };\n    return {\n        deletePayment,\n        loading,\n        error\n    };\n}\nfunction useDeleteExpense() {\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const deleteExpense = async (id)=>{\n        try {\n            setLoading(true);\n            setError(null);\n            await _lib_api__WEBPACK_IMPORTED_MODULE_1__.expensesApi.delete(id);\n        } catch (error) {\n            setError(getErrorMessage(error, 'Failed to delete expense'));\n            throw error;\n        } finally{\n            setLoading(false);\n        }\n    };\n    return {\n        deleteExpense,\n        loading,\n        error\n    };\n}\nfunction useDeleteProject() {\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const deleteProject = async (id)=>{\n        try {\n            setLoading(true);\n            setError(null);\n            await _lib_api__WEBPACK_IMPORTED_MODULE_1__.projectsApi.delete(id);\n        } catch (error) {\n            setError(getErrorMessage(error, 'Failed to delete project'));\n            throw error;\n        } finally{\n            setLoading(false);\n        }\n    };\n    return {\n        deleteProject,\n        loading,\n        error\n    };\n}\nfunction useDeleteSchedule() {\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const deleteSchedule = async (id)=>{\n        try {\n            setLoading(true);\n            setError(null);\n            await _lib_api__WEBPACK_IMPORTED_MODULE_1__.schedulesApi.delete(id);\n        } catch (error) {\n            setError(getErrorMessage(error, 'Failed to delete schedule'));\n            throw error;\n        } finally{\n            setLoading(false);\n        }\n    };\n    return {\n        deleteSchedule,\n        loading,\n        error\n    };\n}\n// Keep for backward compatibility\nfunction useDeleteShoot() {\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const deleteShoot = async (id)=>{\n        try {\n            setLoading(true);\n            setError(null);\n            await _lib_api__WEBPACK_IMPORTED_MODULE_1__.shootsApi.delete(id);\n        } catch (error) {\n            setError(getErrorMessage(error, 'Failed to delete shoot'));\n            throw error;\n        } finally{\n            setLoading(false);\n        }\n    };\n    return {\n        deleteShoot,\n        loading,\n        error\n    };\n}\nfunction useDeleteTask() {\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const deleteTask = async (id)=>{\n        try {\n            setLoading(true);\n            setError(null);\n            await _lib_api__WEBPACK_IMPORTED_MODULE_1__.tasksApi.delete(id);\n        } catch (error) {\n            setError(getErrorMessage(error, 'Failed to delete task'));\n            throw error;\n        } finally{\n            setLoading(false);\n        }\n    };\n    return {\n        deleteTask,\n        loading,\n        error\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useApi.ts\n"));

/***/ })

});