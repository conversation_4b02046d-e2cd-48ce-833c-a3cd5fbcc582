"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_src_lib_background-jobs_ts"],{

/***/ "(app-pages-browser)/./src/lib/background-jobs.ts":
/*!************************************!*\
  !*** ./src/lib/background-jobs.ts ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getAllJobs: () => (/* binding */ getAllJobs),\n/* harmony export */   getBackgroundJobQueue: () => (/* binding */ getBackgroundJobQueue),\n/* harmony export */   getJobStatus: () => (/* binding */ getJobStatus),\n/* harmony export */   queueBackgroundJob: () => (/* binding */ queueBackgroundJob),\n/* harmony export */   queueSharePointFolderCreation: () => (/* binding */ queueSharePointFolderCreation)\n/* harmony export */ });\n/**\n * Background job system for handling asynchronous tasks like SharePoint folder creation\n */ class BackgroundJobQueue {\n    /**\n   * Add a new job to the queue\n   */ addJob(type, entityType, entityId, payload) {\n        const jobId = \"\".concat(type, \"_\").concat(entityType, \"_\").concat(entityId, \"_\").concat(Date.now());\n        const job = {\n            id: jobId,\n            type,\n            entityType,\n            entityId,\n            payload,\n            status: 'pending',\n            attempts: 0,\n            maxAttempts: 3,\n            createdAt: new Date(),\n            updatedAt: new Date()\n        };\n        this.jobs.set(jobId, job);\n        console.log(\"\\uD83D\\uDCCB Background job added: \".concat(jobId, \" (\").concat(type, \" for \").concat(entityType, \":\").concat(entityId, \")\"));\n        console.log(\"\\uD83D\\uDCCA Total jobs in queue: \".concat(this.jobs.size));\n        return jobId;\n    }\n    /**\n   * Start processing jobs in the background\n   */ startProcessing() {\n        if (this.processingInterval) return;\n        console.log('🚀 Starting background job processing...');\n        // Re-enable background job processing with optimized interval\n        this.processingInterval = setInterval(async ()=>{\n            if (this.isProcessing) return;\n            await this.processNextJob();\n        }, 10000); // Process every 10 seconds to reduce CPU load\n    }\n    /**\n   * Stop processing jobs\n   */ stopProcessing() {\n        if (this.processingInterval) {\n            clearInterval(this.processingInterval);\n            this.processingInterval = null;\n        }\n    }\n    /**\n   * Process the next pending job\n   */ async processNextJob() {\n        const pendingJob = Array.from(this.jobs.values()).find((job)=>job.status === 'pending' && job.attempts < job.maxAttempts);\n        if (!pendingJob) return;\n        this.isProcessing = true;\n        pendingJob.status = 'processing';\n        pendingJob.attempts++;\n        pendingJob.updatedAt = new Date();\n        console.log(\"\\uD83D\\uDD04 Processing background job: \".concat(pendingJob.id, \" (attempt \").concat(pendingJob.attempts, \"/\").concat(pendingJob.maxAttempts, \")\"));\n        try {\n            await this.executeJob(pendingJob);\n            pendingJob.status = 'completed';\n            pendingJob.updatedAt = new Date();\n            console.log(\"✅ Background job completed: \".concat(pendingJob.id));\n        } catch (error) {\n            console.error(\"❌ Background job failed: \".concat(pendingJob.id), error);\n            pendingJob.error = error instanceof Error ? error.message : 'Unknown error';\n            pendingJob.updatedAt = new Date();\n            if (pendingJob.attempts >= pendingJob.maxAttempts) {\n                pendingJob.status = 'failed';\n                console.error(\"\\uD83D\\uDC80 Background job permanently failed after \".concat(pendingJob.maxAttempts, \" attempts: \").concat(pendingJob.id));\n            } else {\n                pendingJob.status = 'pending'; // Retry\n                console.log(\"\\uD83D\\uDD04 Background job will retry: \".concat(pendingJob.id));\n            }\n        } finally{\n            this.isProcessing = false;\n        }\n    }\n    /**\n   * Execute a specific job based on its type\n   */ async executeJob(job) {\n        switch(job.type){\n            case 'sharepoint_folder_creation':\n                await this.executeSharePointFolderCreation(job);\n                break;\n            case 'heatmap_automation':\n                await this.executeHeatmapAutomation(job);\n                break;\n            case 'notification_triggers':\n                await this.executeNotificationTriggers(job);\n                break;\n            default:\n                throw new Error(\"Unknown job type: \".concat(job.type));\n        }\n    }\n    /**\n   * Execute SharePoint folder creation job\n   */ async executeSharePointFolderCreation(job) {\n        const { entityType, entityId, payload } = job;\n        switch(entityType){\n            case 'client':\n                await this.createClientFolder(entityId, payload);\n                break;\n            case 'project':\n                await this.createProjectFolder(entityId, payload);\n                break;\n            case 'schedule':\n                await this.createScheduleFolder(entityId, payload);\n                break;\n            default:\n                throw new Error(\"Unknown entity type: \".concat(entityType));\n        }\n    }\n    /**\n   * Create SharePoint folder for client\n   */ async createClientFolder(clientId, payload) {\n        const { createClientFolder } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_src_lib_microsoft-graph_ts\").then(__webpack_require__.bind(__webpack_require__, /*! @/lib/microsoft-graph */ \"(app-pages-browser)/./src/lib/microsoft-graph.ts\"));\n        await createClientFolder(clientId, payload.customId, payload.name);\n    }\n    /**\n   * Create SharePoint folder for project\n   */ async createProjectFolder(projectId, payload) {\n        const { createProjectFolder } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_src_lib_microsoft-graph_ts\").then(__webpack_require__.bind(__webpack_require__, /*! @/lib/microsoft-graph */ \"(app-pages-browser)/./src/lib/microsoft-graph.ts\"));\n        await createProjectFolder(projectId, payload.customId, payload.name, payload.clientId);\n    }\n    /**\n   * Create SharePoint folder for schedule\n   */ async createScheduleFolder(scheduleId, payload) {\n        console.log(\"⚡️ Dynamically importing SharePointService for schedule: \".concat(scheduleId));\n        const { SharePointService } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_src_lib_sharepoint-service_ts\").then(__webpack_require__.bind(__webpack_require__, /*! @/lib/sharepoint-service */ \"(app-pages-browser)/./src/lib/sharepoint-service.ts\"));\n        console.log(\"\\uD83D\\uDCDE Calling SharePointService.ensureScheduleFolder for schedule: \".concat(scheduleId));\n        const result = await SharePointService.ensureScheduleFolder(scheduleId);\n        console.log(\"✔️ SharePointService.ensureScheduleFolder result for schedule \".concat(scheduleId, \":\"), result);\n    }\n    /**\n   * Execute heatmap automation job\n   */ async executeHeatmapAutomation(job) {\n        const { entityId } = job;\n        console.log(\"\\uD83D\\uDDFA️ Executing heatmap automation for schedule: \".concat(entityId));\n        const { processHeatmapAutomation } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_src_lib_heatmap-automation_ts\").then(__webpack_require__.bind(__webpack_require__, /*! @/lib/heatmap-automation */ \"(app-pages-browser)/./src/lib/heatmap-automation.ts\"));\n        const result = await processHeatmapAutomation(entityId);\n        if (!result.success) {\n            throw new Error(result.message);\n        }\n        console.log(\"✅ Heatmap automation completed for schedule: \".concat(entityId));\n    }\n    /**\n   * Execute notification triggers job\n   */ async executeNotificationTriggers(job) {\n        const { payload } = job;\n        console.log(\"\\uD83D\\uDD14 Executing notification triggers: \".concat(payload.triggerType));\n        const { processNotificationTriggers } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_src_lib_notification-triggers_ts\").then(__webpack_require__.bind(__webpack_require__, /*! @/lib/notification-triggers */ \"(app-pages-browser)/./src/lib/notification-triggers.ts\"));\n        const result = await processNotificationTriggers(payload.triggerType, payload.options);\n        if (!result.success) {\n            throw new Error(result.message);\n        }\n        console.log(\"✅ Notification triggers completed: \".concat(payload.triggerType, \", created \").concat(result.count, \" notifications\"));\n    }\n    /**\n   * Get job status\n   */ getJobStatus(jobId) {\n        return this.jobs.get(jobId);\n    }\n    /**\n   * Get all jobs for debugging\n   */ getAllJobs() {\n        return Array.from(this.jobs.values());\n    }\n    /**\n   * Clean up completed and failed jobs older than 1 hour\n   */ cleanup() {\n        const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);\n        for (const [jobId, job] of this.jobs.entries()){\n            if ((job.status === 'completed' || job.status === 'failed') && job.updatedAt < oneHourAgo) {\n                this.jobs.delete(jobId);\n                console.log(\"\\uD83E\\uDDF9 Cleaned up old job: \".concat(jobId));\n            }\n        }\n    }\n    constructor(){\n        this.jobs = new Map();\n        this.isProcessing = false;\n        this.processingInterval = null;\n        console.log('🏗️ BackgroundJobQueue constructor called');\n        // Start processing jobs every 2 seconds\n        this.startProcessing();\n        console.log('✅ BackgroundJobQueue initialized and processing started');\n    }\n}\n// Global instance\nlet backgroundJobQueue = null;\n/**\n * Get the global background job queue instance\n */ function getBackgroundJobQueue() {\n    if (!backgroundJobQueue) {\n        console.log('🚀 Creating new BackgroundJobQueue instance');\n        backgroundJobQueue = new BackgroundJobQueue();\n        // Clean up old jobs every 30 minutes\n        setInterval(()=>{\n            backgroundJobQueue === null || backgroundJobQueue === void 0 ? void 0 : backgroundJobQueue.cleanup();\n        }, 30 * 60 * 1000);\n        console.log('🎯 BackgroundJobQueue instance created and cleanup scheduled');\n    }\n    return backgroundJobQueue;\n}\n/**\n * Add a SharePoint folder creation job to the background queue\n * @param entityType - Type of entity (client, project, schedule)\n * @param entityId - ID of the entity\n * @param payload - Job payload\n * @param options - Additional options\n * @returns Job ID or 'sync' if executed synchronously\n */ async function queueSharePointFolderCreation(entityType, entityId, payload) {\n    let options = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : {};\n    const { executeSync = false, fallbackToSync = true } = options;\n    // If sync execution is requested, execute immediately\n    if (executeSync) {\n        console.log(\"\\uD83D\\uDD04 Executing SharePoint folder creation synchronously for \".concat(entityType, \":\").concat(entityId));\n        try {\n            await executeSharePointFolderCreationDirect(entityType, entityId, payload);\n            console.log(\"✅ Synchronous SharePoint folder creation completed for \".concat(entityType, \":\").concat(entityId));\n            return 'sync';\n        } catch (error) {\n            console.error(\"❌ Synchronous SharePoint folder creation failed for \".concat(entityType, \":\").concat(entityId, \":\"), error);\n            throw error;\n        }\n    }\n    // Try to queue the job\n    try {\n        const queue = getBackgroundJobQueue();\n        const jobId = queue.addJob('sharepoint_folder_creation', entityType, entityId, payload);\n        console.log(\"\\uD83D\\uDCCB SharePoint folder creation queued for \".concat(entityType, \":\").concat(entityId, \" with job ID: \").concat(jobId));\n        return jobId;\n    } catch (queueError) {\n        console.error(\"❌ Failed to queue SharePoint folder creation for \".concat(entityType, \":\").concat(entityId, \":\"), queueError);\n        // Fall back to synchronous execution if queuing fails and fallback is enabled\n        if (fallbackToSync) {\n            console.log(\"\\uD83D\\uDD04 Falling back to synchronous execution for \".concat(entityType, \":\").concat(entityId));\n            try {\n                await executeSharePointFolderCreationDirect(entityType, entityId, payload);\n                console.log(\"✅ Fallback synchronous SharePoint folder creation completed for \".concat(entityType, \":\").concat(entityId));\n                return 'sync-fallback';\n            } catch (syncError) {\n                console.error(\"❌ Fallback synchronous SharePoint folder creation failed for \".concat(entityType, \":\").concat(entityId, \":\"), syncError);\n                throw syncError;\n            }\n        }\n        throw queueError;\n    }\n}\n/**\n * Execute SharePoint folder creation directly (synchronously)\n */ async function executeSharePointFolderCreationDirect(entityType, entityId, payload) {\n    switch(entityType){\n        case 'client':\n            const { createClientFolder } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_src_lib_microsoft-graph_ts\").then(__webpack_require__.bind(__webpack_require__, /*! @/lib/microsoft-graph */ \"(app-pages-browser)/./src/lib/microsoft-graph.ts\"));\n            await createClientFolder(entityId, payload.customId, payload.name);\n            break;\n        case 'project':\n            const { createProjectFolder } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_src_lib_microsoft-graph_ts\").then(__webpack_require__.bind(__webpack_require__, /*! @/lib/microsoft-graph */ \"(app-pages-browser)/./src/lib/microsoft-graph.ts\"));\n            await createProjectFolder(entityId, payload.customId, payload.name, payload.clientId);\n            break;\n        case 'schedule':\n            const { SharePointService } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_src_lib_sharepoint-service_ts\").then(__webpack_require__.bind(__webpack_require__, /*! @/lib/sharepoint-service */ \"(app-pages-browser)/./src/lib/sharepoint-service.ts\"));\n            await SharePointService.ensureScheduleFolder(entityId);\n            break;\n        default:\n            throw new Error(\"Unknown entity type: \".concat(entityType));\n    }\n}\n/**\n * Get the status of a background job\n */ function getJobStatus(jobId) {\n    const queue = getBackgroundJobQueue();\n    return queue.getJobStatus(jobId);\n}\n/**\n * Get all background jobs (for debugging)\n */ function getAllJobs() {\n    const queue = getBackgroundJobQueue();\n    return queue.getAllJobs();\n}\n/**\n * Queue a heatmap automation job for a schedule\n * @param scheduleId - ID of the schedule to process\n * @returns Job ID\n */ function queueBackgroundJob(type, entityType, entityId, payload) {\n    const queue = getBackgroundJobQueue();\n    const jobId = queue.addJob(type, entityType, entityId, payload);\n    console.log(\"\\uD83D\\uDCCB Heatmap automation queued for schedule:\".concat(entityId, \" with job ID: \").concat(jobId));\n    return jobId;\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/background-jobs.ts\n"));

/***/ })

}]);