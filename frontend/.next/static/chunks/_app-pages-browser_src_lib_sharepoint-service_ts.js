"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_src_lib_sharepoint-service_ts"],{

/***/ "(app-pages-browser)/./src/lib/microsoft-graph.ts":
/*!************************************!*\
  !*** ./src/lib/microsoft-graph.ts ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkIfFolderExists: () => (/* binding */ folderExists),\n/* harmony export */   createClientFolder: () => (/* binding */ createClientFolder),\n/* harmony export */   createFolder: () => (/* binding */ createFolder),\n/* harmony export */   createProjectFolder: () => (/* binding */ createProjectFolder),\n/* harmony export */   createProjectFolderStructure: () => (/* binding */ createProjectFolderStructure),\n/* harmony export */   createScheduleFolder: () => (/* binding */ createScheduleFolder),\n/* harmony export */   createSubfolderIfNotExists: () => (/* binding */ createSubfolderIfNotExists),\n/* harmony export */   getClientsFolderId: () => (/* binding */ getClientsFolderId),\n/* harmony export */   getMicrosoftGraphAccessToken: () => (/* binding */ getAccessToken),\n/* harmony export */   getRootFolderId: () => (/* binding */ getRootFolderId),\n/* harmony export */   getScheduleFolderLinks: () => (/* binding */ getScheduleFolderLinks),\n/* harmony export */   storeClientFolderInDatabase: () => (/* binding */ storeClientFolderInDatabase),\n/* harmony export */   storeProjectFolderInDatabase: () => (/* binding */ storeProjectFolderInDatabase)\n/* harmony export */ });\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\");\n// Function to load environment variables from .env.local if not already loaded\nfunction loadEnvIfNeeded() {\n    // Only run on server side\n    if (true) {\n        return; // Skip on client side\n    }\n    // Check if Microsoft Graph credentials are already loaded\n    if (process.env.MICROSOFT_CLIENT_ID && process.env.MICROSOFT_CLIENT_SECRET && process.env.MICROSOFT_TENANT_ID) {\n        return;\n    }\n    // Temporarily disabled to fix chunk loading issues\n    console.log('Microsoft Graph environment loading temporarily disabled');\n}\n// Constants for SharePoint configuration\nconst SHAREPOINT_SITE_URL = 'https://zn6bn.sharepoint.com/sites/files';\nconst DRIVE_ID = 'b!6BliBHB7rkqGeijn571F56igGQK5UQ9GkI_AIFbTSvvooGrFp94hQr6gPCGK1yYV';\n// Cache for site ID and access tokens to avoid repeated API calls\nlet cachedSiteId = null;\nlet cachedAccessToken = null;\nlet tokenExpiry = null;\n// Token refresh threshold (5 minutes before expiry)\nconst TOKEN_REFRESH_THRESHOLD = 5 * 60 * 1000;\n/**\n * Get the Site ID from SharePoint site URL\n * @param accessToken Microsoft Graph API access token\n * @returns Site ID\n */ async function getSiteId(accessToken) {\n    if (cachedSiteId) {\n        return cachedSiteId;\n    }\n    try {\n        // Extract hostname and site path from the SharePoint URL\n        const url = new URL(SHAREPOINT_SITE_URL);\n        const hostname = url.hostname;\n        const sitePath = url.pathname; // This will be '/sites/files'\n        // Format for Microsoft Graph API: hostname:/sites/sitename:\n        const siteIdentifier = \"\".concat(hostname, \":\").concat(sitePath, \":\");\n        console.log('Getting Site ID for:', siteIdentifier);\n        const response = await fetch(\"https://graph.microsoft.com/v1.0/sites/\".concat(siteIdentifier), {\n            headers: {\n                Authorization: \"Bearer \".concat(accessToken)\n            }\n        });\n        if (!response.ok) {\n            const errorText = await response.text();\n            console.error('Site ID request failed:', response.status, errorText);\n            throw new Error(\"HTTP error! status: \".concat(response.status));\n        }\n        const data = await response.json();\n        console.log('Site ID retrieved successfully:', data.id);\n        cachedSiteId = data.id;\n        return data.id;\n    } catch (error) {\n        console.error('Error getting Site ID:', error);\n        throw new Error('Failed to get SharePoint Site ID');\n    }\n}\n/**\n * Get Microsoft Graph API access token using client credentials flow\n * @returns Access token for Microsoft Graph API\n */ async function getAccessToken() {\n    // Check if we have a cached token that's still valid\n    const now = Date.now();\n    if (cachedAccessToken && tokenExpiry && now < tokenExpiry - TOKEN_REFRESH_THRESHOLD) {\n        return cachedAccessToken;\n    }\n    // Ensure environment variables are loaded\n    loadEnvIfNeeded();\n    const clientId = process.env.MICROSOFT_CLIENT_ID;\n    const clientSecret = process.env.MICROSOFT_CLIENT_SECRET;\n    const tenantId = process.env.MICROSOFT_TENANT_ID;\n    if (!clientId || !clientSecret || !tenantId) {\n        throw new Error('Missing Microsoft Graph API credentials in environment variables');\n    }\n    try {\n        const response = await fetch(\"https://login.microsoftonline.com/\".concat(tenantId, \"/oauth2/v2.0/token\"), {\n            method: 'POST',\n            headers: {\n                'Content-Type': 'application/x-www-form-urlencoded'\n            },\n            body: new URLSearchParams({\n                client_id: clientId,\n                scope: 'https://graph.microsoft.com/.default',\n                client_secret: clientSecret,\n                grant_type: 'client_credentials'\n            })\n        });\n        if (!response.ok) {\n            throw new Error(\"HTTP error! status: \".concat(response.status));\n        }\n        const data = await response.json();\n        // Cache the token and its expiry time (typically 1 hour)\n        cachedAccessToken = data.access_token;\n        tokenExpiry = now + data.expires_in * 1000;\n        return data.access_token;\n    } catch (error) {\n        console.error('Error getting Microsoft Graph API access token:', error);\n        throw new Error('Failed to authenticate with Microsoft Graph API');\n    }\n}\n/**\n * Check if a folder exists in SharePoint\n * @param accessToken Microsoft Graph API access token\n * @param folderPath Path to check\n * @returns Folder information if exists, null otherwise\n */ async function folderExists(accessToken, folderPath) {\n    try {\n        const siteId = await getSiteId(accessToken);\n        const response = await fetch(\"https://graph.microsoft.com/v1.0/sites/\".concat(siteId, \"/drives/\").concat(DRIVE_ID, \"/root:\").concat(folderPath), {\n            headers: {\n                Authorization: \"Bearer \".concat(accessToken)\n            }\n        });\n        if (!response.ok) {\n            // If folder doesn't exist, Graph API returns 404\n            if (response.status === 404) {\n                return null;\n            }\n            // Re-throw other errors\n            throw new Error(\"HTTP error! status: \".concat(response.status));\n        }\n        const data = await response.json();\n        return {\n            id: data.id,\n            webUrl: data.webUrl\n        };\n    } catch (error) {\n        console.error('Error checking if folder exists at \"'.concat(folderPath, '\":'), error);\n        throw error;\n    }\n}\n/**\n * Create a folder in SharePoint with retry logic\n * @param accessToken Microsoft Graph API access token\n * @param parentFolderId ID of parent folder\n * @param folderName Name of folder to create\n * @returns Created folder information\n */ async function createFolder(accessToken, parentFolderId, folderName) {\n    const maxRetries = 3;\n    let lastError;\n    for(let attempt = 1; attempt <= maxRetries; attempt++){\n        try {\n            const siteId = await getSiteId(accessToken);\n            const response = await fetch(\"https://graph.microsoft.com/v1.0/sites/\".concat(siteId, \"/drives/\").concat(DRIVE_ID, \"/items/\").concat(parentFolderId, \"/children\"), {\n                method: 'POST',\n                headers: {\n                    Authorization: \"Bearer \".concat(accessToken),\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    name: folderName,\n                    folder: {}\n                })\n            });\n            if (!response.ok) {\n                throw new Error(\"HTTP error! status: \".concat(response.status));\n            }\n            const data = await response.json();\n            return {\n                id: data.id,\n                webUrl: data.webUrl\n            };\n        } catch (error) {\n            console.error('Error creating folder \"'.concat(folderName, '\" (attempt ').concat(attempt, \"/\").concat(maxRetries, \"):\"), error);\n            lastError = error;\n            // Wait before retrying (exponential backoff)\n            if (attempt < maxRetries) {\n                await new Promise((resolve)=>setTimeout(resolve, Math.pow(2, attempt) * 1000));\n            }\n        }\n    }\n    throw new Error('Failed to create folder \"'.concat(folderName, '\" after ').concat(maxRetries, \" attempts: \").concat(lastError instanceof Error ? lastError.message : 'Unknown error'));\n}\n/**\n * Create a share link for a folder with \"anyone with the link\" permissions\n * @param accessToken Microsoft Graph API access token\n * @param folderId ID of the folder to create share link for\n * @returns Share link URL\n */ async function createShareLink(accessToken, folderId) {\n    try {\n        const siteId = await getSiteId(accessToken);\n        const response = await fetch(\"https://graph.microsoft.com/v1.0/sites/\".concat(siteId, \"/drives/\").concat(DRIVE_ID, \"/items/\").concat(folderId, \"/createLink\"), {\n            method: 'POST',\n            headers: {\n                Authorization: \"Bearer \".concat(accessToken),\n                'Content-Type': 'application/json'\n            },\n            body: JSON.stringify({\n                type: 'view',\n                scope: 'anonymous'\n            })\n        });\n        if (!response.ok) {\n            throw new Error(\"HTTP error! status: \".concat(response.status));\n        }\n        const data = await response.json();\n        return data.link.webUrl;\n    } catch (error) {\n        console.error('Error creating share link for folder ID \"'.concat(folderId, '\":'), error);\n        throw new Error(\"Failed to create share link for folder\");\n    }\n}\n/**\n * Get the ID of the root drive folder\n * @param accessToken Microsoft Graph API access token\n * @returns ID of the root folder\n */ async function getRootFolderId(accessToken) {\n    try {\n        const siteId = await getSiteId(accessToken);\n        const response = await fetch(\"https://graph.microsoft.com/v1.0/sites/\".concat(siteId, \"/drives/\").concat(DRIVE_ID, \"/root\"), {\n            headers: {\n                Authorization: \"Bearer \".concat(accessToken)\n            }\n        });\n        if (!response.ok) {\n            throw new Error(\"HTTP error! status: \".concat(response.status));\n        }\n        const data = await response.json();\n        return data.id;\n    } catch (error) {\n        console.error('Error getting root folder ID:', error);\n        throw new Error('Failed to get root folder ID');\n    }\n}\n/**\n * Create a client folder in SharePoint root directory\n * @param clientId ID of the client to associate with the folder\n * @param clientCustomId Custom ID of the client (e.g., CYMCL-25-001)\n * @param clientName Name of the client\n * @returns Object containing folder information\n */ async function createClientFolder(clientId, clientCustomId, clientName) {\n    try {\n        // Get access token\n        const accessToken = await getAccessToken();\n        // Get the root folder ID\n        const rootFolderId = await getRootFolderId(accessToken);\n        // Create folder name: {ClientCustomID} {ClientName}\n        const folderName = \"\".concat(clientCustomId, \" \").concat(clientName);\n        // Check if folder already exists\n        const existingFolder = await folderExists(accessToken, \"/\".concat(folderName));\n        let clientFolder;\n        if (existingFolder) {\n            console.log('Client folder \"'.concat(folderName, '\" already exists'));\n            clientFolder = existingFolder;\n        } else {\n            console.log('Creating client folder \"'.concat(folderName, '\"'));\n            clientFolder = await createFolder(accessToken, rootFolderId, folderName);\n        }\n        // Create share link for the client folder\n        let shareLink;\n        try {\n            shareLink = await createShareLink(accessToken, clientFolder.id);\n            console.log(\"Created share link for client folder: \".concat(shareLink));\n        } catch (error) {\n            console.warn('Failed to create share link for client folder:', error);\n        // Continue without share link\n        }\n        // Store folder information in the database\n        await storeClientFolderInDatabase(clientId, clientFolder.id, clientFolder.webUrl, shareLink);\n        return {\n            folder: {\n                ...clientFolder,\n                shareLink\n            }\n        };\n    } catch (error) {\n        console.error('Error creating client folder:', error);\n        throw new Error(\"Failed to create client folder: \".concat(error instanceof Error ? error.message : 'Unknown error'));\n    }\n}\n/**\n * Create a project folder in SharePoint\n * @param projectId ID of the project\n * @param projectCustomId Custom ID of the project\n * @param projectName Name of the project\n * @param clientId ID of the client (parent folder)\n * @returns Result containing folder information\n */ async function createProjectFolder(projectId, projectCustomId, projectName, clientId) {\n    try {\n        // Get access token\n        const accessToken = await getAccessToken();\n        // Get client information from database using server-side client\n        const { createClient } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @supabase/supabase-js */ \"(app-pages-browser)/./node_modules/@supabase/supabase-js/dist/module/index.js\"));\n        // Validate environment variables with better error context\n        const supabaseUrl = \"https://qxgfulribhhohmggyroq.supabase.co\";\n        const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;\n        console.log('🔍 Environment variable check in storeProjectFolderInDatabase:', {\n            supabaseUrl: supabaseUrl ? 'Present' : 'Missing',\n            supabaseServiceKey: supabaseServiceKey ? 'Present' : 'Missing',\n            nodeEnv: \"development\",\n            context: 'background-job'\n        });\n        if (!supabaseUrl) {\n            console.error('❌ NEXT_PUBLIC_SUPABASE_URL is missing in background job context');\n            throw new Error('NEXT_PUBLIC_SUPABASE_URL is required');\n        }\n        if (!supabaseServiceKey) {\n            console.error('❌ SUPABASE_SERVICE_ROLE_KEY is missing in background job context');\n            throw new Error('SUPABASE_SERVICE_ROLE_KEY is required');\n        }\n        const supabase = createClient(supabaseUrl, supabaseServiceKey);\n        const { data: client, error: clientError } = await supabase.from('clients').select('custom_id, name').eq('id', clientId).single();\n        if (clientError || !client) {\n            throw new Error(\"Failed to get client information: \".concat((clientError === null || clientError === void 0 ? void 0 : clientError.message) || 'Client not found'));\n        }\n        // Create client folder name and project folder name\n        const clientFolderName = \"\".concat(client.custom_id, \" \").concat(client.name);\n        const projectFolderName = \"\".concat(projectCustomId, \" \").concat(projectName);\n        // Check if client folder exists, create if not\n        const clientFolderPath = \"/\".concat(clientFolderName);\n        let clientFolder = await folderExists(accessToken, clientFolderPath);\n        if (!clientFolder) {\n            const rootFolderId = await getRootFolderId(accessToken);\n            clientFolder = await createFolder(accessToken, rootFolderId, clientFolderName);\n        }\n        // Check if project folder already exists\n        const projectFolderPath = \"/\".concat(clientFolderName, \"/\").concat(projectFolderName);\n        const existingProjectFolder = await folderExists(accessToken, projectFolderPath);\n        let projectFolder;\n        if (existingProjectFolder) {\n            console.log('Project folder \"'.concat(projectFolderName, '\" already exists'));\n            projectFolder = existingProjectFolder;\n        } else {\n            console.log('Creating project folder \"'.concat(projectFolderName, '\"'));\n            projectFolder = await createFolder(accessToken, clientFolder.id, projectFolderName);\n        }\n        // Create share link for the project folder\n        let shareLink;\n        try {\n            shareLink = await createShareLink(accessToken, projectFolder.id);\n            console.log(\"Created share link for project folder: \".concat(shareLink));\n        } catch (error) {\n            console.warn('Failed to create share link for project folder:', error);\n        // Continue without share link\n        }\n        // Store folder information in the database\n        await storeProjectFolderInDatabase(projectId, projectFolder.id, projectFolder.webUrl, shareLink);\n        return {\n            folder: {\n                ...projectFolder,\n                shareLink\n            }\n        };\n    } catch (error) {\n        console.error('Error creating project folder:', error);\n        throw new Error(\"Failed to create project folder: \".concat(error instanceof Error ? error.message : 'Unknown error'));\n    }\n}\n/**\n * Create a schedule folder inside the project folder\n * @param scheduleId ID of the schedule to associate with the folder\n * @param scheduleCustomId Custom ID of the schedule (e.g., CYM-25-005)\n * @param scheduleDate Date of the schedule (YYYY-MM-DD format)\n * @param projectCustomId Custom ID of the project\n * @param projectName Name of the project\n * @param clientCustomId Custom ID of the client\n * @param clientName Name of the client\n * @returns Object containing folder information for the main schedule folder\n */ async function createScheduleFolder(scheduleId, scheduleCustomId, scheduleDate, projectCustomId, projectName, clientCustomId, clientName) {\n    try {\n        // Get access token\n        const accessToken = await getAccessToken();\n        // Create folder names\n        const clientFolderName = \"\".concat(clientCustomId, \" \").concat(clientName);\n        const projectFolderName = \"\".concat(projectCustomId, \" \").concat(projectName);\n        const scheduleFolderName = \"\".concat(scheduleCustomId, \" \").concat(scheduleDate);\n        // Ensure client folder exists\n        const clientFolderPath = \"/\".concat(clientFolderName);\n        let clientFolder = await folderExists(accessToken, clientFolderPath);\n        if (!clientFolder) {\n            const rootFolderId = await getRootFolderId(accessToken);\n            clientFolder = await createFolder(accessToken, rootFolderId, clientFolderName);\n        }\n        // Ensure project folder exists\n        const projectFolderPath = \"/\".concat(clientFolderName, \"/\").concat(projectFolderName);\n        let projectFolder = await folderExists(accessToken, projectFolderPath);\n        if (!projectFolder) {\n            projectFolder = await createFolder(accessToken, clientFolder.id, projectFolderName);\n        }\n        // Check if schedule folder already exists\n        const scheduleFolderPath = \"/\".concat(clientFolderName, \"/\").concat(projectFolderName, \"/\").concat(scheduleFolderName);\n        const existingScheduleFolder = await folderExists(accessToken, scheduleFolderPath);\n        let scheduleFolder;\n        if (existingScheduleFolder) {\n            console.log('Schedule folder \"'.concat(scheduleFolderName, '\" already exists'));\n            scheduleFolder = existingScheduleFolder;\n        } else {\n            console.log('Creating schedule folder \"'.concat(scheduleFolderName, '\"'));\n            scheduleFolder = await createFolder(accessToken, projectFolder.id, scheduleFolderName);\n        }\n        // Create Raw and Output subfolders inside the schedule folder\n        try {\n            console.log('Creating \"Raw\" subfolder in schedule folder \"'.concat(scheduleFolderName, '\"'));\n            await createFolder(accessToken, scheduleFolder.id, 'Raw');\n            console.log('Successfully created \"Raw\" subfolder');\n            console.log('Creating \"Output\" subfolder in schedule folder \"'.concat(scheduleFolderName, '\"'));\n            await createFolder(accessToken, scheduleFolder.id, 'Output');\n            console.log('Successfully created \"Output\" subfolder');\n        } catch (subfolderError) {\n            console.warn('Failed to create subfolders (Raw/Output) in schedule folder:', subfolderError);\n        // Don't fail the entire operation if subfolder creation fails\n        }\n        // Create share link for the schedule folder\n        let scheduleShareLink;\n        try {\n            scheduleShareLink = await createShareLink(accessToken, scheduleFolder.id);\n            console.log(\"Created share link for schedule folder: \".concat(scheduleShareLink));\n        } catch (error) {\n            console.warn('Failed to create share link for schedule folder:', error);\n        }\n        // Store folder information in the database (only main schedule folder, not subfolders)\n        await storeScheduleFolderLinksInDatabase(scheduleId, scheduleFolder.id, scheduleFolder.webUrl, scheduleShareLink);\n        return {\n            folder: {\n                ...scheduleFolder,\n                shareLink: scheduleShareLink\n            }\n        };\n    } catch (error) {\n        console.error('Error creating schedule folder:', error);\n        throw new Error(\"Failed to create schedule folder: \".concat(error instanceof Error ? error.message : 'Unknown error'));\n    }\n}\n/**\n * Create folder structure in SharePoint for a project and store links in database\n * @param scheduleId ID of the schedule to associate with the folders\n * @param clientName Name of the client\n * @param projectName Name of the project\n * @param date Date of the project (YYYY-MM-DD format)\n * @returns Object containing folder information for the main schedule folder\n */ async function createProjectFolderStructure(scheduleId, clientName, projectName, date) {\n    try {\n        // Get access token\n        const accessToken = await getAccessToken();\n        // Get the Clients folder ID (root for our structure)\n        let currentFolderId = await getClientsFolderId(accessToken);\n        // Build the folder path: /Clients/<clientName>/<projectName>/<date>/\n        const folderPath = \"/\".concat(clientName, \"/\").concat(projectName, \"/\").concat(date);\n        const fullPath = \"/Clients\".concat(folderPath);\n        // Create each folder in the path if it doesn't exist\n        const pathParts = [\n            clientName,\n            projectName,\n            date\n        ];\n        for (const part of pathParts){\n            const currentPath = \"/Clients/\".concat(pathParts.slice(0, pathParts.indexOf(part) + 1).join('/'));\n            const existingFolder = await folderExists(accessToken, currentPath);\n            if (existingFolder) {\n                console.log('Folder \"'.concat(part, '\" already exists at ').concat(currentPath));\n                currentFolderId = existingFolder.id;\n            } else {\n                console.log('Creating folder \"'.concat(part, '\" at ').concat(currentPath));\n                const newFolder = await createFolder(accessToken, currentFolderId, part);\n                currentFolderId = newFolder.id;\n            }\n        }\n        // Create share link for the main folder\n        const shareLink = await createShareLink(accessToken, currentFolderId);\n        // Get the main folder information\n        const mainFolder = {\n            id: currentFolderId,\n            webUrl: fullPath,\n            shareLink: shareLink\n        };\n        // Store folder information in the database\n        await storeScheduleFolderLinksInDatabase(scheduleId, mainFolder.id, mainFolder.webUrl, mainFolder.shareLink);\n        return mainFolder;\n    } catch (error) {\n        console.error('Error creating project folder structure:', error);\n        throw new Error(\"Failed to create folder structure: \".concat(error instanceof Error ? error.message : 'Unknown error'));\n    }\n}\n/**\n * Create a subfolder if it doesn't already exist\n * @param accessToken Microsoft Graph API access token\n * @param parentFolderId ID of parent folder\n * @param folderName Name of folder to create\n * @returns Folder information\n */ async function createSubfolderIfNotExists(accessToken, parentFolderId, folderName) {\n    try {\n        // Try to get the folder first\n        const siteId = await getSiteId(accessToken);\n        const response = await fetch(\"https://graph.microsoft.com/v1.0/sites/\".concat(siteId, \"/drives/\").concat(DRIVE_ID, \"/items/\").concat(parentFolderId, \"/children?$filter=name eq '\").concat(folderName, \"'\"), {\n            headers: {\n                Authorization: \"Bearer \".concat(accessToken)\n            }\n        });\n        if (!response.ok) {\n            // If we get an error other than 404, re-throw it\n            if (response.status !== 404) {\n                throw new Error(\"HTTP error! status: \".concat(response.status));\n            }\n        // If 404, continue to create the folder\n        } else {\n            const data = await response.json();\n            // Check if folder exists in response\n            if (data.value && data.value.length > 0) {\n                const existingFolder = data.value[0];\n                if (existingFolder.folder) {\n                    console.log('Folder \"'.concat(folderName, '\" already exists'));\n                    return {\n                        id: existingFolder.id,\n                        webUrl: existingFolder.webUrl\n                    };\n                }\n            }\n        }\n        // Folder doesn't exist, create it\n        console.log('Creating folder \"'.concat(folderName, '\"'));\n        return await createFolder(accessToken, parentFolderId, folderName);\n    } catch (error) {\n        console.error('Error checking/creating folder \"'.concat(folderName, '\":'), error);\n        // Try to create the folder directly\n        return await createFolder(accessToken, parentFolderId, folderName);\n    }\n}\n/**\n * Store client folder information in the database\n * @param clientId ID of the client to update\n * @param folderId SharePoint ID of the client folder\n * @param folderUrl SharePoint URL of the client folder\n * @param shareLink Public share link for the client folder (optional)\n */ async function storeClientFolderInDatabase(clientId, folderId, folderUrl, shareLink) {\n    try {\n        // Use server-side Supabase client\n        const { createClient } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @supabase/supabase-js */ \"(app-pages-browser)/./node_modules/@supabase/supabase-js/dist/module/index.js\"));\n        // Validate environment variables with better error context\n        const supabaseUrl = \"https://qxgfulribhhohmggyroq.supabase.co\";\n        const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;\n        console.log('🔍 Environment variable check in storeClientFolderInDatabase:', {\n            supabaseUrl: supabaseUrl ? 'Present' : 'Missing',\n            supabaseServiceKey: supabaseServiceKey ? 'Present' : 'Missing',\n            nodeEnv: \"development\",\n            context: 'background-job'\n        });\n        if (!supabaseUrl) {\n            console.error('❌ NEXT_PUBLIC_SUPABASE_URL is missing in background job context');\n            throw new Error('NEXT_PUBLIC_SUPABASE_URL is required');\n        }\n        if (!supabaseServiceKey) {\n            console.error('❌ SUPABASE_SERVICE_ROLE_KEY is missing in background job context');\n            throw new Error('SUPABASE_SERVICE_ROLE_KEY is required');\n        }\n        const supabase = createClient(supabaseUrl, supabaseServiceKey);\n        const updateData = {\n            sharepoint_folder_id: folderId,\n            sharepoint_folder_url: folderUrl\n        };\n        if (shareLink) {\n            updateData.sharepoint_share_link = shareLink;\n        }\n        const { error } = await supabase.from('clients').update(updateData).eq('id', clientId);\n        if (error) {\n            console.error('Error storing client folder in database:', error);\n            throw new Error('Failed to store client folder in database');\n        }\n        console.log('Successfully stored client folder in database for client:', clientId);\n    } catch (error) {\n        console.error('Error in storeClientFolderInDatabase:', error);\n        throw error;\n    }\n}\n/**\n * Store project folder information in the database\n * @param projectId ID of the project to update\n * @param folderId SharePoint ID of the project folder\n * @param folderUrl SharePoint URL of the project folder\n * @param shareLink Public share link for the project folder (optional)\n */ async function storeProjectFolderInDatabase(projectId, folderId, folderUrl, shareLink) {\n    try {\n        // Use server-side Supabase client\n        const { createClient } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @supabase/supabase-js */ \"(app-pages-browser)/./node_modules/@supabase/supabase-js/dist/module/index.js\"));\n        // Validate environment variables with better error context\n        const supabaseUrl = \"https://qxgfulribhhohmggyroq.supabase.co\";\n        const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;\n        console.log('🔍 Environment variable check in getScheduleFolderLinks:', {\n            supabaseUrl: supabaseUrl ? 'Present' : 'Missing',\n            supabaseServiceKey: supabaseServiceKey ? 'Present' : 'Missing',\n            nodeEnv: \"development\",\n            context: 'background-job'\n        });\n        if (!supabaseUrl) {\n            console.error('❌ NEXT_PUBLIC_SUPABASE_URL is missing in background job context');\n            throw new Error('NEXT_PUBLIC_SUPABASE_URL is required');\n        }\n        if (!supabaseServiceKey) {\n            console.error('❌ SUPABASE_SERVICE_ROLE_KEY is missing in background job context');\n            throw new Error('SUPABASE_SERVICE_ROLE_KEY is required');\n        }\n        const supabase = createClient(supabaseUrl, supabaseServiceKey);\n        const updateData = {\n            sharepoint_folder_id: folderId,\n            sharepoint_folder_url: folderUrl\n        };\n        if (shareLink) {\n            updateData.sharepoint_share_link = shareLink;\n        }\n        const { error } = await supabase.from('projects').update(updateData).eq('id', projectId);\n        if (error) {\n            console.error('Error storing project folder in database:', error);\n            throw new Error('Failed to store project folder in database');\n        }\n        console.log('Successfully stored project folder in database for project:', projectId);\n    } catch (error) {\n        console.error('Error in storeProjectFolderInDatabase:', error);\n        throw error;\n    }\n}\n/**\n * Store schedule folder links in the schedules table\n * @param scheduleId ID of the schedule\n * @param scheduleFolderId SharePoint ID of the schedule folder\n * @param scheduleFolderUrl SharePoint URL of the schedule folder\n * @param scheduleShareLink Public share link for the schedule folder\n */ async function storeScheduleFolderLinksInDatabase(scheduleId, scheduleFolderId, scheduleFolderUrl, scheduleShareLink) {\n    try {\n        // Use server-side Supabase client\n        const { createClient } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @supabase/supabase-js */ \"(app-pages-browser)/./node_modules/@supabase/supabase-js/dist/module/index.js\"));\n        // Validate environment variables with better error context\n        const supabaseUrl = \"https://qxgfulribhhohmggyroq.supabase.co\";\n        const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;\n        console.log('🔍 Environment variable check in storeScheduleFolderLinksInDatabase:', {\n            supabaseUrl: supabaseUrl ? 'Present' : 'Missing',\n            supabaseServiceKey: supabaseServiceKey ? 'Present' : 'Missing',\n            nodeEnv: \"development\",\n            context: 'background-job'\n        });\n        if (!supabaseUrl) {\n            console.error('❌ NEXT_PUBLIC_SUPABASE_URL is missing in background job context');\n            throw new Error('NEXT_PUBLIC_SUPABASE_URL is required');\n        }\n        if (!supabaseServiceKey) {\n            console.error('❌ SUPABASE_SERVICE_ROLE_KEY is missing in background job context');\n            throw new Error('SUPABASE_SERVICE_ROLE_KEY is required');\n        }\n        const supabase = createClient(supabaseUrl, supabaseServiceKey);\n        // Prepare update data for schedules table\n        const updateData = {\n            sharepoint_folder_id: scheduleFolderId,\n            sharepoint_folder_url: scheduleFolderUrl\n        };\n        if (scheduleShareLink) {\n            updateData.sharepoint_share_link = scheduleShareLink;\n        }\n        // Update the schedules table\n        const { error } = await supabase.from('schedules').update(updateData).eq('id', scheduleId);\n        if (error) {\n            console.error('Error storing schedule folder links in database:', error);\n            throw new Error('Failed to store schedule folder links in database');\n        }\n        console.log('Successfully stored schedule folder links in schedules table for schedule:', scheduleId);\n    } catch (error) {\n        console.error('Error in storeScheduleFolderLinksInDatabase:', error);\n        throw error;\n    }\n}\n/**\n * Get schedule folder links from the database\n * @param scheduleId ID of the schedule\n * @returns Object containing folder information for the main schedule folder, or null if not found\n */ async function getScheduleFolderLinks(scheduleId) {\n    // Skip on client side\n    if (true) {\n        console.log('Microsoft Graph functions disabled on client side');\n        return null;\n    }\n    try {\n        // Use server-side Supabase client\n        const { createClient } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @supabase/supabase-js */ \"(app-pages-browser)/./node_modules/@supabase/supabase-js/dist/module/index.js\"));\n        // Validate environment variables\n        const supabaseUrl = \"https://qxgfulribhhohmggyroq.supabase.co\";\n        const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;\n        if (!supabaseUrl) {\n            throw new Error('NEXT_PUBLIC_SUPABASE_URL is required');\n        }\n        if (!supabaseServiceKey) {\n            throw new Error('SUPABASE_SERVICE_ROLE_KEY is required');\n        }\n        const supabase = createClient(supabaseUrl, supabaseServiceKey);\n        const { data, error } = await supabase.from('schedules').select(\"\\n        sharepoint_folder_id,\\n        sharepoint_folder_url,\\n        sharepoint_share_link\\n      \").eq('id', scheduleId).single();\n        if (error) {\n            console.error('Error getting schedule folder links from database:', error);\n            return null;\n        }\n        if (!data || !data.sharepoint_folder_id || !data.sharepoint_folder_url) {\n            console.log('No schedule folder links found for schedule:', scheduleId);\n            return null;\n        }\n        const result = {\n            id: data.sharepoint_folder_id,\n            webUrl: data.sharepoint_folder_url,\n            shareLink: data.sharepoint_share_link\n        };\n        return result;\n    } catch (error) {\n        console.error('Error in getScheduleFolderLinks:', error);\n        return null;\n    }\n}\n/**\n * Get the ID of the Clients folder (legacy function for compatibility)\n * @param accessToken Microsoft Graph API access token\n * @returns ID of the Clients folder\n */ async function getClientsFolderId(accessToken) {\n    // For backward compatibility, return the root folder ID\n    return await getRootFolderId(accessToken);\n}\n\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/microsoft-graph.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/sharepoint-service.ts":
/*!***************************************!*\
  !*** ./src/lib/sharepoint-service.ts ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SharePointService: () => (/* binding */ SharePointService)\n/* harmony export */ });\n/* harmony import */ var _lib_microsoft_graph__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/microsoft-graph */ \"(app-pages-browser)/./src/lib/microsoft-graph.ts\");\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @supabase/supabase-js */ \"(app-pages-browser)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\");\n\n\n// Simple cache for folder IDs to reduce database calls\nconst folderIdCache = new Map();\nconst CACHE_TTL = 5 * 60 * 1000 // 5 minutes\n;\n// Helper function to create server-side Supabase client with proper error handling\nfunction createServerSupabaseClient() {\n    console.log('--- ENV VARS IN SHAREPOINT-SERVICE ---');\n    console.log('NEXT_PUBLIC_SUPABASE_URL:', \"https://qxgfulribhhohmggyroq.supabase.co\");\n    console.log('SUPABASE_SERVICE_ROLE_KEY:', process.env.SUPABASE_SERVICE_ROLE_KEY);\n    console.log('------------------------------------');\n    const supabaseUrl = \"https://qxgfulribhhohmggyroq.supabase.co\";\n    const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;\n    if (!supabaseUrl) {\n        console.error('❌ NEXT_PUBLIC_SUPABASE_URL is missing in sharepoint-service.ts background job context');\n        throw new Error('NEXT_PUBLIC_SUPABASE_URL is required');\n    }\n    if (!supabaseServiceKey) {\n        console.error('❌ SUPABASE_SERVICE_ROLE_KEY is missing in sharepoint-service.ts background job context');\n        throw new Error('SUPABASE_SERVICE_ROLE_KEY is required');\n    }\n    return (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__.createClient)(supabaseUrl, supabaseServiceKey);\n}\nclass SharePointService {\n    /**\n   * Ensures SharePoint folder is created for a specific schedule\n   */ static async ensureScheduleFolder(scheduleId) {\n        try {\n            console.log('🔄 Ensuring SharePoint folder for schedule:', scheduleId);\n            // Get schedule details with project and client info using server client\n            const serverSupabase = createServerSupabaseClient();\n            const { data: schedule, error } = await serverSupabase.from('schedules').select(\"\\n          id,\\n          custom_id,\\n          project_id,\\n          scheduled_date,\\n          scheduled_end_date,\\n          projects!inner (\\n            id,\\n            custom_id,\\n            name,\\n            client_id,\\n            clients!inner (\\n              id,\\n              custom_id,\\n              name,\\n              sharepoint_folder_id\\n            )\\n          )\\n        \").eq('id', scheduleId).single();\n            if (error || !schedule) {\n                console.log('❌ Schedule not found:', scheduleId, error);\n                return false;\n            }\n            // Type assertion to help TypeScript understand the structure\n            const project = schedule.projects;\n            const client = project.clients;\n            if (!project || !client) {\n                console.log('❌ Project or client not found for schedule:', scheduleId);\n                return false;\n            }\n            // Check if we have all required data\n            if (!schedule.custom_id || !project.custom_id || !project.name || !client.custom_id || !client.name) {\n                console.log('❌ Missing required data for SharePoint folder creation:', {\n                    scheduleCustomId: schedule.custom_id,\n                    projectCustomId: project.custom_id,\n                    projectName: project.name,\n                    clientCustomId: client.custom_id,\n                    clientName: client.name\n                });\n                return false;\n            }\n            // Format the date for folder naming - using START date (scheduled_date)\n            // Convert to India timezone (IST) for consistent folder naming\n            const scheduleDate = new Date(schedule.scheduled_date);\n            const istDate = new Date(scheduleDate.toLocaleString(\"en-US\", {\n                timeZone: \"Asia/Kolkata\"\n            }));\n            const formattedDate = \"\".concat(istDate.getFullYear(), \"-\").concat(String(istDate.getMonth() + 1).padStart(2, '0'), \"-\").concat(String(istDate.getDate()).padStart(2, '0'));\n            console.log('🚀 Creating SharePoint folder with parameters:', {\n                scheduleId: schedule.id,\n                scheduleCustomId: schedule.custom_id,\n                scheduleDate: formattedDate,\n                projectCustomId: project.custom_id,\n                projectName: project.name,\n                clientCustomId: client.custom_id,\n                clientName: client.name\n            });\n            // Create the folder structure\n            await (0,_lib_microsoft_graph__WEBPACK_IMPORTED_MODULE_0__.createScheduleFolder)(schedule.id, schedule.custom_id, formattedDate, project.custom_id, project.name, client.custom_id, client.name // clientName\n            );\n            console.log('✅ SharePoint folder created successfully for schedule:', scheduleId);\n            return true;\n        } catch (error) {\n            console.error('❌ Failed to create SharePoint folder for schedule:', scheduleId, error);\n            return false;\n        }\n    }\n    /**\n   * Ensures SharePoint folders are created for all schedules that don't have them\n   */ static async ensureAllScheduleFolders() {\n        console.log('🚀 Starting ensureAllScheduleFolders...');\n        try {\n            // Get all schedules using direct Supabase query with service role\n            console.log('📋 Fetching all schedules with server client...');\n            const serverSupabase = createServerSupabaseClient();\n            const { data: schedules, error } = await serverSupabase.from('schedules').select(\"\\n          id,\\n          custom_id,\\n          project_id,\\n          scheduled_date\\n        \").order('scheduled_date');\n            if (error) {\n                console.error('❌ Error fetching schedules:', error);\n                throw new Error(\"Failed to fetch schedules: \".concat(error.message));\n            }\n            console.log(\"\\uD83D\\uDCCA Found \".concat((schedules === null || schedules === void 0 ? void 0 : schedules.length) || 0, \" schedules\"));\n            if (schedules && schedules.length > 0) {\n                console.log('🔍 First few schedules:', schedules.slice(0, 3).map((s)=>({\n                        id: s.id,\n                        custom_id: s.custom_id,\n                        project_id: s.project_id,\n                        scheduled_date: s.scheduled_date\n                    })));\n            }\n            let successCount = 0;\n            let failedCount = 0;\n            const details = [];\n            for (const schedule of schedules || []){\n                try {\n                    console.log(\"\\uD83D\\uDD04 Processing schedule \".concat(schedule.id, \"...\"));\n                    await SharePointService.ensureScheduleFolder(schedule.id);\n                    successCount++;\n                    details.push(\"✅ Schedule \".concat(schedule.id, \": Folder ensured\"));\n                    console.log(\"✅ Successfully processed schedule \".concat(schedule.id));\n                } catch (error) {\n                    failedCount++;\n                    const errorMessage = error instanceof Error ? error.message : 'Unknown error';\n                    details.push(\"❌ Schedule \".concat(schedule.id, \": \").concat(errorMessage));\n                    console.error(\"❌ Failed to process schedule \".concat(schedule.id, \":\"), error);\n                }\n                // Small delay to prevent API overload\n                await new Promise((resolve)=>setTimeout(resolve, 100));\n            }\n            console.log(\"\\uD83C\\uDFAF Completed: \".concat(successCount, \" successful, \").concat(failedCount, \" failed\"));\n            return {\n                success: successCount,\n                failed: failedCount,\n                details\n            };\n        } catch (error) {\n            console.error('💥 Error in ensureAllScheduleFolders:', error);\n            throw error;\n        }\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9saWIvc2hhcmVwb2ludC1zZXJ2aWNlLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBNEQ7QUFDUjtBQUVwRCx1REFBdUQ7QUFDdkQsTUFBTUUsZ0JBQWdCLElBQUlDO0FBQzFCLE1BQU1DLFlBQVksSUFBSSxLQUFLLEtBQUssWUFBWTs7QUFFNUMsbUZBQW1GO0FBQ25GLFNBQVNDO0lBQ1BDLFFBQVFDLEdBQUcsQ0FBQztJQUNaRCxRQUFRQyxHQUFHLENBQUMsNkJBQTZCQywwQ0FBb0M7SUFDN0VGLFFBQVFDLEdBQUcsQ0FBQyw4QkFBOEJDLE9BQU9BLENBQUNDLEdBQUcsQ0FBQ0UseUJBQXlCO0lBQy9FTCxRQUFRQyxHQUFHLENBQUM7SUFFWixNQUFNSyxjQUFjSiwwQ0FBb0M7SUFDeEQsTUFBTUsscUJBQXFCTCxPQUFPQSxDQUFDQyxHQUFHLENBQUNFLHlCQUF5QjtJQUVoRSxJQUFJLENBQUNDLGFBQWE7UUFDaEJOLFFBQVFRLEtBQUssQ0FBQztRQUNkLE1BQU0sSUFBSUMsTUFBTTtJQUNsQjtJQUVBLElBQUksQ0FBQ0Ysb0JBQW9CO1FBQ3ZCUCxRQUFRUSxLQUFLLENBQUM7UUFDZCxNQUFNLElBQUlDLE1BQU07SUFDbEI7SUFFQSxPQUFPZCxtRUFBWUEsQ0FBQ1csYUFBYUM7QUFDbkM7QUFFTyxNQUFNRztJQUNYOztHQUVDLEdBQ0QsYUFBYUMscUJBQXFCQyxVQUFrQixFQUFvQjtRQUN0RSxJQUFJO1lBQ0ZaLFFBQVFDLEdBQUcsQ0FBQywrQ0FBK0NXO1lBRTNELHdFQUF3RTtZQUN4RSxNQUFNQyxpQkFBaUJkO1lBQ3ZCLE1BQU0sRUFBRWUsTUFBTUMsUUFBUSxFQUFFUCxLQUFLLEVBQUUsR0FBRyxNQUFNSyxlQUNyQ0csSUFBSSxDQUFDLGFBQ0xDLE1BQU0sQ0FBRSxtWkFtQlJDLEVBQUUsQ0FBQyxNQUFNTixZQUNUTyxNQUFNO1lBRVQsSUFBSVgsU0FBUyxDQUFDTyxVQUFVO2dCQUN0QmYsUUFBUUMsR0FBRyxDQUFDLHlCQUF5QlcsWUFBWUo7Z0JBQ2pELE9BQU87WUFDVDtZQUVBLDZEQUE2RDtZQUM3RCxNQUFNWSxVQUFVTCxTQUFTTSxRQUFRO1lBQ2pDLE1BQU1DLFNBQVNGLFFBQVFHLE9BQU87WUFFOUIsSUFBSSxDQUFDSCxXQUFXLENBQUNFLFFBQVE7Z0JBQ3ZCdEIsUUFBUUMsR0FBRyxDQUFDLCtDQUErQ1c7Z0JBQzNELE9BQU87WUFDVDtZQUVBLHFDQUFxQztZQUNyQyxJQUFJLENBQUNHLFNBQVNTLFNBQVMsSUFBSSxDQUFDSixRQUFRSSxTQUFTLElBQUksQ0FBQ0osUUFBUUssSUFBSSxJQUMxRCxDQUFDSCxPQUFPRSxTQUFTLElBQUksQ0FBQ0YsT0FBT0csSUFBSSxFQUFFO2dCQUNyQ3pCLFFBQVFDLEdBQUcsQ0FBQywyREFBMkQ7b0JBQ3JFeUIsa0JBQWtCWCxTQUFTUyxTQUFTO29CQUNwQ0csaUJBQWlCUCxRQUFRSSxTQUFTO29CQUNsQ0ksYUFBYVIsUUFBUUssSUFBSTtvQkFDekJJLGdCQUFnQlAsT0FBT0UsU0FBUztvQkFDaENNLFlBQVlSLE9BQU9HLElBQUk7Z0JBQ3pCO2dCQUNBLE9BQU87WUFDVDtZQUVBLHdFQUF3RTtZQUN4RSwrREFBK0Q7WUFDL0QsTUFBTU0sZUFBZSxJQUFJQyxLQUFLakIsU0FBU2tCLGNBQWM7WUFDckQsTUFBTUMsVUFBVSxJQUFJRixLQUFLRCxhQUFhSSxjQUFjLENBQUMsU0FBUztnQkFBQ0MsVUFBVTtZQUFjO1lBQ3ZGLE1BQU1DLGdCQUFnQixHQUE0QkMsT0FBekJKLFFBQVFLLFdBQVcsSUFBRyxLQUFzREQsT0FBbkRBLE9BQU9KLFFBQVFNLFFBQVEsS0FBSyxHQUFHQyxRQUFRLENBQUMsR0FBRyxNQUFLLEtBQThDLE9BQTNDSCxPQUFPSixRQUFRUSxPQUFPLElBQUlELFFBQVEsQ0FBQyxHQUFHO1lBRTNJekMsUUFBUUMsR0FBRyxDQUFDLGtEQUFrRDtnQkFDNURXLFlBQVlHLFNBQVM0QixFQUFFO2dCQUN2QmpCLGtCQUFrQlgsU0FBU1MsU0FBUztnQkFDcENPLGNBQWNNO2dCQUNkVixpQkFBaUJQLFFBQVFJLFNBQVM7Z0JBQ2xDSSxhQUFhUixRQUFRSyxJQUFJO2dCQUN6QkksZ0JBQWdCUCxPQUFPRSxTQUFTO2dCQUNoQ00sWUFBWVIsT0FBT0csSUFBSTtZQUN6QjtZQUVBLDhCQUE4QjtZQUM5QixNQUFNL0IsMEVBQW9CQSxDQUN4QnFCLFNBQVM0QixFQUFFLEVBQ1g1QixTQUFTUyxTQUFTLEVBQ2xCYSxlQUNBakIsUUFBUUksU0FBUyxFQUNqQkosUUFBUUssSUFBSSxFQUNaSCxPQUFPRSxTQUFTLEVBQ2hCRixPQUFPRyxJQUFJLENBQXFCLGFBQWE7O1lBRy9DekIsUUFBUUMsR0FBRyxDQUFDLDBEQUEwRFc7WUFDdEUsT0FBTztRQUNULEVBQUUsT0FBT0osT0FBTztZQUNkUixRQUFRUSxLQUFLLENBQUMsc0RBQXNESSxZQUFZSjtZQUNoRixPQUFPO1FBQ1Q7SUFDRjtJQUVBOztHQUVDLEdBQ0QsYUFBYW9DLDJCQUE0RjtRQUN2RzVDLFFBQVFDLEdBQUcsQ0FBQztRQUVaLElBQUk7WUFDRixrRUFBa0U7WUFDbEVELFFBQVFDLEdBQUcsQ0FBQztZQUNaLE1BQU1ZLGlCQUFpQmQ7WUFDdkIsTUFBTSxFQUFFZSxNQUFNK0IsU0FBUyxFQUFFckMsS0FBSyxFQUFFLEdBQUcsTUFBTUssZUFDdENHLElBQUksQ0FBQyxhQUNMQyxNQUFNLENBQUUsb0dBTVI2QixLQUFLLENBQUM7WUFFVCxJQUFJdEMsT0FBTztnQkFDVFIsUUFBUVEsS0FBSyxDQUFDLCtCQUErQkE7Z0JBQzdDLE1BQU0sSUFBSUMsTUFBTSw4QkFBNEMsT0FBZEQsTUFBTXVDLE9BQU87WUFDN0Q7WUFFQS9DLFFBQVFDLEdBQUcsQ0FBQyxzQkFBbUMsT0FBdkI0QyxDQUFBQSxzQkFBQUEsZ0NBQUFBLFVBQVdHLE1BQU0sS0FBSSxHQUFFO1lBRS9DLElBQUlILGFBQWFBLFVBQVVHLE1BQU0sR0FBRyxHQUFHO2dCQUNyQ2hELFFBQVFDLEdBQUcsQ0FBQywyQkFBMkI0QyxVQUFVSSxLQUFLLENBQUMsR0FBRyxHQUFHQyxHQUFHLENBQUMsQ0FBQ0MsSUFBWTt3QkFDNUVSLElBQUlRLEVBQUVSLEVBQUU7d0JBQ1JuQixXQUFXMkIsRUFBRTNCLFNBQVM7d0JBQ3RCNEIsWUFBWUQsRUFBRUMsVUFBVTt3QkFDeEJuQixnQkFBZ0JrQixFQUFFbEIsY0FBYztvQkFDbEM7WUFDRjtZQUVBLElBQUlvQixlQUFlO1lBQ25CLElBQUlDLGNBQWM7WUFDbEIsTUFBTUMsVUFBb0IsRUFBRTtZQUU1QixLQUFLLE1BQU14QyxZQUFZOEIsYUFBYSxFQUFFLENBQUU7Z0JBQ3RDLElBQUk7b0JBQ0Y3QyxRQUFRQyxHQUFHLENBQUMsb0NBQXNDLE9BQVpjLFNBQVM0QixFQUFFLEVBQUM7b0JBQ2xELE1BQU1qQyxrQkFBa0JDLG9CQUFvQixDQUFDSSxTQUFTNEIsRUFBRTtvQkFDeERVO29CQUNBRSxRQUFRQyxJQUFJLENBQUMsY0FBMEIsT0FBWnpDLFNBQVM0QixFQUFFLEVBQUM7b0JBQ3ZDM0MsUUFBUUMsR0FBRyxDQUFDLHFDQUFpRCxPQUFaYyxTQUFTNEIsRUFBRTtnQkFDOUQsRUFBRSxPQUFPbkMsT0FBTztvQkFDZDhDO29CQUNBLE1BQU1HLGVBQWVqRCxpQkFBaUJDLFFBQVFELE1BQU11QyxPQUFPLEdBQUc7b0JBQzlEUSxRQUFRQyxJQUFJLENBQUMsY0FBOEJDLE9BQWhCMUMsU0FBUzRCLEVBQUUsRUFBQyxNQUFpQixPQUFiYztvQkFDM0N6RCxRQUFRUSxLQUFLLENBQUMsZ0NBQTRDLE9BQVpPLFNBQVM0QixFQUFFLEVBQUMsTUFBSW5DO2dCQUNoRTtnQkFFQSxzQ0FBc0M7Z0JBQ3RDLE1BQU0sSUFBSWtELFFBQVFDLENBQUFBLFVBQVdDLFdBQVdELFNBQVM7WUFDbkQ7WUFFQTNELFFBQVFDLEdBQUcsQ0FBQywyQkFBNkNxRCxPQUE1QkQsY0FBYSxpQkFBMkIsT0FBWkMsYUFBWTtZQUNyRSxPQUFPO2dCQUFFTyxTQUFTUjtnQkFBY1MsUUFBUVI7Z0JBQWFDO1lBQVE7UUFDL0QsRUFBRSxPQUFPL0MsT0FBTztZQUNkUixRQUFRUSxLQUFLLENBQUMseUNBQXlDQTtZQUN2RCxNQUFNQTtRQUNSO0lBQ0Y7QUFDRiIsInNvdXJjZXMiOlsiL1VzZXJzL3ZpamF5eWFzby9Eb2N1bWVudHMvRGV2ZWxvcG1lbnQvY3ltYXRpY3MvZnJvbnRlbmQvc3JjL2xpYi9zaGFyZXBvaW50LXNlcnZpY2UudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgY3JlYXRlU2NoZWR1bGVGb2xkZXIgfSBmcm9tICdAL2xpYi9taWNyb3NvZnQtZ3JhcGgnXG5pbXBvcnQgeyBjcmVhdGVDbGllbnQgfSBmcm9tICdAc3VwYWJhc2Uvc3VwYWJhc2UtanMnXG5cbi8vIFNpbXBsZSBjYWNoZSBmb3IgZm9sZGVyIElEcyB0byByZWR1Y2UgZGF0YWJhc2UgY2FsbHNcbmNvbnN0IGZvbGRlcklkQ2FjaGUgPSBuZXcgTWFwPHN0cmluZywgeyBpZDogc3RyaW5nOyB0aW1lc3RhbXA6IG51bWJlciB9PigpXG5jb25zdCBDQUNIRV9UVEwgPSA1ICogNjAgKiAxMDAwIC8vIDUgbWludXRlc1xuXG4vLyBIZWxwZXIgZnVuY3Rpb24gdG8gY3JlYXRlIHNlcnZlci1zaWRlIFN1cGFiYXNlIGNsaWVudCB3aXRoIHByb3BlciBlcnJvciBoYW5kbGluZ1xuZnVuY3Rpb24gY3JlYXRlU2VydmVyU3VwYWJhc2VDbGllbnQoKSB7XG4gIGNvbnNvbGUubG9nKCctLS0gRU5WIFZBUlMgSU4gU0hBUkVQT0lOVC1TRVJWSUNFIC0tLScpO1xuICBjb25zb2xlLmxvZygnTkVYVF9QVUJMSUNfU1VQQUJBU0VfVVJMOicsIHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX1NVUEFCQVNFX1VSTCk7XG4gIGNvbnNvbGUubG9nKCdTVVBBQkFTRV9TRVJWSUNFX1JPTEVfS0VZOicsIHByb2Nlc3MuZW52LlNVUEFCQVNFX1NFUlZJQ0VfUk9MRV9LRVkpO1xuICBjb25zb2xlLmxvZygnLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tJyk7XG5cbiAgY29uc3Qgc3VwYWJhc2VVcmwgPSBwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19TVVBBQkFTRV9VUkxcbiAgY29uc3Qgc3VwYWJhc2VTZXJ2aWNlS2V5ID0gcHJvY2Vzcy5lbnYuU1VQQUJBU0VfU0VSVklDRV9ST0xFX0tFWVxuXG4gIGlmICghc3VwYWJhc2VVcmwpIHtcbiAgICBjb25zb2xlLmVycm9yKCfinYwgTkVYVF9QVUJMSUNfU1VQQUJBU0VfVVJMIGlzIG1pc3NpbmcgaW4gc2hhcmVwb2ludC1zZXJ2aWNlLnRzIGJhY2tncm91bmQgam9iIGNvbnRleHQnKTtcbiAgICB0aHJvdyBuZXcgRXJyb3IoJ05FWFRfUFVCTElDX1NVUEFCQVNFX1VSTCBpcyByZXF1aXJlZCcpXG4gIH1cblxuICBpZiAoIXN1cGFiYXNlU2VydmljZUtleSkge1xuICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCBTVVBBQkFTRV9TRVJWSUNFX1JPTEVfS0VZIGlzIG1pc3NpbmcgaW4gc2hhcmVwb2ludC1zZXJ2aWNlLnRzIGJhY2tncm91bmQgam9iIGNvbnRleHQnKTtcbiAgICB0aHJvdyBuZXcgRXJyb3IoJ1NVUEFCQVNFX1NFUlZJQ0VfUk9MRV9LRVkgaXMgcmVxdWlyZWQnKVxuICB9XG5cbiAgcmV0dXJuIGNyZWF0ZUNsaWVudChzdXBhYmFzZVVybCwgc3VwYWJhc2VTZXJ2aWNlS2V5KVxufVxuXG5leHBvcnQgY2xhc3MgU2hhcmVQb2ludFNlcnZpY2Uge1xuICAvKipcbiAgICogRW5zdXJlcyBTaGFyZVBvaW50IGZvbGRlciBpcyBjcmVhdGVkIGZvciBhIHNwZWNpZmljIHNjaGVkdWxlXG4gICAqL1xuICBzdGF0aWMgYXN5bmMgZW5zdXJlU2NoZWR1bGVGb2xkZXIoc2NoZWR1bGVJZDogc3RyaW5nKTogUHJvbWlzZTxib29sZWFuPiB7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnNvbGUubG9nKCfwn5SEIEVuc3VyaW5nIFNoYXJlUG9pbnQgZm9sZGVyIGZvciBzY2hlZHVsZTonLCBzY2hlZHVsZUlkKVxuICAgICAgXG4gICAgICAvLyBHZXQgc2NoZWR1bGUgZGV0YWlscyB3aXRoIHByb2plY3QgYW5kIGNsaWVudCBpbmZvIHVzaW5nIHNlcnZlciBjbGllbnRcbiAgICAgIGNvbnN0IHNlcnZlclN1cGFiYXNlID0gY3JlYXRlU2VydmVyU3VwYWJhc2VDbGllbnQoKVxuICAgICAgY29uc3QgeyBkYXRhOiBzY2hlZHVsZSwgZXJyb3IgfSA9IGF3YWl0IHNlcnZlclN1cGFiYXNlXG4gICAgICAgIC5mcm9tKCdzY2hlZHVsZXMnKVxuICAgICAgICAuc2VsZWN0KGBcbiAgICAgICAgICBpZCxcbiAgICAgICAgICBjdXN0b21faWQsXG4gICAgICAgICAgcHJvamVjdF9pZCxcbiAgICAgICAgICBzY2hlZHVsZWRfZGF0ZSxcbiAgICAgICAgICBzY2hlZHVsZWRfZW5kX2RhdGUsXG4gICAgICAgICAgcHJvamVjdHMhaW5uZXIgKFxuICAgICAgICAgICAgaWQsXG4gICAgICAgICAgICBjdXN0b21faWQsXG4gICAgICAgICAgICBuYW1lLFxuICAgICAgICAgICAgY2xpZW50X2lkLFxuICAgICAgICAgICAgY2xpZW50cyFpbm5lciAoXG4gICAgICAgICAgICAgIGlkLFxuICAgICAgICAgICAgICBjdXN0b21faWQsXG4gICAgICAgICAgICAgIG5hbWUsXG4gICAgICAgICAgICAgIHNoYXJlcG9pbnRfZm9sZGVyX2lkXG4gICAgICAgICAgICApXG4gICAgICAgICAgKVxuICAgICAgICBgKVxuICAgICAgICAuZXEoJ2lkJywgc2NoZWR1bGVJZClcbiAgICAgICAgLnNpbmdsZSgpXG5cbiAgICAgIGlmIChlcnJvciB8fCAhc2NoZWR1bGUpIHtcbiAgICAgICAgY29uc29sZS5sb2coJ+KdjCBTY2hlZHVsZSBub3QgZm91bmQ6Jywgc2NoZWR1bGVJZCwgZXJyb3IpXG4gICAgICAgIHJldHVybiBmYWxzZVxuICAgICAgfVxuXG4gICAgICAvLyBUeXBlIGFzc2VydGlvbiB0byBoZWxwIFR5cGVTY3JpcHQgdW5kZXJzdGFuZCB0aGUgc3RydWN0dXJlXG4gICAgICBjb25zdCBwcm9qZWN0ID0gc2NoZWR1bGUucHJvamVjdHMgYXMgYW55XG4gICAgICBjb25zdCBjbGllbnQgPSBwcm9qZWN0LmNsaWVudHMgYXMgYW55XG5cbiAgICAgIGlmICghcHJvamVjdCB8fCAhY2xpZW50KSB7XG4gICAgICAgIGNvbnNvbGUubG9nKCfinYwgUHJvamVjdCBvciBjbGllbnQgbm90IGZvdW5kIGZvciBzY2hlZHVsZTonLCBzY2hlZHVsZUlkKVxuICAgICAgICByZXR1cm4gZmFsc2VcbiAgICAgIH1cblxuICAgICAgLy8gQ2hlY2sgaWYgd2UgaGF2ZSBhbGwgcmVxdWlyZWQgZGF0YVxuICAgICAgaWYgKCFzY2hlZHVsZS5jdXN0b21faWQgfHwgIXByb2plY3QuY3VzdG9tX2lkIHx8ICFwcm9qZWN0Lm5hbWUgfHwgXG4gICAgICAgICAgIWNsaWVudC5jdXN0b21faWQgfHwgIWNsaWVudC5uYW1lKSB7XG4gICAgICAgIGNvbnNvbGUubG9nKCfinYwgTWlzc2luZyByZXF1aXJlZCBkYXRhIGZvciBTaGFyZVBvaW50IGZvbGRlciBjcmVhdGlvbjonLCB7XG4gICAgICAgICAgc2NoZWR1bGVDdXN0b21JZDogc2NoZWR1bGUuY3VzdG9tX2lkLFxuICAgICAgICAgIHByb2plY3RDdXN0b21JZDogcHJvamVjdC5jdXN0b21faWQsXG4gICAgICAgICAgcHJvamVjdE5hbWU6IHByb2plY3QubmFtZSxcbiAgICAgICAgICBjbGllbnRDdXN0b21JZDogY2xpZW50LmN1c3RvbV9pZCxcbiAgICAgICAgICBjbGllbnROYW1lOiBjbGllbnQubmFtZVxuICAgICAgICB9KVxuICAgICAgICByZXR1cm4gZmFsc2VcbiAgICAgIH1cblxuICAgICAgLy8gRm9ybWF0IHRoZSBkYXRlIGZvciBmb2xkZXIgbmFtaW5nIC0gdXNpbmcgU1RBUlQgZGF0ZSAoc2NoZWR1bGVkX2RhdGUpXG4gICAgICAvLyBDb252ZXJ0IHRvIEluZGlhIHRpbWV6b25lIChJU1QpIGZvciBjb25zaXN0ZW50IGZvbGRlciBuYW1pbmdcbiAgICAgIGNvbnN0IHNjaGVkdWxlRGF0ZSA9IG5ldyBEYXRlKHNjaGVkdWxlLnNjaGVkdWxlZF9kYXRlKVxuICAgICAgY29uc3QgaXN0RGF0ZSA9IG5ldyBEYXRlKHNjaGVkdWxlRGF0ZS50b0xvY2FsZVN0cmluZyhcImVuLVVTXCIsIHt0aW1lWm9uZTogXCJBc2lhL0tvbGthdGFcIn0pKVxuICAgICAgY29uc3QgZm9ybWF0dGVkRGF0ZSA9IGAke2lzdERhdGUuZ2V0RnVsbFllYXIoKX0tJHtTdHJpbmcoaXN0RGF0ZS5nZXRNb250aCgpICsgMSkucGFkU3RhcnQoMiwgJzAnKX0tJHtTdHJpbmcoaXN0RGF0ZS5nZXREYXRlKCkpLnBhZFN0YXJ0KDIsICcwJyl9YFxuICAgICAgXG4gICAgICBjb25zb2xlLmxvZygn8J+agCBDcmVhdGluZyBTaGFyZVBvaW50IGZvbGRlciB3aXRoIHBhcmFtZXRlcnM6Jywge1xuICAgICAgICBzY2hlZHVsZUlkOiBzY2hlZHVsZS5pZCxcbiAgICAgICAgc2NoZWR1bGVDdXN0b21JZDogc2NoZWR1bGUuY3VzdG9tX2lkLFxuICAgICAgICBzY2hlZHVsZURhdGU6IGZvcm1hdHRlZERhdGUsXG4gICAgICAgIHByb2plY3RDdXN0b21JZDogcHJvamVjdC5jdXN0b21faWQsXG4gICAgICAgIHByb2plY3ROYW1lOiBwcm9qZWN0Lm5hbWUsXG4gICAgICAgIGNsaWVudEN1c3RvbUlkOiBjbGllbnQuY3VzdG9tX2lkLFxuICAgICAgICBjbGllbnROYW1lOiBjbGllbnQubmFtZVxuICAgICAgfSlcbiAgICAgIFxuICAgICAgLy8gQ3JlYXRlIHRoZSBmb2xkZXIgc3RydWN0dXJlXG4gICAgICBhd2FpdCBjcmVhdGVTY2hlZHVsZUZvbGRlcihcbiAgICAgICAgc2NoZWR1bGUuaWQsICAgICAgICAgICAgICAgICAgICAvLyBzY2hlZHVsZUlkXG4gICAgICAgIHNjaGVkdWxlLmN1c3RvbV9pZCwgICAgICAgICAgICAgLy8gc2NoZWR1bGVDdXN0b21JZFxuICAgICAgICBmb3JtYXR0ZWREYXRlLCAgICAgICAgICAgICAgICAgIC8vIHNjaGVkdWxlRGF0ZVxuICAgICAgICBwcm9qZWN0LmN1c3RvbV9pZCwgICAgICAgICAgICAgIC8vIHByb2plY3RDdXN0b21JZFxuICAgICAgICBwcm9qZWN0Lm5hbWUsICAgICAgICAgICAgICAgICAgIC8vIHByb2plY3ROYW1lXG4gICAgICAgIGNsaWVudC5jdXN0b21faWQsICAgICAgICAgICAgICAgLy8gY2xpZW50Q3VzdG9tSWRcbiAgICAgICAgY2xpZW50Lm5hbWUgICAgICAgICAgICAgICAgICAgICAvLyBjbGllbnROYW1lXG4gICAgICApXG4gICAgICBcbiAgICAgIGNvbnNvbGUubG9nKCfinIUgU2hhcmVQb2ludCBmb2xkZXIgY3JlYXRlZCBzdWNjZXNzZnVsbHkgZm9yIHNjaGVkdWxlOicsIHNjaGVkdWxlSWQpXG4gICAgICByZXR1cm4gdHJ1ZVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCfinYwgRmFpbGVkIHRvIGNyZWF0ZSBTaGFyZVBvaW50IGZvbGRlciBmb3Igc2NoZWR1bGU6Jywgc2NoZWR1bGVJZCwgZXJyb3IpXG4gICAgICByZXR1cm4gZmFsc2VcbiAgICB9XG4gIH1cblxuICAvKipcbiAgICogRW5zdXJlcyBTaGFyZVBvaW50IGZvbGRlcnMgYXJlIGNyZWF0ZWQgZm9yIGFsbCBzY2hlZHVsZXMgdGhhdCBkb24ndCBoYXZlIHRoZW1cbiAgICovXG4gIHN0YXRpYyBhc3luYyBlbnN1cmVBbGxTY2hlZHVsZUZvbGRlcnMoKTogUHJvbWlzZTx7IHN1Y2Nlc3M6IG51bWJlcjsgZmFpbGVkOiBudW1iZXI7IGRldGFpbHM6IHN0cmluZ1tdIH0+IHtcbiAgICBjb25zb2xlLmxvZygn8J+agCBTdGFydGluZyBlbnN1cmVBbGxTY2hlZHVsZUZvbGRlcnMuLi4nKVxuICAgIFxuICAgIHRyeSB7XG4gICAgICAvLyBHZXQgYWxsIHNjaGVkdWxlcyB1c2luZyBkaXJlY3QgU3VwYWJhc2UgcXVlcnkgd2l0aCBzZXJ2aWNlIHJvbGVcbiAgICAgIGNvbnNvbGUubG9nKCfwn5OLIEZldGNoaW5nIGFsbCBzY2hlZHVsZXMgd2l0aCBzZXJ2ZXIgY2xpZW50Li4uJylcbiAgICAgIGNvbnN0IHNlcnZlclN1cGFiYXNlID0gY3JlYXRlU2VydmVyU3VwYWJhc2VDbGllbnQoKVxuICAgICAgY29uc3QgeyBkYXRhOiBzY2hlZHVsZXMsIGVycm9yIH0gPSBhd2FpdCBzZXJ2ZXJTdXBhYmFzZVxuICAgICAgICAuZnJvbSgnc2NoZWR1bGVzJylcbiAgICAgICAgLnNlbGVjdChgXG4gICAgICAgICAgaWQsXG4gICAgICAgICAgY3VzdG9tX2lkLFxuICAgICAgICAgIHByb2plY3RfaWQsXG4gICAgICAgICAgc2NoZWR1bGVkX2RhdGVcbiAgICAgICAgYClcbiAgICAgICAgLm9yZGVyKCdzY2hlZHVsZWRfZGF0ZScpXG5cbiAgICAgIGlmIChlcnJvcikge1xuICAgICAgICBjb25zb2xlLmVycm9yKCfinYwgRXJyb3IgZmV0Y2hpbmcgc2NoZWR1bGVzOicsIGVycm9yKVxuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoYEZhaWxlZCB0byBmZXRjaCBzY2hlZHVsZXM6ICR7ZXJyb3IubWVzc2FnZX1gKVxuICAgICAgfVxuXG4gICAgICBjb25zb2xlLmxvZyhg8J+TiiBGb3VuZCAke3NjaGVkdWxlcz8ubGVuZ3RoIHx8IDB9IHNjaGVkdWxlc2ApXG4gICAgICBcbiAgICAgIGlmIChzY2hlZHVsZXMgJiYgc2NoZWR1bGVzLmxlbmd0aCA+IDApIHtcbiAgICAgICAgY29uc29sZS5sb2coJ/CflI0gRmlyc3QgZmV3IHNjaGVkdWxlczonLCBzY2hlZHVsZXMuc2xpY2UoMCwgMykubWFwKChzOiBhbnkpID0+ICh7XG4gICAgICAgICAgaWQ6IHMuaWQsXG4gICAgICAgICAgY3VzdG9tX2lkOiBzLmN1c3RvbV9pZCxcbiAgICAgICAgICBwcm9qZWN0X2lkOiBzLnByb2plY3RfaWQsXG4gICAgICAgICAgc2NoZWR1bGVkX2RhdGU6IHMuc2NoZWR1bGVkX2RhdGVcbiAgICAgICAgfSkpKVxuICAgICAgfVxuXG4gICAgICBsZXQgc3VjY2Vzc0NvdW50ID0gMFxuICAgICAgbGV0IGZhaWxlZENvdW50ID0gMFxuICAgICAgY29uc3QgZGV0YWlsczogc3RyaW5nW10gPSBbXVxuXG4gICAgICBmb3IgKGNvbnN0IHNjaGVkdWxlIG9mIHNjaGVkdWxlcyB8fCBbXSkge1xuICAgICAgICB0cnkge1xuICAgICAgICAgIGNvbnNvbGUubG9nKGDwn5SEIFByb2Nlc3Npbmcgc2NoZWR1bGUgJHtzY2hlZHVsZS5pZH0uLi5gKVxuICAgICAgICAgIGF3YWl0IFNoYXJlUG9pbnRTZXJ2aWNlLmVuc3VyZVNjaGVkdWxlRm9sZGVyKHNjaGVkdWxlLmlkKVxuICAgICAgICAgIHN1Y2Nlc3NDb3VudCsrXG4gICAgICAgICAgZGV0YWlscy5wdXNoKGDinIUgU2NoZWR1bGUgJHtzY2hlZHVsZS5pZH06IEZvbGRlciBlbnN1cmVkYClcbiAgICAgICAgICBjb25zb2xlLmxvZyhg4pyFIFN1Y2Nlc3NmdWxseSBwcm9jZXNzZWQgc2NoZWR1bGUgJHtzY2hlZHVsZS5pZH1gKVxuICAgICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICAgIGZhaWxlZENvdW50KytcbiAgICAgICAgICBjb25zdCBlcnJvck1lc3NhZ2UgPSBlcnJvciBpbnN0YW5jZW9mIEVycm9yID8gZXJyb3IubWVzc2FnZSA6ICdVbmtub3duIGVycm9yJ1xuICAgICAgICAgIGRldGFpbHMucHVzaChg4p2MIFNjaGVkdWxlICR7c2NoZWR1bGUuaWR9OiAke2Vycm9yTWVzc2FnZX1gKVxuICAgICAgICAgIGNvbnNvbGUuZXJyb3IoYOKdjCBGYWlsZWQgdG8gcHJvY2VzcyBzY2hlZHVsZSAke3NjaGVkdWxlLmlkfTpgLCBlcnJvcilcbiAgICAgICAgfVxuXG4gICAgICAgIC8vIFNtYWxsIGRlbGF5IHRvIHByZXZlbnQgQVBJIG92ZXJsb2FkXG4gICAgICAgIGF3YWl0IG5ldyBQcm9taXNlKHJlc29sdmUgPT4gc2V0VGltZW91dChyZXNvbHZlLCAxMDApKVxuICAgICAgfVxuXG4gICAgICBjb25zb2xlLmxvZyhg8J+OryBDb21wbGV0ZWQ6ICR7c3VjY2Vzc0NvdW50fSBzdWNjZXNzZnVsLCAke2ZhaWxlZENvdW50fSBmYWlsZWRgKVxuICAgICAgcmV0dXJuIHsgc3VjY2Vzczogc3VjY2Vzc0NvdW50LCBmYWlsZWQ6IGZhaWxlZENvdW50LCBkZXRhaWxzIH1cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcign8J+SpSBFcnJvciBpbiBlbnN1cmVBbGxTY2hlZHVsZUZvbGRlcnM6JywgZXJyb3IpXG4gICAgICB0aHJvdyBlcnJvclxuICAgIH1cbiAgfVxufSJdLCJuYW1lcyI6WyJjcmVhdGVTY2hlZHVsZUZvbGRlciIsImNyZWF0ZUNsaWVudCIsImZvbGRlcklkQ2FjaGUiLCJNYXAiLCJDQUNIRV9UVEwiLCJjcmVhdGVTZXJ2ZXJTdXBhYmFzZUNsaWVudCIsImNvbnNvbGUiLCJsb2ciLCJwcm9jZXNzIiwiZW52IiwiTkVYVF9QVUJMSUNfU1VQQUJBU0VfVVJMIiwiU1VQQUJBU0VfU0VSVklDRV9ST0xFX0tFWSIsInN1cGFiYXNlVXJsIiwic3VwYWJhc2VTZXJ2aWNlS2V5IiwiZXJyb3IiLCJFcnJvciIsIlNoYXJlUG9pbnRTZXJ2aWNlIiwiZW5zdXJlU2NoZWR1bGVGb2xkZXIiLCJzY2hlZHVsZUlkIiwic2VydmVyU3VwYWJhc2UiLCJkYXRhIiwic2NoZWR1bGUiLCJmcm9tIiwic2VsZWN0IiwiZXEiLCJzaW5nbGUiLCJwcm9qZWN0IiwicHJvamVjdHMiLCJjbGllbnQiLCJjbGllbnRzIiwiY3VzdG9tX2lkIiwibmFtZSIsInNjaGVkdWxlQ3VzdG9tSWQiLCJwcm9qZWN0Q3VzdG9tSWQiLCJwcm9qZWN0TmFtZSIsImNsaWVudEN1c3RvbUlkIiwiY2xpZW50TmFtZSIsInNjaGVkdWxlRGF0ZSIsIkRhdGUiLCJzY2hlZHVsZWRfZGF0ZSIsImlzdERhdGUiLCJ0b0xvY2FsZVN0cmluZyIsInRpbWVab25lIiwiZm9ybWF0dGVkRGF0ZSIsIlN0cmluZyIsImdldEZ1bGxZZWFyIiwiZ2V0TW9udGgiLCJwYWRTdGFydCIsImdldERhdGUiLCJpZCIsImVuc3VyZUFsbFNjaGVkdWxlRm9sZGVycyIsInNjaGVkdWxlcyIsIm9yZGVyIiwibWVzc2FnZSIsImxlbmd0aCIsInNsaWNlIiwibWFwIiwicyIsInByb2plY3RfaWQiLCJzdWNjZXNzQ291bnQiLCJmYWlsZWRDb3VudCIsImRldGFpbHMiLCJwdXNoIiwiZXJyb3JNZXNzYWdlIiwiUHJvbWlzZSIsInJlc29sdmUiLCJzZXRUaW1lb3V0Iiwic3VjY2VzcyIsImZhaWxlZCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/sharepoint-service.ts\n"));

/***/ })

}]);