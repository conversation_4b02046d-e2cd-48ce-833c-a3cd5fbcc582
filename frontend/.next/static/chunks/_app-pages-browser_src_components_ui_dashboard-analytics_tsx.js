"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_src_components_ui_dashboard-analytics_tsx"],{

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-column.js":
/*!******************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/chart-column.js ***!
  \******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ ChartColumn)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M3 3v16a2 2 0 0 0 2 2h16\",\n            key: \"c24i48\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M18 17V9\",\n            key: \"2bz60n\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M13 17V5\",\n            key: \"1frdt8\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M8 17v-3\",\n            key: \"17ska0\"\n        }\n    ]\n];\nconst ChartColumn = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"chart-column\", __iconNode);\n //# sourceMappingURL=chart-column.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-column.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-pie.js":
/*!***************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/chart-pie.js ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ ChartPie)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M21 12c.552 0 1.005-.449.95-.998a10 10 0 0 0-8.953-8.951c-.55-.055-.998.398-.998.95v8a1 1 0 0 0 1 1z\",\n            key: \"pzmjnu\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M21.21 15.89A10 10 0 1 1 8 2.83\",\n            key: \"k2fpak\"\n        }\n    ]\n];\nconst ChartPie = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"chart-pie\", __iconNode);\n //# sourceMappingURL=chart-pie.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-pie.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js":
/*!************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/target.js ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ Target)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"circle\",\n        {\n            cx: \"12\",\n            cy: \"12\",\n            r: \"10\",\n            key: \"1mglay\"\n        }\n    ],\n    [\n        \"circle\",\n        {\n            cx: \"12\",\n            cy: \"12\",\n            r: \"6\",\n            key: \"1vlfrh\"\n        }\n    ],\n    [\n        \"circle\",\n        {\n            cx: \"12\",\n            cy: \"12\",\n            r: \"2\",\n            key: \"1c9p78\"\n        }\n    ]\n];\nconst Target = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"target\", __iconNode);\n //# sourceMappingURL=target.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ui/dashboard-analytics.tsx":
/*!***************************************************!*\
  !*** ./src/components/ui/dashboard-analytics.tsx ***!
  \***************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DashboardAnalytics: () => (/* binding */ DashboardAnalytics)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Calendar_CheckCircle_Clock_DollarSign_PieChart_Target_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Calendar,CheckCircle,Clock,DollarSign,PieChart,Target,TrendingDown,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Calendar_CheckCircle_Clock_DollarSign_PieChart_Target_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Calendar,CheckCircle,Clock,DollarSign,PieChart,Target,TrendingDown,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-down.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Calendar_CheckCircle_Clock_DollarSign_PieChart_Target_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Calendar,CheckCircle,Clock,DollarSign,PieChart,Target,TrendingDown,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Calendar_CheckCircle_Clock_DollarSign_PieChart_Target_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Calendar,CheckCircle,Clock,DollarSign,PieChart,Target,TrendingDown,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Calendar_CheckCircle_Clock_DollarSign_PieChart_Target_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Calendar,CheckCircle,Clock,DollarSign,PieChart,Target,TrendingDown,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Calendar_CheckCircle_Clock_DollarSign_PieChart_Target_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Calendar,CheckCircle,Clock,DollarSign,PieChart,Target,TrendingDown,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Calendar_CheckCircle_Clock_DollarSign_PieChart_Target_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Calendar,CheckCircle,Clock,DollarSign,PieChart,Target,TrendingDown,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Calendar_CheckCircle_Clock_DollarSign_PieChart_Target_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Calendar,CheckCircle,Clock,DollarSign,PieChart,Target,TrendingDown,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Calendar_CheckCircle_Clock_DollarSign_PieChart_Target_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Calendar,CheckCircle,Clock,DollarSign,PieChart,Target,TrendingDown,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Calendar_CheckCircle_Clock_DollarSign_PieChart_Target_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Calendar,CheckCircle,Clock,DollarSign,PieChart,Target,TrendingDown,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-pie.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Calendar_CheckCircle_Clock_DollarSign_PieChart_Target_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Calendar,CheckCircle,Clock,DollarSign,PieChart,Target,TrendingDown,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* __next_internal_client_entry_do_not_use__ DashboardAnalytics auto */ \nvar _s = $RefreshSig$();\n\n\nfunction DashboardAnalytics(param) {\n    let { data, timeRange, onTimeRangeChange } = param;\n    _s();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('overview');\n    const getGrowthColor = (growth)=>{\n        if (growth > 0) return 'text-green-600 dark:text-green-400';\n        if (growth < 0) return 'text-red-600 dark:text-red-400';\n        return 'text-muted-foreground';\n    };\n    const getGrowthIcon = (growth)=>{\n        if (growth > 0) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Calendar_CheckCircle_Clock_DollarSign_PieChart_Target_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n            className: \"w-4 h-4\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n            lineNumber: 60,\n            columnNumber: 28\n        }, this);\n        if (growth < 0) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Calendar_CheckCircle_Clock_DollarSign_PieChart_Target_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            className: \"w-4 h-4\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n            lineNumber: 61,\n            columnNumber: 28\n        }, this);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Calendar_CheckCircle_Clock_DollarSign_PieChart_Target_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n            className: \"w-4 h-4\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n            lineNumber: 62,\n            columnNumber: 12\n        }, this);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-card rounded-lg shadow border border-border\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-6 border-b border-border\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-2 bg-primary/10 rounded-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Calendar_CheckCircle_Clock_DollarSign_PieChart_Target_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"w-6 h-6 text-primary\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                        lineNumber: 72,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                    lineNumber: 71,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-foreground\",\n                                            children: \"Business Analytics\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                            lineNumber: 75,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-muted-foreground\",\n                                            children: \"Comprehensive insights into your drone service business\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                            lineNumber: 76,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                    lineNumber: 74,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                            lineNumber: 70,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-1 bg-muted rounded-lg p-1\",\n                            children: [\n                                {\n                                    id: 'week',\n                                    label: 'Week'\n                                },\n                                {\n                                    id: 'month',\n                                    label: 'Month'\n                                },\n                                {\n                                    id: 'quarter',\n                                    label: 'Quarter'\n                                }\n                            ].map((range)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>onTimeRangeChange(range.id),\n                                    className: \"px-3 py-1 rounded-md text-sm font-medium transition-colors \".concat(timeRange === range.id ? 'bg-background text-foreground shadow' : 'text-muted-foreground hover:text-foreground'),\n                                    children: range.label\n                                }, range.id, false, {\n                                    fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                    lineNumber: 87,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                            lineNumber: 81,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                    lineNumber: 69,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                lineNumber: 68,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-b border-border\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex space-x-8 px-6\",\n                    children: [\n                        {\n                            id: 'overview',\n                            label: 'Overview',\n                            icon: _barrel_optimize_names_Activity_BarChart3_Calendar_CheckCircle_Clock_DollarSign_PieChart_Target_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"]\n                        },\n                        {\n                            id: 'revenue',\n                            label: 'Revenue',\n                            icon: _barrel_optimize_names_Activity_BarChart3_Calendar_CheckCircle_Clock_DollarSign_PieChart_Target_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n                        },\n                        {\n                            id: 'projects',\n                            label: 'Projects',\n                            icon: _barrel_optimize_names_Activity_BarChart3_Calendar_CheckCircle_Clock_DollarSign_PieChart_Target_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n                        },\n                        {\n                            id: 'performance',\n                            label: 'Performance',\n                            icon: _barrel_optimize_names_Activity_BarChart3_Calendar_CheckCircle_Clock_DollarSign_PieChart_Target_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"]\n                        }\n                    ].map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setActiveTab(tab.id),\n                            className: \"flex items-center space-x-2 py-4 border-b-2 transition-colors \".concat(activeTab === tab.id ? 'border-primary text-primary' : 'border-transparent text-muted-foreground hover:text-foreground'),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tab.icon, {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                    lineNumber: 121,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"font-medium\",\n                                    children: tab.label\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                    lineNumber: 122,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, tab.id, true, {\n                            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                            lineNumber: 112,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                    lineNumber: 105,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                lineNumber: 104,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-6\",\n                children: [\n                    activeTab === 'overview' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-primary/10 border border-primary/20 rounded-lg p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Calendar_CheckCircle_Clock_DollarSign_PieChart_Target_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                className: \"w-8 h-8 text-primary\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                lineNumber: 134,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-1 \".concat(getGrowthColor(data.revenue.growth)),\n                                                children: [\n                                                    getGrowthIcon(data.revenue.growth),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium\",\n                                                        children: [\n                                                            Math.abs(data.revenue.growth),\n                                                            \"%\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                        lineNumber: 137,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                lineNumber: 135,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                        lineNumber: 133,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-foreground\",\n                                        children: [\n                                            \"₹\",\n                                            data.revenue.total.toLocaleString()\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                        lineNumber: 140,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-muted-foreground\",\n                                        children: \"Total Revenue\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                        lineNumber: 141,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                lineNumber: 132,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-green-600/10 border border-green-600/20 rounded-lg p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Calendar_CheckCircle_Clock_DollarSign_PieChart_Target_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"w-8 h-8 text-green-600\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                lineNumber: 146,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-green-600\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Calendar_CheckCircle_Clock_DollarSign_PieChart_Target_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"w-5 h-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                    lineNumber: 148,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                lineNumber: 147,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                        lineNumber: 145,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-foreground\",\n                                        children: data.projects.completed\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                        lineNumber: 151,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-muted-foreground\",\n                                        children: \"Completed Projects\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                        lineNumber: 152,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                lineNumber: 144,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-purple-600/10 border border-purple-600/20 rounded-lg p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Calendar_CheckCircle_Clock_DollarSign_PieChart_Target_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"w-8 h-8 text-purple-600\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                lineNumber: 157,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-purple-600\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium\",\n                                                    children: [\n                                                        data.clients.retention,\n                                                        \"%\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                    lineNumber: 159,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                lineNumber: 158,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                        lineNumber: 156,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-foreground\",\n                                        children: data.clients.active\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                        lineNumber: 162,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-muted-foreground\",\n                                        children: \"Active Clients\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                        lineNumber: 163,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                lineNumber: 155,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-orange-600/10 border border-orange-600/20 rounded-lg p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Calendar_CheckCircle_Clock_DollarSign_PieChart_Target_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"w-8 h-8 text-orange-600\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                lineNumber: 168,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-orange-600\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium\",\n                                                    children: [\n                                                        data.performance.efficiency,\n                                                        \"%\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                    lineNumber: 170,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                lineNumber: 169,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                        lineNumber: 167,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-foreground\",\n                                        children: data.performance.avgCompletionTime\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                        lineNumber: 173,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-muted-foreground\",\n                                        children: \"Avg. Days to Complete\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                        lineNumber: 174,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                lineNumber: 166,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                        lineNumber: 131,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === 'revenue' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-green-600/10 border border-green-600/20 rounded-lg p-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2 mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Calendar_CheckCircle_Clock_DollarSign_PieChart_Target_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                        className: \"w-5 h-5 text-green-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                        lineNumber: 184,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium text-foreground\",\n                                                        children: \"Monthly Revenue\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                        lineNumber: 185,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                lineNumber: 183,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xl font-bold text-foreground\",\n                                                children: [\n                                                    \"₹\",\n                                                    data.revenue.monthly.toLocaleString()\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                lineNumber: 187,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm flex items-center space-x-1 \".concat(getGrowthColor(data.revenue.growth)),\n                                                children: [\n                                                    getGrowthIcon(data.revenue.growth),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            Math.abs(data.revenue.growth),\n                                                            \"% vs last \",\n                                                            timeRange\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                        lineNumber: 190,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                lineNumber: 188,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                        lineNumber: 182,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-primary/10 border border-primary/20 rounded-lg p-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2 mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Calendar_CheckCircle_Clock_DollarSign_PieChart_Target_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        className: \"w-5 h-5 text-primary\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                        lineNumber: 196,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium text-foreground\",\n                                                        children: \"Avg. Project Value\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                        lineNumber: 197,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                lineNumber: 195,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xl font-bold text-foreground\",\n                                                children: [\n                                                    \"₹\",\n                                                    data.performance.avgProjectValue.toLocaleString()\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                lineNumber: 199,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-muted-foreground\",\n                                                children: \"Per project average\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                lineNumber: 200,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                        lineNumber: 194,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-purple-600/10 border border-purple-600/20 rounded-lg p-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2 mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Calendar_CheckCircle_Clock_DollarSign_PieChart_Target_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"w-5 h-5 text-purple-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                        lineNumber: 205,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium text-foreground\",\n                                                        children: \"Revenue Sources\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                        lineNumber: 206,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                lineNumber: 204,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between text-sm\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-muted-foreground\",\n                                                                children: \"Real Estate\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                                lineNumber: 210,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium text-foreground\",\n                                                                children: \"65%\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                                lineNumber: 211,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                        lineNumber: 209,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between text-sm\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-muted-foreground\",\n                                                                children: \"Events\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                                lineNumber: 214,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium text-foreground\",\n                                                                children: \"25%\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                                lineNumber: 215,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                        lineNumber: 213,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between text-sm\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-muted-foreground\",\n                                                                children: \"Commercial\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                                lineNumber: 218,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium text-foreground\",\n                                                                children: \"10%\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                                lineNumber: 219,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                        lineNumber: 217,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                lineNumber: 208,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                        lineNumber: 203,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                lineNumber: 181,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-muted/50 border border-border rounded-lg p-6 text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Calendar_CheckCircle_Clock_DollarSign_PieChart_Target_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"w-12 h-12 text-muted-foreground mx-auto mb-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                        lineNumber: 227,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"text-lg font-medium text-foreground mb-2\",\n                                        children: \"Revenue Trends\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                        lineNumber: 228,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-muted-foreground\",\n                                        children: \"Interactive revenue chart would be displayed here\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                        lineNumber: 229,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                lineNumber: 226,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                        lineNumber: 180,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === 'projects' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-card border border-border rounded-lg p-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"font-medium text-foreground mb-4\",\n                                                children: \"Project Status Distribution\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                lineNumber: 238,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-3 h-3 bg-green-500 rounded-full\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                                        lineNumber: 242,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm text-muted-foreground\",\n                                                                        children: \"Completed\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                                        lineNumber: 243,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                                lineNumber: 241,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium text-foreground\",\n                                                                children: data.projects.completed\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                                lineNumber: 245,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                        lineNumber: 240,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-3 h-3 bg-blue-500 rounded-full\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                                        lineNumber: 249,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm text-muted-foreground\",\n                                                                        children: \"Active\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                                        lineNumber: 250,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                                lineNumber: 248,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium text-foreground\",\n                                                                children: data.projects.active\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                                lineNumber: 252,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                        lineNumber: 247,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-3 h-3 bg-gray-400 rounded-full\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                                        lineNumber: 256,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm text-muted-foreground\",\n                                                                        children: \"Total\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                                        lineNumber: 257,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                                lineNumber: 255,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium text-foreground\",\n                                                                children: data.projects.total\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                                lineNumber: 259,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                        lineNumber: 254,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                lineNumber: 239,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                        lineNumber: 237,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-card border border-border rounded-lg p-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"font-medium text-foreground mb-4\",\n                                                children: \"Performance Metrics\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                lineNumber: 265,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-muted-foreground\",\n                                                                children: \"Completion Rate\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                                lineNumber: 268,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium text-green-600\",\n                                                                children: [\n                                                                    data.projects.completionRate,\n                                                                    \"%\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                                lineNumber: 269,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                        lineNumber: 267,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-muted-foreground\",\n                                                                children: \"Avg. Completion Time\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                                lineNumber: 272,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium text-foreground\",\n                                                                children: [\n                                                                    data.performance.avgCompletionTime,\n                                                                    \" days\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                                lineNumber: 273,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                        lineNumber: 271,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-muted-foreground\",\n                                                                children: \"Efficiency Score\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                                lineNumber: 276,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium text-primary\",\n                                                                children: [\n                                                                    data.performance.efficiency,\n                                                                    \"%\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                                lineNumber: 277,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                        lineNumber: 275,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                lineNumber: 266,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                        lineNumber: 264,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                lineNumber: 236,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-muted/50 border border-border rounded-lg p-6 text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Calendar_CheckCircle_Clock_DollarSign_PieChart_Target_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"w-12 h-12 text-muted-foreground mx-auto mb-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                        lineNumber: 285,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"text-lg font-medium text-foreground mb-2\",\n                                        children: \"Project Timeline\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                        lineNumber: 286,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-muted-foreground\",\n                                        children: \"Project timeline and milestones would be displayed here\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                        lineNumber: 287,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                lineNumber: 284,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                        lineNumber: 235,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === 'performance' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center p-4 border border-border rounded-lg bg-card\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold text-foreground\",\n                                                children: [\n                                                    data.performance.efficiency,\n                                                    \"%\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                lineNumber: 296,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-muted-foreground\",\n                                                children: \"Overall Efficiency\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                lineNumber: 297,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                        lineNumber: 295,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center p-4 border border-border rounded-lg bg-card\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold text-foreground\",\n                                                children: [\n                                                    data.clients.retention,\n                                                    \"%\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                lineNumber: 300,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-muted-foreground\",\n                                                children: \"Client Retention\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                lineNumber: 301,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                        lineNumber: 299,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center p-4 border border-border rounded-lg bg-card\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold text-foreground\",\n                                                children: data.performance.avgCompletionTime\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                lineNumber: 304,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-muted-foreground\",\n                                                children: \"Avg. Days to Complete\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                lineNumber: 305,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                        lineNumber: 303,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center p-4 border border-border rounded-lg bg-card\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold text-foreground\",\n                                                children: [\n                                                    \"₹\",\n                                                    data.performance.avgProjectValue.toLocaleString()\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                lineNumber: 308,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-muted-foreground\",\n                                                children: \"Avg. Project Value\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                lineNumber: 309,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                        lineNumber: 307,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                lineNumber: 294,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-primary/10 border border-primary/20 rounded-lg p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"font-medium text-primary mb-3\",\n                                        children: \"Performance Insights\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                        lineNumber: 315,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2 text-sm text-muted-foreground\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Calendar_CheckCircle_Clock_DollarSign_PieChart_Target_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        className: \"w-4 h-4 text-primary\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                        lineNumber: 318,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Project completion rate is above industry average\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                        lineNumber: 319,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                lineNumber: 317,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Calendar_CheckCircle_Clock_DollarSign_PieChart_Target_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                        className: \"w-4 h-4 text-primary\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                        lineNumber: 322,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            \"Revenue growth trending upward this \",\n                                                            timeRange\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                        lineNumber: 323,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                lineNumber: 321,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Calendar_CheckCircle_Clock_DollarSign_PieChart_Target_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"w-4 h-4 text-primary\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                        lineNumber: 326,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Client retention rate indicates strong satisfaction\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                        lineNumber: 327,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                lineNumber: 325,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                        lineNumber: 316,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                lineNumber: 314,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                        lineNumber: 293,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                lineNumber: 129,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n        lineNumber: 66,\n        columnNumber: 5\n    }, this);\n}\n_s(DashboardAnalytics, \"chU/96YDf6tpLdqvgqS2ZL+A4Bo=\");\n_c = DashboardAnalytics;\nvar _c;\n$RefreshReg$(_c, \"DashboardAnalytics\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/dashboard-analytics.tsx\n"));

/***/ })

}]);