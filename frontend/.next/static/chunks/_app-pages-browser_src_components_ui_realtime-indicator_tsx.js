"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_src_components_ui_realtime-indicator_tsx"],{

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js":
/*!**************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/activity.js ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ Activity)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M22 12h-2.48a2 2 0 0 0-1.93 1.46l-2.35 8.36a.25.25 0 0 1-.48 0L9.24 2.18a.25.25 0 0 0-.48 0l-2.35 8.36A2 2 0 0 1 4.49 12H2\",\n            key: \"169zse\"\n        }\n    ]\n];\nconst Activity = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"activity\", __iconNode);\n //# sourceMappingURL=activity.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wifi-off.js":
/*!**************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/wifi-off.js ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ WifiOff)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M12 20h.01\",\n            key: \"zekei9\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M8.5 16.429a5 5 0 0 1 7 0\",\n            key: \"1bycff\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M5 12.859a10 10 0 0 1 5.17-2.69\",\n            key: \"1dl1wf\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M19 12.859a10 10 0 0 0-2.007-1.523\",\n            key: \"4k23kn\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M2 8.82a15 15 0 0 1 4.177-2.643\",\n            key: \"1grhjp\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M22 8.82a15 15 0 0 0-11.288-3.764\",\n            key: \"z3jwby\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"m2 2 20 20\",\n            key: \"1ooewy\"\n        }\n    ]\n];\nconst WifiOff = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"wifi-off\", __iconNode);\n //# sourceMappingURL=wifi-off.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wifi-off.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wifi.js":
/*!**********************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/wifi.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ Wifi)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M12 20h.01\",\n            key: \"zekei9\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M2 8.82a15 15 0 0 1 20 0\",\n            key: \"dnpr2z\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M5 12.859a10 10 0 0 1 14 0\",\n            key: \"1x1e6c\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M8.5 16.429a5 5 0 0 1 7 0\",\n            key: \"1bycff\"\n        }\n    ]\n];\nconst Wifi = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"wifi\", __iconNode);\n //# sourceMappingURL=wifi.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wifi.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js":
/*!*********************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/zap.js ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ Zap)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z\",\n            key: \"1xq2db\"\n        }\n    ]\n];\nconst Zap = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"zap\", __iconNode);\n //# sourceMappingURL=zap.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ui/realtime-indicator.tsx":
/*!**************************************************!*\
  !*** ./src/components/ui/realtime-indicator.tsx ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RealtimeIndicator: () => (/* binding */ RealtimeIndicator),\n/* harmony export */   useRealtimeStatus: () => (/* binding */ useRealtimeStatus)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/auth */ \"(app-pages-browser)/./src/lib/auth.ts\");\n/* harmony import */ var _barrel_optimize_names_Activity_Wifi_WifiOff_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Wifi,WifiOff,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wifi-off.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Wifi_WifiOff_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Wifi,WifiOff,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wifi.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Wifi_WifiOff_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Wifi,WifiOff,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Wifi_WifiOff_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Wifi,WifiOff,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ RealtimeIndicator,useRealtimeStatus auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\nfunction RealtimeIndicator(param) {\n    let { className, showText = false, size = 'sm' } = param;\n    _s();\n    const [isConnected, setIsConnected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isOnline, setIsOnline] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [lastActivity, setLastActivity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const supabase = (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.createClientSupabaseClient)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"RealtimeIndicator.useEffect\": ()=>{\n            // Check initial online status\n            setIsOnline(navigator.onLine);\n            // Listen for online/offline events\n            const handleOnline = {\n                \"RealtimeIndicator.useEffect.handleOnline\": ()=>setIsOnline(true)\n            }[\"RealtimeIndicator.useEffect.handleOnline\"];\n            const handleOffline = {\n                \"RealtimeIndicator.useEffect.handleOffline\": ()=>setIsOnline(false)\n            }[\"RealtimeIndicator.useEffect.handleOffline\"];\n            window.addEventListener('online', handleOnline);\n            window.addEventListener('offline', handleOffline);\n            // Set up Supabase connection monitoring\n            const channel = supabase.channel('connection-monitor').on('presence', {\n                event: 'sync'\n            }, {\n                \"RealtimeIndicator.useEffect.channel\": ()=>{\n                    setIsConnected(true);\n                    setLastActivity(new Date());\n                }\n            }[\"RealtimeIndicator.useEffect.channel\"]).on('presence', {\n                event: 'join'\n            }, {\n                \"RealtimeIndicator.useEffect.channel\": ()=>{\n                    setIsConnected(true);\n                    setLastActivity(new Date());\n                }\n            }[\"RealtimeIndicator.useEffect.channel\"]).on('presence', {\n                event: 'leave'\n            }, {\n                \"RealtimeIndicator.useEffect.channel\": ()=>{\n                    setIsConnected(false);\n                }\n            }[\"RealtimeIndicator.useEffect.channel\"]).subscribe({\n                \"RealtimeIndicator.useEffect.channel\": (status)=>{\n                    if (status === 'SUBSCRIBED') {\n                        setIsConnected(true);\n                        setLastActivity(new Date());\n                    } else if (status === 'CHANNEL_ERROR' || status === 'TIMED_OUT') {\n                        setIsConnected(false);\n                    }\n                }\n            }[\"RealtimeIndicator.useEffect.channel\"]);\n            // Monitor real-time activity with a test channel\n            const activityChannel = supabase.channel('activity-monitor').on('broadcast', {\n                event: 'ping'\n            }, {\n                \"RealtimeIndicator.useEffect.activityChannel\": ()=>{\n                    setLastActivity(new Date());\n                }\n            }[\"RealtimeIndicator.useEffect.activityChannel\"]).subscribe();\n            // Send periodic pings to test connectivity\n            const pingInterval = setInterval({\n                \"RealtimeIndicator.useEffect.pingInterval\": ()=>{\n                    if (isOnline) {\n                        activityChannel.send({\n                            type: 'broadcast',\n                            event: 'ping',\n                            payload: {\n                                timestamp: new Date().toISOString()\n                            }\n                        });\n                    }\n                }\n            }[\"RealtimeIndicator.useEffect.pingInterval\"], 30000) // Ping every 30 seconds\n            ;\n            return ({\n                \"RealtimeIndicator.useEffect\": ()=>{\n                    window.removeEventListener('online', handleOnline);\n                    window.removeEventListener('offline', handleOffline);\n                    clearInterval(pingInterval);\n                    supabase.removeChannel(channel);\n                    supabase.removeChannel(activityChannel);\n                }\n            })[\"RealtimeIndicator.useEffect\"];\n        }\n    }[\"RealtimeIndicator.useEffect\"], [\n        supabase,\n        isOnline\n    ]);\n    const getStatusColor = ()=>{\n        if (!isOnline) return 'text-red-500';\n        if (!isConnected) return 'text-yellow-500';\n        return 'text-green-500';\n    };\n    const getStatusText = ()=>{\n        if (!isOnline) return 'Offline';\n        if (!isConnected) return 'Connecting...';\n        return 'Real-time';\n    };\n    const getIcon = ()=>{\n        if (!isOnline) return _barrel_optimize_names_Activity_Wifi_WifiOff_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"];\n        if (!isConnected) return _barrel_optimize_names_Activity_Wifi_WifiOff_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"];\n        return _barrel_optimize_names_Activity_Wifi_WifiOff_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"];\n    };\n    const getSizeClasses = ()=>{\n        switch(size){\n            case 'sm':\n                return 'w-3 h-3';\n            case 'md':\n                return 'w-4 h-4';\n            case 'lg':\n                return 'w-5 h-5';\n            default:\n                return 'w-3 h-3';\n        }\n    };\n    const Icon = getIcon();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)('flex items-center space-x-1', className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(getSizeClasses(), getStatusColor(), isConnected && isOnline && 'animate-pulse')\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/realtime-indicator.tsx\",\n                        lineNumber: 125,\n                        columnNumber: 9\n                    }, this),\n                    isConnected && isOnline && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)('absolute inset-0 rounded-full animate-ping', getSizeClasses(), 'bg-green-400 opacity-20')\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/realtime-indicator.tsx\",\n                        lineNumber: 133,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/realtime-indicator.tsx\",\n                lineNumber: 124,\n                columnNumber: 7\n            }, this),\n            showText && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)('text-xs font-medium', getStatusColor()),\n                children: getStatusText()\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/realtime-indicator.tsx\",\n                lineNumber: 142,\n                columnNumber: 9\n            }, this),\n            lastActivity && isConnected && isOnline && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-1\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Wifi_WifiOff_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        className: \"w-2 h-2 text-green-400\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/realtime-indicator.tsx\",\n                        lineNumber: 153,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-xs text-gray-500\",\n                        children: lastActivity.toLocaleTimeString([], {\n                            hour: '2-digit',\n                            minute: '2-digit'\n                        })\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/realtime-indicator.tsx\",\n                        lineNumber: 154,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/realtime-indicator.tsx\",\n                lineNumber: 152,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/realtime-indicator.tsx\",\n        lineNumber: 120,\n        columnNumber: 5\n    }, this);\n}\n_s(RealtimeIndicator, \"zvTu2NpUoC79CHYFMwvMZLKsZfA=\");\n_c = RealtimeIndicator;\n// Hook to get real-time connection status\nfunction useRealtimeStatus() {\n    _s1();\n    const [isConnected, setIsConnected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isOnline, setIsOnline] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [lastActivity, setLastActivity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const supabase = (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.createClientSupabaseClient)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"useRealtimeStatus.useEffect\": ()=>{\n            setIsOnline(navigator.onLine);\n            const handleOnline = {\n                \"useRealtimeStatus.useEffect.handleOnline\": ()=>setIsOnline(true)\n            }[\"useRealtimeStatus.useEffect.handleOnline\"];\n            const handleOffline = {\n                \"useRealtimeStatus.useEffect.handleOffline\": ()=>setIsOnline(false)\n            }[\"useRealtimeStatus.useEffect.handleOffline\"];\n            window.addEventListener('online', handleOnline);\n            window.addEventListener('offline', handleOffline);\n            const channel = supabase.channel('status-monitor').on('presence', {\n                event: 'sync'\n            }, {\n                \"useRealtimeStatus.useEffect.channel\": ()=>{\n                    setIsConnected(true);\n                    setLastActivity(new Date());\n                }\n            }[\"useRealtimeStatus.useEffect.channel\"]).subscribe({\n                \"useRealtimeStatus.useEffect.channel\": (status)=>{\n                    setIsConnected(status === 'SUBSCRIBED');\n                    if (status === 'SUBSCRIBED') {\n                        setLastActivity(new Date());\n                    }\n                }\n            }[\"useRealtimeStatus.useEffect.channel\"]);\n            return ({\n                \"useRealtimeStatus.useEffect\": ()=>{\n                    window.removeEventListener('online', handleOnline);\n                    window.removeEventListener('offline', handleOffline);\n                    supabase.removeChannel(channel);\n                }\n            })[\"useRealtimeStatus.useEffect\"];\n        }\n    }[\"useRealtimeStatus.useEffect\"], [\n        supabase\n    ]);\n    return {\n        isConnected,\n        isOnline,\n        lastActivity,\n        status: !isOnline ? 'offline' : !isConnected ? 'connecting' : 'connected'\n    };\n}\n_s1(useRealtimeStatus, \"zvTu2NpUoC79CHYFMwvMZLKsZfA=\");\nvar _c;\n$RefreshReg$(_c, \"RealtimeIndicator\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/realtime-indicator.tsx\n"));

/***/ })

}]);