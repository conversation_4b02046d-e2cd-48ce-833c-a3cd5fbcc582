"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_src_lib_default-tasks_ts"],{

/***/ "(app-pages-browser)/./src/lib/default-tasks.ts":
/*!**********************************!*\
  !*** ./src/lib/default-tasks.ts ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DEFAULT_TASK_TEMPLATES: () => (/* binding */ DEFAULT_TASK_TEMPLATES),\n/* harmony export */   createTasksFromTemplates: () => (/* binding */ createTasksFromTemplates),\n/* harmony export */   getClientTypesWithDefaultTasks: () => (/* binding */ getClientTypesWithDefaultTasks),\n/* harmony export */   getDefaultTasksForClientType: () => (/* binding */ getDefaultTasksForClientType),\n/* harmony export */   getDefaultTasksForScheduleType: () => (/* binding */ getDefaultTasksForScheduleType)\n/* harmony export */ });\n// Default task templates for each schedule type (based on requirements/tasks.md)\nconst DEFAULT_TASK_TEMPLATES = {\n    // Wedding, Movie, Surveillance, Events, News, Collaboration\n    Wedding: [\n        {\n            title: 'Shoot',\n            description: 'Conduct the wedding shoot',\n            assigned_role: 'pilot',\n            priority: 'high',\n            order: 1,\n            isProjectTask: false\n        },\n        {\n            title: 'File Upload',\n            description: 'Upload captured files to storage',\n            assigned_role: 'pilot',\n            priority: 'medium',\n            order: 2,\n            dueDaysAfterShoot: 1,\n            isProjectTask: false\n        },\n        {\n            title: 'File Backup',\n            description: 'Create backup of all files',\n            assigned_role: 'pilot',\n            priority: 'medium',\n            order: 3,\n            dueDaysAfterShoot: 1,\n            isProjectTask: false\n        },\n        // Project-level tasks (created once per project)\n        {\n            title: 'Deliver Files',\n            description: 'Deliver final files to client',\n            assigned_role: 'admin',\n            priority: 'medium',\n            order: 100,\n            dueDaysAfterShoot: 7,\n            isProjectTask: true\n        },\n        {\n            title: 'Payment Collect',\n            description: 'Collect payment from client',\n            assigned_role: 'admin',\n            priority: 'high',\n            order: 101,\n            dueDaysAfterTask: 'Deliver Files',\n            dueDaysAfterShoot: 7,\n            isProjectTask: true\n        }\n    ],\n    Movie: [\n        {\n            title: 'Shoot',\n            description: 'Conduct the movie shoot',\n            assigned_role: 'pilot',\n            priority: 'high',\n            order: 1,\n            isProjectTask: false\n        },\n        {\n            title: 'File Upload',\n            description: 'Upload captured files to storage',\n            assigned_role: 'pilot',\n            priority: 'medium',\n            order: 2,\n            dueDaysAfterShoot: 1,\n            isProjectTask: false\n        },\n        {\n            title: 'File Backup',\n            description: 'Create backup of all files',\n            assigned_role: 'pilot',\n            priority: 'medium',\n            order: 3,\n            dueDaysAfterShoot: 1,\n            isProjectTask: false\n        },\n        // Project-level tasks (created once per project)\n        {\n            title: 'Deliver Files',\n            description: 'Deliver final files to client',\n            assigned_role: 'admin',\n            priority: 'medium',\n            order: 100,\n            dueDaysAfterShoot: 7,\n            isProjectTask: true\n        },\n        {\n            title: 'Payment Collect',\n            description: 'Collect payment from client',\n            assigned_role: 'admin',\n            priority: 'high',\n            order: 101,\n            dueDaysAfterTask: 'Deliver Files',\n            dueDaysAfterShoot: 7,\n            isProjectTask: true\n        }\n    ],\n    Surveillance: [\n        {\n            title: 'Shoot',\n            description: 'Conduct the surveillance shoot',\n            assigned_role: 'pilot',\n            priority: 'high',\n            order: 1,\n            isProjectTask: false\n        },\n        {\n            title: 'File Upload',\n            description: 'Upload captured files to storage',\n            assigned_role: 'pilot',\n            priority: 'medium',\n            order: 2,\n            dueDaysAfterShoot: 1,\n            isProjectTask: false\n        },\n        {\n            title: 'File Backup',\n            description: 'Create backup of all files',\n            assigned_role: 'pilot',\n            priority: 'medium',\n            order: 3,\n            dueDaysAfterShoot: 1,\n            isProjectTask: false\n        },\n        // Project-level tasks (created once per project)\n        {\n            title: 'Deliver Files',\n            description: 'Deliver final files to client',\n            assigned_role: 'admin',\n            priority: 'medium',\n            order: 100,\n            dueDaysAfterShoot: 7,\n            isProjectTask: true\n        },\n        {\n            title: 'Payment Collect',\n            description: 'Collect payment from client',\n            assigned_role: 'admin',\n            priority: 'high',\n            order: 101,\n            dueDaysAfterTask: 'Deliver Files',\n            dueDaysAfterShoot: 7,\n            isProjectTask: true\n        }\n    ],\n    Event: [\n        {\n            title: 'Shoot',\n            description: 'Conduct the event shoot',\n            assigned_role: 'pilot',\n            priority: 'high',\n            order: 1,\n            isProjectTask: false\n        },\n        {\n            title: 'File Upload',\n            description: 'Upload captured files to storage',\n            assigned_role: 'pilot',\n            priority: 'medium',\n            order: 2,\n            dueDaysAfterShoot: 1,\n            isProjectTask: false\n        },\n        {\n            title: 'File Backup',\n            description: 'Create backup of all files',\n            assigned_role: 'pilot',\n            priority: 'medium',\n            order: 3,\n            dueDaysAfterShoot: 1,\n            isProjectTask: false\n        },\n        // Project-level tasks (created once per project)\n        {\n            title: 'Deliver Files',\n            description: 'Deliver final files to client',\n            assigned_role: 'admin',\n            priority: 'medium',\n            order: 100,\n            dueDaysAfterShoot: 7,\n            isProjectTask: true\n        },\n        {\n            title: 'Payment Collect',\n            description: 'Collect payment from client',\n            assigned_role: 'admin',\n            priority: 'high',\n            order: 101,\n            dueDaysAfterTask: 'Deliver Files',\n            dueDaysAfterShoot: 7,\n            isProjectTask: true\n        }\n    ],\n    News: [\n        {\n            title: 'Shoot',\n            description: 'Conduct the news shoot',\n            assigned_role: 'pilot',\n            priority: 'high',\n            order: 1,\n            isProjectTask: false\n        },\n        {\n            title: 'File Upload',\n            description: 'Upload captured files to storage',\n            assigned_role: 'pilot',\n            priority: 'medium',\n            order: 2,\n            dueDaysAfterShoot: 1,\n            isProjectTask: false\n        },\n        {\n            title: 'File Backup',\n            description: 'Create backup of all files',\n            assigned_role: 'pilot',\n            priority: 'medium',\n            order: 3,\n            dueDaysAfterShoot: 1,\n            isProjectTask: false\n        },\n        // Project-level tasks (created once per project)\n        {\n            title: 'Deliver Files',\n            description: 'Deliver final files to client',\n            assigned_role: 'admin',\n            priority: 'medium',\n            order: 100,\n            dueDaysAfterShoot: 7,\n            isProjectTask: true\n        },\n        {\n            title: 'Payment Collect',\n            description: 'Collect payment from client',\n            assigned_role: 'admin',\n            priority: 'high',\n            order: 101,\n            dueDaysAfterTask: 'Deliver Files',\n            dueDaysAfterShoot: 7,\n            isProjectTask: true\n        }\n    ],\n    Collaboration: [\n        {\n            title: 'Shoot',\n            description: 'Conduct the collaboration shoot',\n            assigned_role: 'pilot',\n            priority: 'high',\n            order: 1,\n            isProjectTask: false\n        },\n        {\n            title: 'File Upload',\n            description: 'Upload captured files to storage',\n            assigned_role: 'pilot',\n            priority: 'medium',\n            order: 2,\n            dueDaysAfterShoot: 1,\n            isProjectTask: false\n        },\n        {\n            title: 'File Backup',\n            description: 'Create backup of all files',\n            assigned_role: 'pilot',\n            priority: 'medium',\n            order: 3,\n            dueDaysAfterShoot: 1,\n            isProjectTask: false\n        },\n        // Project-level tasks (created once per project)\n        {\n            title: 'Deliver Files',\n            description: 'Deliver final files to client',\n            assigned_role: 'admin',\n            priority: 'medium',\n            order: 100,\n            dueDaysAfterShoot: 7,\n            isProjectTask: true\n        },\n        {\n            title: 'Payment Collect',\n            description: 'Collect payment from client',\n            assigned_role: 'admin',\n            priority: 'high',\n            order: 101,\n            dueDaysAfterTask: 'Deliver Files',\n            dueDaysAfterShoot: 7,\n            isProjectTask: true\n        }\n    ],\n    // Corporate, Real Estate (Script Confirmation + Shoot + File Upload + File Backup + Edit)\n    Corporate: [\n        {\n            title: 'Script Confirmation',\n            description: 'Confirm script and requirements with client',\n            assigned_role: 'pilot',\n            priority: 'high',\n            order: 1,\n            isProjectTask: false\n        },\n        {\n            title: 'Shoot',\n            description: 'Conduct the corporate shoot',\n            assigned_role: 'pilot',\n            priority: 'high',\n            order: 2,\n            isProjectTask: false\n        },\n        {\n            title: 'File Upload',\n            description: 'Upload captured files to storage',\n            assigned_role: 'pilot',\n            priority: 'medium',\n            order: 3,\n            dueDaysAfterShoot: 1,\n            isProjectTask: false\n        },\n        {\n            title: 'File Backup',\n            description: 'Create backup of all files',\n            assigned_role: 'pilot',\n            priority: 'medium',\n            order: 4,\n            dueDaysAfterShoot: 1,\n            isProjectTask: false\n        },\n        {\n            title: 'Edit',\n            description: 'Edit and post-process the footage',\n            assigned_role: 'editor',\n            priority: 'high',\n            order: 5,\n            dueDaysAfterShoot: 7,\n            isProjectTask: false\n        },\n        // Project-level tasks (created once per project)\n        {\n            title: 'Deliver Files',\n            description: 'Deliver final files to client',\n            assigned_role: 'admin',\n            priority: 'medium',\n            order: 100,\n            dueDaysAfterShoot: 7,\n            isProjectTask: true\n        },\n        {\n            title: 'Payment Collect',\n            description: 'Collect payment from client',\n            assigned_role: 'admin',\n            priority: 'high',\n            order: 101,\n            dueDaysAfterTask: 'Deliver Files',\n            dueDaysAfterShoot: 7,\n            isProjectTask: true\n        }\n    ],\n    'Real Estate': [\n        {\n            title: 'Script Confirmation',\n            description: 'Confirm property details and requirements',\n            assigned_role: 'pilot',\n            priority: 'high',\n            order: 1,\n            isProjectTask: false\n        },\n        {\n            title: 'Shoot',\n            description: 'Conduct the real estate shoot',\n            assigned_role: 'pilot',\n            priority: 'high',\n            order: 2,\n            isProjectTask: false\n        },\n        {\n            title: 'File Upload',\n            description: 'Upload captured files to storage',\n            assigned_role: 'pilot',\n            priority: 'medium',\n            order: 3,\n            dueDaysAfterShoot: 1,\n            isProjectTask: false\n        },\n        {\n            title: 'File Backup',\n            description: 'Create backup of all files',\n            assigned_role: 'pilot',\n            priority: 'medium',\n            order: 4,\n            dueDaysAfterShoot: 1,\n            isProjectTask: false\n        },\n        {\n            title: 'Edit',\n            description: 'Edit and enhance the property footage',\n            assigned_role: 'editor',\n            priority: 'high',\n            order: 5,\n            dueDaysAfterShoot: 7,\n            isProjectTask: false\n        },\n        // Project-level tasks (created once per project)\n        {\n            title: 'Deliver Files',\n            description: 'Deliver final files to client',\n            assigned_role: 'admin',\n            priority: 'medium',\n            order: 100,\n            dueDaysAfterShoot: 7,\n            isProjectTask: true\n        },\n        {\n            title: 'Payment Collect',\n            description: 'Collect payment from client',\n            assigned_role: 'admin',\n            priority: 'high',\n            order: 101,\n            dueDaysAfterTask: 'Deliver Files',\n            dueDaysAfterShoot: 7,\n            isProjectTask: true\n        }\n    ],\n    // Government, NGO (Shoot + File Upload + File Backup + Edit)\n    Govt: [\n        {\n            title: 'Shoot',\n            description: 'Conduct the government project shoot',\n            assigned_role: 'pilot',\n            priority: 'high',\n            order: 1,\n            isProjectTask: false\n        },\n        {\n            title: 'File Upload',\n            description: 'Upload captured files to storage',\n            assigned_role: 'pilot',\n            priority: 'medium',\n            order: 2,\n            dueDaysAfterShoot: 1,\n            isProjectTask: false\n        },\n        {\n            title: 'File Backup',\n            description: 'Create backup of all files',\n            assigned_role: 'pilot',\n            priority: 'medium',\n            order: 3,\n            dueDaysAfterShoot: 1,\n            isProjectTask: false\n        },\n        {\n            title: 'Edit',\n            description: 'Edit and process the footage',\n            assigned_role: 'editor',\n            priority: 'medium',\n            order: 4,\n            dueDaysAfterShoot: 7,\n            isProjectTask: false\n        },\n        // Project-level tasks (created once per project)\n        {\n            title: 'Deliver Files',\n            description: 'Deliver final files to client',\n            assigned_role: 'admin',\n            priority: 'medium',\n            order: 100,\n            dueDaysAfterShoot: 7,\n            isProjectTask: true\n        },\n        {\n            title: 'Payment Collect',\n            description: 'Collect payment from client',\n            assigned_role: 'admin',\n            priority: 'high',\n            order: 101,\n            dueDaysAfterTask: 'Deliver Files',\n            dueDaysAfterShoot: 7,\n            isProjectTask: true\n        }\n    ],\n    NGO: [\n        {\n            title: 'Shoot',\n            description: 'Conduct the NGO project shoot',\n            assigned_role: 'pilot',\n            priority: 'high',\n            order: 1,\n            isProjectTask: false\n        },\n        {\n            title: 'File Upload',\n            description: 'Upload captured files to storage',\n            assigned_role: 'pilot',\n            priority: 'medium',\n            order: 2,\n            dueDaysAfterShoot: 1,\n            isProjectTask: false\n        },\n        {\n            title: 'File Backup',\n            description: 'Create backup of all files',\n            assigned_role: 'pilot',\n            priority: 'medium',\n            order: 3,\n            dueDaysAfterShoot: 1,\n            isProjectTask: false\n        },\n        {\n            title: 'Edit',\n            description: 'Edit and process the footage',\n            assigned_role: 'editor',\n            priority: 'medium',\n            order: 4,\n            dueDaysAfterShoot: 7,\n            isProjectTask: false\n        },\n        // Project-level tasks (created once per project)\n        {\n            title: 'Deliver Files',\n            description: 'Deliver final files to client',\n            assigned_role: 'admin',\n            priority: 'medium',\n            order: 100,\n            dueDaysAfterShoot: 7,\n            isProjectTask: true\n        },\n        {\n            title: 'Payment Collect',\n            description: 'Collect payment from client',\n            assigned_role: 'admin',\n            priority: 'high',\n            order: 101,\n            dueDaysAfterTask: 'Deliver Files',\n            dueDaysAfterShoot: 7,\n            isProjectTask: true\n        }\n    ],\n    // Survey (Plan Flight + Mark GCPs + Shoot + File Upload + File Backup + Post-Processing)\n    Survey: [\n        {\n            title: 'Plan Flight',\n            description: 'Plan the survey flight path and parameters',\n            assigned_role: 'pilot',\n            priority: 'high',\n            order: 1,\n            isProjectTask: false\n        },\n        {\n            title: 'Mark GCPs',\n            description: 'Mark Ground Control Points for survey accuracy',\n            assigned_role: 'pilot',\n            priority: 'high',\n            order: 2,\n            isProjectTask: false\n        },\n        {\n            title: 'Shoot',\n            description: 'Conduct the survey shoot',\n            assigned_role: 'pilot',\n            priority: 'high',\n            order: 3,\n            isProjectTask: false\n        },\n        {\n            title: 'File Upload',\n            description: 'Upload captured survey data to storage',\n            assigned_role: 'pilot',\n            priority: 'medium',\n            order: 4,\n            dueDaysAfterShoot: 1,\n            isProjectTask: false\n        },\n        {\n            title: 'File Backup',\n            description: 'Create backup of all survey data',\n            assigned_role: 'pilot',\n            priority: 'medium',\n            order: 5,\n            dueDaysAfterShoot: 1,\n            isProjectTask: false\n        },\n        {\n            title: 'Post-Processing',\n            description: 'Process survey data and generate maps/models',\n            assigned_role: 'pilot',\n            priority: 'high',\n            order: 6,\n            dueDaysAfterShoot: 7,\n            isProjectTask: false\n        },\n        // Project-level tasks (created once per project)\n        {\n            title: 'Deliver Files',\n            description: 'Deliver processed survey results to client',\n            assigned_role: 'admin',\n            priority: 'medium',\n            order: 100,\n            dueDaysAfterShoot: 7,\n            isProjectTask: true\n        },\n        {\n            title: 'Payment Collect',\n            description: 'Collect payment from client',\n            assigned_role: 'admin',\n            priority: 'high',\n            order: 101,\n            dueDaysAfterTask: 'Deliver Files',\n            dueDaysAfterShoot: 7,\n            isProjectTask: true\n        }\n    ]\n};\n/**\n * Get default tasks for a specific client type (for project-level tasks)\n */ function getDefaultTasksForClientType(clientType) {\n    return DEFAULT_TASK_TEMPLATES[clientType] || [];\n}\n/**\n * Get default tasks for a specific schedule type (for schedule-based tasks)\n */ function getDefaultTasksForScheduleType(scheduleType) {\n    return DEFAULT_TASK_TEMPLATES[scheduleType] || [];\n}\n/**\n * Create task forms from templates for a specific project\n */ function createTasksFromTemplates(templates, projectId, usersByRole, shootDate, shootId// Optional shoot ID for shoot-based tasks\n) {\n    const sortedTemplates = templates.sort((a, b)=>a.order - b.order);\n    return sortedTemplates.map((template)=>{\n        let dueDate;\n        // Special case: Shoot task should have due date as the schedule start time\n        if (shootDate && template.title === 'Shoot') {\n            dueDate = shootDate; // Use the full schedule date/time\n        } else if (shootDate && template.dueDaysAfterShoot) {\n            const shootDateTime = new Date(shootDate);\n            const dueDateObj = new Date(shootDateTime);\n            dueDateObj.setDate(dueDateObj.getDate() + template.dueDaysAfterShoot);\n            dueDate = dueDateObj.toISOString().split('T')[0]; // Format as YYYY-MM-DD\n        }\n        // Handle tasks that depend on other tasks (like Payment Collect after Deliver Files)\n        if (shootDate && template.dueDaysAfterTask && template.dueDaysAfterShoot) {\n            const shootDateTime = new Date(shootDate);\n            const dueDateObj = new Date(shootDateTime);\n            dueDateObj.setDate(dueDateObj.getDate() + template.dueDaysAfterShoot);\n            dueDate = dueDateObj.toISOString().split('T')[0];\n        }\n        return {\n            title: template.title,\n            description: template.description,\n            status: 'pending',\n            priority: template.priority,\n            assigned_to: usersByRole[template.assigned_role] || '',\n            assigned_role: template.assigned_role,\n            project_id: projectId,\n            shoot_id: template.isProjectTask ? undefined : shootId,\n            due_date: dueDate,\n            order: template.order\n        };\n    });\n}\n/**\n * Get all available client types that have default tasks\n */ function getClientTypesWithDefaultTasks() {\n    return Object.keys(DEFAULT_TASK_TEMPLATES);\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/default-tasks.ts\n"));

/***/ })

}]);