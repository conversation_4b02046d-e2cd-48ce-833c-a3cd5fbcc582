"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_src_components_ui_notification-bell_tsx"],{

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bell.js":
/*!**********************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/bell.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ Bell)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M10.268 21a2 2 0 0 0 3.464 0\",\n            key: \"vwvbt9\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M3.262 15.326A1 1 0 0 0 4 17h16a1 1 0 0 0 .74-1.673C19.41 13.956 18 12.499 18 8A6 6 0 0 0 6 8c0 4.499-1.411 5.956-2.738 7.326\",\n            key: \"11g9vi\"\n        }\n    ]\n];\nconst Bell = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"bell\", __iconNode);\n //# sourceMappingURL=bell.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bell.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ui/notification-bell.tsx":
/*!*************************************************!*\
  !*** ./src/components/ui/notification-bell.tsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NotificationBell: () => (/* binding */ NotificationBell)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Bell!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _hooks_useNotifications__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/hooks/useNotifications */ \"(app-pages-browser)/./src/hooks/useNotifications.ts\");\n/* __next_internal_client_entry_do_not_use__ NotificationBell auto */ \nvar _s = $RefreshSig$();\n\n\nfunction NotificationBell() {\n    _s();\n    const { stats, loading, error } = (0,_hooks_useNotifications__WEBPACK_IMPORTED_MODULE_1__.useNotifications)({\n        read: false,\n        limit: 5\n    });\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"relative p-2 rounded-full hover:bg-muted transition-colors\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                className: \"w-5 h-5 text-muted-foreground\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/notification-bell.tsx\",\n                lineNumber: 19,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/notification-bell.tsx\",\n            lineNumber: 18,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"relative p-2 rounded-full hover:bg-muted transition-colors\",\n            title: error,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                className: \"w-5 h-5 text-red-500\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/notification-bell.tsx\",\n                lineNumber: 27,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/notification-bell.tsx\",\n            lineNumber: 26,\n            columnNumber: 7\n        }, this);\n    }\n    const unreadCount = (stats === null || stats === void 0 ? void 0 : stats.unread) || 0;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative p-2 rounded-full hover:bg-muted transition-colors cursor-pointer\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                className: \"w-5 h-5 text-muted-foreground\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/notification-bell.tsx\",\n                lineNumber: 36,\n                columnNumber: 7\n            }, this),\n            unreadCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center\",\n                children: unreadCount > 99 ? '99+' : unreadCount\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/notification-bell.tsx\",\n                lineNumber: 38,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/notification-bell.tsx\",\n        lineNumber: 35,\n        columnNumber: 5\n    }, this);\n}\n_s(NotificationBell, \"SXx0sKgu09RQWLxrxebzlyuwa3I=\", false, function() {\n    return [\n        _hooks_useNotifications__WEBPACK_IMPORTED_MODULE_1__.useNotifications\n    ];\n});\n_c = NotificationBell;\nvar _c;\n$RefreshReg$(_c, \"NotificationBell\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/notification-bell.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/hooks/useNotifications.ts":
/*!***************************************!*\
  !*** ./src/hooks/useNotifications.ts ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useNotifications: () => (/* binding */ useNotifications)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\nfunction useNotifications(filters) {\n    const [notifications, setNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    console.log('🔔 useNotifications hook called with filters:', filters);\n    // Fetch notifications and stats\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useNotifications.useEffect\": ()=>{\n            console.log('🔔 useNotifications effect running');\n            const fetchNotifications = {\n                \"useNotifications.useEffect.fetchNotifications\": async ()=>{\n                    try {\n                        setLoading(true);\n                        setError(null);\n                        // Provide fallback data instead of making API calls\n                        console.warn('🔔 Notifications API disabled, using empty data');\n                        setNotifications([]);\n                        setStats({\n                            total: 0,\n                            unread: 0,\n                            by_type: {\n                                payment: 0,\n                                schedule: 0,\n                                task: 0,\n                                info: 0,\n                                success: 0,\n                                warning: 0,\n                                error: 0,\n                                project: 0,\n                                system: 0\n                            },\n                            by_category: {\n                                task_assigned: 0,\n                                task_due: 0,\n                                task_overdue: 0,\n                                task_completed: 0,\n                                project_created: 0,\n                                project_updated: 0,\n                                schedule_upcoming: 0,\n                                schedule_changed: 0,\n                                payment_received: 0,\n                                payment_overdue: 0,\n                                system_update: 0,\n                                user_mention: 0,\n                                deadline_reminder: 0,\n                                general: 0\n                            },\n                            by_priority: {\n                                low: 0,\n                                medium: 0,\n                                high: 0,\n                                urgent: 0\n                            }\n                        });\n                    } catch (err) {\n                        console.error('Error in useNotifications:', err);\n                        setError(err instanceof Error ? err.message : 'Failed to fetch notifications');\n                        // Ensure we still set empty data even if there's an error\n                        setNotifications([]);\n                        setStats({\n                            total: 0,\n                            unread: 0,\n                            by_type: {\n                                payment: 0,\n                                schedule: 0,\n                                task: 0,\n                                info: 0,\n                                success: 0,\n                                warning: 0,\n                                error: 0,\n                                project: 0,\n                                system: 0\n                            },\n                            by_category: {\n                                task_assigned: 0,\n                                task_due: 0,\n                                task_overdue: 0,\n                                task_completed: 0,\n                                project_created: 0,\n                                project_updated: 0,\n                                schedule_upcoming: 0,\n                                schedule_changed: 0,\n                                payment_received: 0,\n                                payment_overdue: 0,\n                                system_update: 0,\n                                user_mention: 0,\n                                deadline_reminder: 0,\n                                general: 0\n                            },\n                            by_priority: {\n                                low: 0,\n                                medium: 0,\n                                high: 0,\n                                urgent: 0\n                            }\n                        });\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"useNotifications.useEffect.fetchNotifications\"];\n            fetchNotifications();\n        }\n    }[\"useNotifications.useEffect\"], [\n        filters === null || filters === void 0 ? void 0 : filters.read,\n        filters === null || filters === void 0 ? void 0 : filters.type,\n        filters === null || filters === void 0 ? void 0 : filters.category,\n        filters === null || filters === void 0 ? void 0 : filters.priority,\n        filters === null || filters === void 0 ? void 0 : filters.limit,\n        filters === null || filters === void 0 ? void 0 : filters.offset\n    ]);\n    // Return minimal interface\n    return {\n        notifications,\n        stats,\n        loading,\n        error,\n        refetch: ()=>Promise.resolve(),\n        markAsRead: ()=>Promise.resolve(),\n        markAsUnread: ()=>Promise.resolve(),\n        markAllAsRead: ()=>Promise.resolve(),\n        deleteNotification: ()=>Promise.resolve(),\n        createNotification: ()=>Promise.resolve({})\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useNotifications.ts\n"));

/***/ })

}]);