"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_exifr_dist_full_esm_mjs"],{

/***/ "(app-pages-browser)/./node_modules/exifr/dist/full.esm.mjs":
/*!**********************************************!*\
  !*** ./node_modules/exifr/dist/full.esm.mjs ***!
  \**********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Exifr: () => (/* binding */ te),\n/* harmony export */   Options: () => (/* binding */ q),\n/* harmony export */   allFormatters: () => (/* binding */ X),\n/* harmony export */   chunkedProps: () => (/* binding */ G),\n/* harmony export */   createDictionary: () => (/* binding */ U),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   extendDictionary: () => (/* binding */ F),\n/* harmony export */   fetchUrlAsArrayBuffer: () => (/* binding */ M),\n/* harmony export */   fileParsers: () => (/* binding */ w),\n/* harmony export */   fileReaders: () => (/* binding */ A),\n/* harmony export */   gps: () => (/* binding */ Se),\n/* harmony export */   gpsOnlyOptions: () => (/* binding */ me),\n/* harmony export */   inheritables: () => (/* binding */ K),\n/* harmony export */   orientation: () => (/* binding */ Pe),\n/* harmony export */   orientationOnlyOptions: () => (/* binding */ Ie),\n/* harmony export */   otherSegments: () => (/* binding */ V),\n/* harmony export */   parse: () => (/* binding */ ie),\n/* harmony export */   readBlobAsArrayBuffer: () => (/* binding */ R),\n/* harmony export */   rotateCanvas: () => (/* binding */ we),\n/* harmony export */   rotateCss: () => (/* binding */ Te),\n/* harmony export */   rotation: () => (/* binding */ Ae),\n/* harmony export */   rotations: () => (/* binding */ ke),\n/* harmony export */   segmentParsers: () => (/* binding */ T),\n/* harmony export */   segments: () => (/* binding */ z),\n/* harmony export */   segmentsAndBlocks: () => (/* binding */ j),\n/* harmony export */   sidecar: () => (/* binding */ st),\n/* harmony export */   tagKeys: () => (/* binding */ E),\n/* harmony export */   tagRevivers: () => (/* binding */ N),\n/* harmony export */   tagValues: () => (/* binding */ B),\n/* harmony export */   thumbnail: () => (/* binding */ ye),\n/* harmony export */   thumbnailOnlyOptions: () => (/* binding */ Ce),\n/* harmony export */   thumbnailUrl: () => (/* binding */ be),\n/* harmony export */   tiffBlocks: () => (/* binding */ H),\n/* harmony export */   tiffExtractables: () => (/* binding */ W)\n/* harmony export */ });\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\");\n/* provided dependency */ var Buffer = __webpack_require__(/*! buffer */ \"(app-pages-browser)/./node_modules/next/dist/compiled/buffer/index.js\")[\"Buffer\"];\nvar e=\"undefined\"!=typeof self?self:global;const t=\"undefined\"!=typeof navigator,i=t&&\"undefined\"==typeof HTMLImageElement,n=!(\"undefined\"==typeof global||\"undefined\"==typeof process||!process.versions||!process.versions.node),s=e.Buffer,r=e.BigInt,a=!!s,o=e=>e;function l(e,t=o){if(n)try{return\"function\"==typeof require?Promise.resolve(t(require(e))):import(/* webpackIgnore: true */ e).then(t)}catch(t){console.warn(`Couldn't load ${e}`)}}let h=e.fetch;const u=e=>h=e;if(!e.fetch){const e=l(\"http\",(e=>e)),t=l(\"https\",(e=>e)),i=(n,{headers:s}={})=>new Promise((async(r,a)=>{let{port:o,hostname:l,pathname:h,protocol:u,search:c}=new URL(n);const f={method:\"GET\",hostname:l,path:encodeURI(h)+c,headers:s};\"\"!==o&&(f.port=Number(o));const d=(\"https:\"===u?await t:await e).request(f,(e=>{if(301===e.statusCode||302===e.statusCode){let t=new URL(e.headers.location,n).toString();return i(t,{headers:s}).then(r).catch(a)}r({status:e.statusCode,arrayBuffer:()=>new Promise((t=>{let i=[];e.on(\"data\",(e=>i.push(e))),e.on(\"end\",(()=>t(Buffer.concat(i))))}))})}));d.on(\"error\",a),d.end()}));u(i)}function c(e,t,i){return t in e?Object.defineProperty(e,t,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[t]=i,e}const f=e=>p(e)?void 0:e,d=e=>void 0!==e;function p(e){return void 0===e||(e instanceof Map?0===e.size:0===Object.values(e).filter(d).length)}function g(e){let t=new Error(e);throw delete t.stack,t}function m(e){return\"\"===(e=function(e){for(;e.endsWith(\"\\0\");)e=e.slice(0,-1);return e}(e).trim())?void 0:e}function S(e){let t=function(e){let t=0;return e.ifd0.enabled&&(t+=1024),e.exif.enabled&&(t+=2048),e.makerNote&&(t+=2048),e.userComment&&(t+=1024),e.gps.enabled&&(t+=512),e.interop.enabled&&(t+=100),e.ifd1.enabled&&(t+=1024),t+2048}(e);return e.jfif.enabled&&(t+=50),e.xmp.enabled&&(t+=2e4),e.iptc.enabled&&(t+=14e3),e.icc.enabled&&(t+=6e3),t}const C=e=>String.fromCharCode.apply(null,e),y=\"undefined\"!=typeof TextDecoder?new TextDecoder(\"utf-8\"):void 0;function b(e){return y?y.decode(e):a?Buffer.from(e).toString(\"utf8\"):decodeURIComponent(escape(C(e)))}class I{static from(e,t){return e instanceof this&&e.le===t?e:new I(e,void 0,void 0,t)}constructor(e,t=0,i,n){if(\"boolean\"==typeof n&&(this.le=n),Array.isArray(e)&&(e=new Uint8Array(e)),0===e)this.byteOffset=0,this.byteLength=0;else if(e instanceof ArrayBuffer){void 0===i&&(i=e.byteLength-t);let n=new DataView(e,t,i);this._swapDataView(n)}else if(e instanceof Uint8Array||e instanceof DataView||e instanceof I){void 0===i&&(i=e.byteLength-t),(t+=e.byteOffset)+i>e.byteOffset+e.byteLength&&g(\"Creating view outside of available memory in ArrayBuffer\");let n=new DataView(e.buffer,t,i);this._swapDataView(n)}else if(\"number\"==typeof e){let t=new DataView(new ArrayBuffer(e));this._swapDataView(t)}else g(\"Invalid input argument for BufferView: \"+e)}_swapArrayBuffer(e){this._swapDataView(new DataView(e))}_swapBuffer(e){this._swapDataView(new DataView(e.buffer,e.byteOffset,e.byteLength))}_swapDataView(e){this.dataView=e,this.buffer=e.buffer,this.byteOffset=e.byteOffset,this.byteLength=e.byteLength}_lengthToEnd(e){return this.byteLength-e}set(e,t,i=I){return e instanceof DataView||e instanceof I?e=new Uint8Array(e.buffer,e.byteOffset,e.byteLength):e instanceof ArrayBuffer&&(e=new Uint8Array(e)),e instanceof Uint8Array||g(\"BufferView.set(): Invalid data argument.\"),this.toUint8().set(e,t),new i(this,t,e.byteLength)}subarray(e,t){return t=t||this._lengthToEnd(e),new I(this,e,t)}toUint8(){return new Uint8Array(this.buffer,this.byteOffset,this.byteLength)}getUint8Array(e,t){return new Uint8Array(this.buffer,this.byteOffset+e,t)}getString(e=0,t=this.byteLength){return b(this.getUint8Array(e,t))}getLatin1String(e=0,t=this.byteLength){let i=this.getUint8Array(e,t);return C(i)}getUnicodeString(e=0,t=this.byteLength){const i=[];for(let n=0;n<t&&e+n<this.byteLength;n+=2)i.push(this.getUint16(e+n));return C(i)}getInt8(e){return this.dataView.getInt8(e)}getUint8(e){return this.dataView.getUint8(e)}getInt16(e,t=this.le){return this.dataView.getInt16(e,t)}getInt32(e,t=this.le){return this.dataView.getInt32(e,t)}getUint16(e,t=this.le){return this.dataView.getUint16(e,t)}getUint32(e,t=this.le){return this.dataView.getUint32(e,t)}getFloat32(e,t=this.le){return this.dataView.getFloat32(e,t)}getFloat64(e,t=this.le){return this.dataView.getFloat64(e,t)}getFloat(e,t=this.le){return this.dataView.getFloat32(e,t)}getDouble(e,t=this.le){return this.dataView.getFloat64(e,t)}getUintBytes(e,t,i){switch(t){case 1:return this.getUint8(e,i);case 2:return this.getUint16(e,i);case 4:return this.getUint32(e,i);case 8:return this.getUint64&&this.getUint64(e,i)}}getUint(e,t,i){switch(t){case 8:return this.getUint8(e,i);case 16:return this.getUint16(e,i);case 32:return this.getUint32(e,i);case 64:return this.getUint64&&this.getUint64(e,i)}}toString(e){return this.dataView.toString(e,this.constructor.name)}ensureChunk(){}}function P(e,t){g(`${e} '${t}' was not loaded, try using full build of exifr.`)}class k extends Map{constructor(e){super(),this.kind=e}get(e,t){return this.has(e)||P(this.kind,e),t&&(e in t||function(e,t){g(`Unknown ${e} '${t}'.`)}(this.kind,e),t[e].enabled||P(this.kind,e)),super.get(e)}keyList(){return Array.from(this.keys())}}var w=new k(\"file parser\"),T=new k(\"segment parser\"),A=new k(\"file reader\");function D(e,n){return\"string\"==typeof e?O(e,n):t&&!i&&e instanceof HTMLImageElement?O(e.src,n):e instanceof Uint8Array||e instanceof ArrayBuffer||e instanceof DataView?new I(e):t&&e instanceof Blob?x(e,n,\"blob\",R):void g(\"Invalid input argument\")}function O(e,i){return(s=e).startsWith(\"data:\")||s.length>1e4?v(e,i,\"base64\"):n&&e.includes(\"://\")?x(e,i,\"url\",M):n?v(e,i,\"fs\"):t?x(e,i,\"url\",M):void g(\"Invalid input argument\");var s}async function x(e,t,i,n){return A.has(i)?v(e,t,i):n?async function(e,t){let i=await t(e);return new I(i)}(e,n):void g(`Parser ${i} is not loaded`)}async function v(e,t,i){let n=new(A.get(i))(e,t);return await n.read(),n}const M=e=>h(e).then((e=>e.arrayBuffer())),R=e=>new Promise(((t,i)=>{let n=new FileReader;n.onloadend=()=>t(n.result||new ArrayBuffer),n.onerror=i,n.readAsArrayBuffer(e)}));class L extends Map{get tagKeys(){return this.allKeys||(this.allKeys=Array.from(this.keys())),this.allKeys}get tagValues(){return this.allValues||(this.allValues=Array.from(this.values())),this.allValues}}function U(e,t,i){let n=new L;for(let[e,t]of i)n.set(e,t);if(Array.isArray(t))for(let i of t)e.set(i,n);else e.set(t,n);return n}function F(e,t,i){let n,s=e.get(t);for(n of i)s.set(n[0],n[1])}const E=new Map,B=new Map,N=new Map,G=[\"chunked\",\"firstChunkSize\",\"firstChunkSizeNode\",\"firstChunkSizeBrowser\",\"chunkSize\",\"chunkLimit\"],V=[\"jfif\",\"xmp\",\"icc\",\"iptc\",\"ihdr\"],z=[\"tiff\",...V],H=[\"ifd0\",\"ifd1\",\"exif\",\"gps\",\"interop\"],j=[...z,...H],W=[\"makerNote\",\"userComment\"],K=[\"translateKeys\",\"translateValues\",\"reviveValues\",\"multiSegment\"],X=[...K,\"sanitize\",\"mergeOutput\",\"silentErrors\"];class _{get translate(){return this.translateKeys||this.translateValues||this.reviveValues}}class Y extends _{get needed(){return this.enabled||this.deps.size>0}constructor(e,t,i,n){if(super(),c(this,\"enabled\",!1),c(this,\"skip\",new Set),c(this,\"pick\",new Set),c(this,\"deps\",new Set),c(this,\"translateKeys\",!1),c(this,\"translateValues\",!1),c(this,\"reviveValues\",!1),this.key=e,this.enabled=t,this.parse=this.enabled,this.applyInheritables(n),this.canBeFiltered=H.includes(e),this.canBeFiltered&&(this.dict=E.get(e)),void 0!==i)if(Array.isArray(i))this.parse=this.enabled=!0,this.canBeFiltered&&i.length>0&&this.translateTagSet(i,this.pick);else if(\"object\"==typeof i){if(this.enabled=!0,this.parse=!1!==i.parse,this.canBeFiltered){let{pick:e,skip:t}=i;e&&e.length>0&&this.translateTagSet(e,this.pick),t&&t.length>0&&this.translateTagSet(t,this.skip)}this.applyInheritables(i)}else!0===i||!1===i?this.parse=this.enabled=i:g(`Invalid options argument: ${i}`)}applyInheritables(e){let t,i;for(t of K)i=e[t],void 0!==i&&(this[t]=i)}translateTagSet(e,t){if(this.dict){let i,n,{tagKeys:s,tagValues:r}=this.dict;for(i of e)\"string\"==typeof i?(n=r.indexOf(i),-1===n&&(n=s.indexOf(Number(i))),-1!==n&&t.add(Number(s[n]))):t.add(i)}else for(let i of e)t.add(i)}finalizeFilters(){!this.enabled&&this.deps.size>0?(this.enabled=!0,ee(this.pick,this.deps)):this.enabled&&this.pick.size>0&&ee(this.pick,this.deps)}}var $={jfif:!1,tiff:!0,xmp:!1,icc:!1,iptc:!1,ifd0:!0,ifd1:!1,exif:!0,gps:!0,interop:!1,ihdr:void 0,makerNote:!1,userComment:!1,multiSegment:!1,skip:[],pick:[],translateKeys:!0,translateValues:!0,reviveValues:!0,sanitize:!0,mergeOutput:!0,silentErrors:!0,chunked:!0,firstChunkSize:void 0,firstChunkSizeNode:512,firstChunkSizeBrowser:65536,chunkSize:65536,chunkLimit:5},J=new Map;class q extends _{static useCached(e){let t=J.get(e);return void 0!==t||(t=new this(e),J.set(e,t)),t}constructor(e){super(),!0===e?this.setupFromTrue():void 0===e?this.setupFromUndefined():Array.isArray(e)?this.setupFromArray(e):\"object\"==typeof e?this.setupFromObject(e):g(`Invalid options argument ${e}`),void 0===this.firstChunkSize&&(this.firstChunkSize=t?this.firstChunkSizeBrowser:this.firstChunkSizeNode),this.mergeOutput&&(this.ifd1.enabled=!1),this.filterNestedSegmentTags(),this.traverseTiffDependencyTree(),this.checkLoadedPlugins()}setupFromUndefined(){let e;for(e of G)this[e]=$[e];for(e of X)this[e]=$[e];for(e of W)this[e]=$[e];for(e of j)this[e]=new Y(e,$[e],void 0,this)}setupFromTrue(){let e;for(e of G)this[e]=$[e];for(e of X)this[e]=$[e];for(e of W)this[e]=!0;for(e of j)this[e]=new Y(e,!0,void 0,this)}setupFromArray(e){let t;for(t of G)this[t]=$[t];for(t of X)this[t]=$[t];for(t of W)this[t]=$[t];for(t of j)this[t]=new Y(t,!1,void 0,this);this.setupGlobalFilters(e,void 0,H)}setupFromObject(e){let t;for(t of(H.ifd0=H.ifd0||H.image,H.ifd1=H.ifd1||H.thumbnail,Object.assign(this,e),G))this[t]=Z(e[t],$[t]);for(t of X)this[t]=Z(e[t],$[t]);for(t of W)this[t]=Z(e[t],$[t]);for(t of z)this[t]=new Y(t,$[t],e[t],this);for(t of H)this[t]=new Y(t,$[t],e[t],this.tiff);this.setupGlobalFilters(e.pick,e.skip,H,j),!0===e.tiff?this.batchEnableWithBool(H,!0):!1===e.tiff?this.batchEnableWithUserValue(H,e):Array.isArray(e.tiff)?this.setupGlobalFilters(e.tiff,void 0,H):\"object\"==typeof e.tiff&&this.setupGlobalFilters(e.tiff.pick,e.tiff.skip,H)}batchEnableWithBool(e,t){for(let i of e)this[i].enabled=t}batchEnableWithUserValue(e,t){for(let i of e){let e=t[i];this[i].enabled=!1!==e&&void 0!==e}}setupGlobalFilters(e,t,i,n=i){if(e&&e.length){for(let e of n)this[e].enabled=!1;let t=Q(e,i);for(let[e,i]of t)ee(this[e].pick,i),this[e].enabled=!0}else if(t&&t.length){let e=Q(t,i);for(let[t,i]of e)ee(this[t].skip,i)}}filterNestedSegmentTags(){let{ifd0:e,exif:t,xmp:i,iptc:n,icc:s}=this;this.makerNote?t.deps.add(37500):t.skip.add(37500),this.userComment?t.deps.add(37510):t.skip.add(37510),i.enabled||e.skip.add(700),n.enabled||e.skip.add(33723),s.enabled||e.skip.add(34675)}traverseTiffDependencyTree(){let{ifd0:e,exif:t,gps:i,interop:n}=this;n.needed&&(t.deps.add(40965),e.deps.add(40965)),t.needed&&e.deps.add(34665),i.needed&&e.deps.add(34853),this.tiff.enabled=H.some((e=>!0===this[e].enabled))||this.makerNote||this.userComment;for(let e of H)this[e].finalizeFilters()}get onlyTiff(){return!V.map((e=>this[e].enabled)).some((e=>!0===e))&&this.tiff.enabled}checkLoadedPlugins(){for(let e of z)this[e].enabled&&!T.has(e)&&P(\"segment parser\",e)}}function Q(e,t){let i,n,s,r,a=[];for(s of t){for(r of(i=E.get(s),n=[],i))(e.includes(r[0])||e.includes(r[1]))&&n.push(r[0]);n.length&&a.push([s,n])}return a}function Z(e,t){return void 0!==e?e:void 0!==t?t:void 0}function ee(e,t){for(let i of t)e.add(i)}c(q,\"default\",$);class te{constructor(e){c(this,\"parsers\",{}),c(this,\"output\",{}),c(this,\"errors\",[]),c(this,\"pushToErrors\",(e=>this.errors.push(e))),this.options=q.useCached(e)}async read(e){this.file=await D(e,this.options)}setup(){if(this.fileParser)return;let{file:e}=this,t=e.getUint16(0);for(let[i,n]of w)if(n.canHandle(e,t))return this.fileParser=new n(this.options,this.file,this.parsers),e[i]=!0;this.file.close&&this.file.close(),g(\"Unknown file format\")}async parse(){let{output:e,errors:t}=this;return this.setup(),this.options.silentErrors?(await this.executeParsers().catch(this.pushToErrors),t.push(...this.fileParser.errors)):await this.executeParsers(),this.file.close&&this.file.close(),this.options.silentErrors&&t.length>0&&(e.errors=t),f(e)}async executeParsers(){let{output:e}=this;await this.fileParser.parse();let t=Object.values(this.parsers).map((async t=>{let i=await t.parse();t.assignToOutput(e,i)}));this.options.silentErrors&&(t=t.map((e=>e.catch(this.pushToErrors)))),await Promise.all(t)}async extractThumbnail(){this.setup();let{options:e,file:t}=this,i=T.get(\"tiff\",e);var n;if(t.tiff?n={start:0,type:\"tiff\"}:t.jpeg&&(n=await this.fileParser.getOrFindSegment(\"tiff\")),void 0===n)return;let s=await this.fileParser.ensureSegmentChunk(n),r=this.parsers.tiff=new i(s,e,t),a=await r.extractThumbnail();return t.close&&t.close(),a}}async function ie(e,t){let i=new te(t);return await i.read(e),i.parse()}var ne=Object.freeze({__proto__:null,parse:ie,Exifr:te,fileParsers:w,segmentParsers:T,fileReaders:A,tagKeys:E,tagValues:B,tagRevivers:N,createDictionary:U,extendDictionary:F,fetchUrlAsArrayBuffer:M,readBlobAsArrayBuffer:R,chunkedProps:G,otherSegments:V,segments:z,tiffBlocks:H,segmentsAndBlocks:j,tiffExtractables:W,inheritables:K,allFormatters:X,Options:q});class se{constructor(e,t,i){c(this,\"errors\",[]),c(this,\"ensureSegmentChunk\",(async e=>{let t=e.start,i=e.size||65536;if(this.file.chunked)if(this.file.available(t,i))e.chunk=this.file.subarray(t,i);else try{e.chunk=await this.file.readChunk(t,i)}catch(t){g(`Couldn't read segment: ${JSON.stringify(e)}. ${t.message}`)}else this.file.byteLength>t+i?e.chunk=this.file.subarray(t,i):void 0===e.size?e.chunk=this.file.subarray(t):g(\"Segment unreachable: \"+JSON.stringify(e));return e.chunk})),this.extendOptions&&this.extendOptions(e),this.options=e,this.file=t,this.parsers=i}injectSegment(e,t){this.options[e].enabled&&this.createParser(e,t)}createParser(e,t){let i=new(T.get(e))(t,this.options,this.file);return this.parsers[e]=i}createParsers(e){for(let t of e){let{type:e,chunk:i}=t,n=this.options[e];if(n&&n.enabled){let t=this.parsers[e];t&&t.append||t||this.createParser(e,i)}}}async readSegments(e){let t=e.map(this.ensureSegmentChunk);await Promise.all(t)}}class re{static findPosition(e,t){let i=e.getUint16(t+2)+2,n=\"function\"==typeof this.headerLength?this.headerLength(e,t,i):this.headerLength,s=t+n,r=i-n;return{offset:t,length:i,headerLength:n,start:s,size:r,end:s+r}}static parse(e,t={}){return new this(e,new q({[this.type]:t}),e).parse()}normalizeInput(e){return e instanceof I?e:new I(e)}constructor(e,t={},i){c(this,\"errors\",[]),c(this,\"raw\",new Map),c(this,\"handleError\",(e=>{if(!this.options.silentErrors)throw e;this.errors.push(e.message)})),this.chunk=this.normalizeInput(e),this.file=i,this.type=this.constructor.type,this.globalOptions=this.options=t,this.localOptions=t[this.type],this.canTranslate=this.localOptions&&this.localOptions.translate}translate(){this.canTranslate&&(this.translated=this.translateBlock(this.raw,this.type))}get output(){return this.translated?this.translated:this.raw?Object.fromEntries(this.raw):void 0}translateBlock(e,t){let i=N.get(t),n=B.get(t),s=E.get(t),r=this.options[t],a=r.reviveValues&&!!i,o=r.translateValues&&!!n,l=r.translateKeys&&!!s,h={};for(let[t,r]of e)a&&i.has(t)?r=i.get(t)(r):o&&n.has(t)&&(r=this.translateValue(r,n.get(t))),l&&s.has(t)&&(t=s.get(t)||t),h[t]=r;return h}translateValue(e,t){return t[e]||t.DEFAULT||e}assignToOutput(e,t){this.assignObjectToOutput(e,this.constructor.type,t)}assignObjectToOutput(e,t,i){if(this.globalOptions.mergeOutput)return Object.assign(e,i);e[t]?Object.assign(e[t],i):e[t]=i}}c(re,\"headerLength\",4),c(re,\"type\",void 0),c(re,\"multiSegment\",!1),c(re,\"canHandle\",(()=>!1));function ae(e){return 192===e||194===e||196===e||219===e||221===e||218===e||254===e}function oe(e){return e>=224&&e<=239}function le(e,t,i){for(let[n,s]of T)if(s.canHandle(e,t,i))return n}class he extends se{constructor(...e){super(...e),c(this,\"appSegments\",[]),c(this,\"jpegSegments\",[]),c(this,\"unknownSegments\",[])}static canHandle(e,t){return 65496===t}async parse(){await this.findAppSegments(),await this.readSegments(this.appSegments),this.mergeMultiSegments(),this.createParsers(this.mergedAppSegments||this.appSegments)}setupSegmentFinderArgs(e){!0===e?(this.findAll=!0,this.wanted=new Set(T.keyList())):(e=void 0===e?T.keyList().filter((e=>this.options[e].enabled)):e.filter((e=>this.options[e].enabled&&T.has(e))),this.findAll=!1,this.remaining=new Set(e),this.wanted=new Set(e)),this.unfinishedMultiSegment=!1}async findAppSegments(e=0,t){this.setupSegmentFinderArgs(t);let{file:i,findAll:n,wanted:s,remaining:r}=this;if(!n&&this.file.chunked&&(n=Array.from(s).some((e=>{let t=T.get(e),i=this.options[e];return t.multiSegment&&i.multiSegment})),n&&await this.file.readWhole()),e=this.findAppSegmentsInRange(e,i.byteLength),!this.options.onlyTiff&&i.chunked){let t=!1;for(;r.size>0&&!t&&(i.canReadNextChunk||this.unfinishedMultiSegment);){let{nextChunkOffset:n}=i,s=this.appSegments.some((e=>!this.file.available(e.offset||e.start,e.length||e.size)));if(t=e>n&&!s?!await i.readNextChunk(e):!await i.readNextChunk(n),void 0===(e=this.findAppSegmentsInRange(e,i.byteLength)))return}}}findAppSegmentsInRange(e,t){t-=2;let i,n,s,r,a,o,{file:l,findAll:h,wanted:u,remaining:c,options:f}=this;for(;e<t;e++)if(255===l.getUint8(e))if(i=l.getUint8(e+1),oe(i)){if(n=l.getUint16(e+2),s=le(l,e,n),s&&u.has(s)&&(r=T.get(s),a=r.findPosition(l,e),o=f[s],a.type=s,this.appSegments.push(a),!h&&(r.multiSegment&&o.multiSegment?(this.unfinishedMultiSegment=a.chunkNumber<a.chunkCount,this.unfinishedMultiSegment||c.delete(s)):c.delete(s),0===c.size)))break;f.recordUnknownSegments&&(a=re.findPosition(l,e),a.marker=i,this.unknownSegments.push(a)),e+=n+1}else if(ae(i)){if(n=l.getUint16(e+2),218===i&&!1!==f.stopAfterSos)return;f.recordJpegSegments&&this.jpegSegments.push({offset:e,length:n,marker:i}),e+=n+1}return e}mergeMultiSegments(){if(!this.appSegments.some((e=>e.multiSegment)))return;let e=function(e,t){let i,n,s,r=new Map;for(let a=0;a<e.length;a++)i=e[a],n=i[t],r.has(n)?s=r.get(n):r.set(n,s=[]),s.push(i);return Array.from(r)}(this.appSegments,\"type\");this.mergedAppSegments=e.map((([e,t])=>{let i=T.get(e,this.options);if(i.handleMultiSegments){return{type:e,chunk:i.handleMultiSegments(t)}}return t[0]}))}getSegment(e){return this.appSegments.find((t=>t.type===e))}async getOrFindSegment(e){let t=this.getSegment(e);return void 0===t&&(await this.findAppSegments(0,[e]),t=this.getSegment(e)),t}}c(he,\"type\",\"jpeg\"),w.set(\"jpeg\",he);const ue=[void 0,1,1,2,4,8,1,1,2,4,8,4,8,4];class ce extends re{parseHeader(){var e=this.chunk.getUint16();18761===e?this.le=!0:19789===e&&(this.le=!1),this.chunk.le=this.le,this.headerParsed=!0}parseTags(e,t,i=new Map){let{pick:n,skip:s}=this.options[t];n=new Set(n);let r=n.size>0,a=0===s.size,o=this.chunk.getUint16(e);e+=2;for(let l=0;l<o;l++){let o=this.chunk.getUint16(e);if(r){if(n.has(o)&&(i.set(o,this.parseTag(e,o,t)),n.delete(o),0===n.size))break}else!a&&s.has(o)||i.set(o,this.parseTag(e,o,t));e+=12}return i}parseTag(e,t,i){let{chunk:n}=this,s=n.getUint16(e+2),r=n.getUint32(e+4),a=ue[s];if(a*r<=4?e+=8:e=n.getUint32(e+8),(s<1||s>13)&&g(`Invalid TIFF value type. block: ${i.toUpperCase()}, tag: ${t.toString(16)}, type: ${s}, offset ${e}`),e>n.byteLength&&g(`Invalid TIFF value offset. block: ${i.toUpperCase()}, tag: ${t.toString(16)}, type: ${s}, offset ${e} is outside of chunk size ${n.byteLength}`),1===s)return n.getUint8Array(e,r);if(2===s)return m(n.getString(e,r));if(7===s)return n.getUint8Array(e,r);if(1===r)return this.parseTagValue(s,e);{let t=new(function(e){switch(e){case 1:return Uint8Array;case 3:return Uint16Array;case 4:return Uint32Array;case 5:return Array;case 6:return Int8Array;case 8:return Int16Array;case 9:return Int32Array;case 10:return Array;case 11:return Float32Array;case 12:return Float64Array;default:return Array}}(s))(r),i=a;for(let n=0;n<r;n++)t[n]=this.parseTagValue(s,e),e+=i;return t}}parseTagValue(e,t){let{chunk:i}=this;switch(e){case 1:return i.getUint8(t);case 3:return i.getUint16(t);case 4:return i.getUint32(t);case 5:return i.getUint32(t)/i.getUint32(t+4);case 6:return i.getInt8(t);case 8:return i.getInt16(t);case 9:return i.getInt32(t);case 10:return i.getInt32(t)/i.getInt32(t+4);case 11:return i.getFloat(t);case 12:return i.getDouble(t);case 13:return i.getUint32(t);default:g(`Invalid tiff type ${e}`)}}}class fe extends ce{static canHandle(e,t){return 225===e.getUint8(t+1)&&1165519206===e.getUint32(t+4)&&0===e.getUint16(t+8)}async parse(){this.parseHeader();let{options:e}=this;return e.ifd0.enabled&&await this.parseIfd0Block(),e.exif.enabled&&await this.safeParse(\"parseExifBlock\"),e.gps.enabled&&await this.safeParse(\"parseGpsBlock\"),e.interop.enabled&&await this.safeParse(\"parseInteropBlock\"),e.ifd1.enabled&&await this.safeParse(\"parseThumbnailBlock\"),this.createOutput()}safeParse(e){let t=this[e]();return void 0!==t.catch&&(t=t.catch(this.handleError)),t}findIfd0Offset(){void 0===this.ifd0Offset&&(this.ifd0Offset=this.chunk.getUint32(4))}findIfd1Offset(){if(void 0===this.ifd1Offset){this.findIfd0Offset();let e=this.chunk.getUint16(this.ifd0Offset),t=this.ifd0Offset+2+12*e;this.ifd1Offset=this.chunk.getUint32(t)}}parseBlock(e,t){let i=new Map;return this[t]=i,this.parseTags(e,t,i),i}async parseIfd0Block(){if(this.ifd0)return;let{file:e}=this;this.findIfd0Offset(),this.ifd0Offset<8&&g(\"Malformed EXIF data\"),!e.chunked&&this.ifd0Offset>e.byteLength&&g(`IFD0 offset points to outside of file.\\nthis.ifd0Offset: ${this.ifd0Offset}, file.byteLength: ${e.byteLength}`),e.tiff&&await e.ensureChunk(this.ifd0Offset,S(this.options));let t=this.parseBlock(this.ifd0Offset,\"ifd0\");return 0!==t.size?(this.exifOffset=t.get(34665),this.interopOffset=t.get(40965),this.gpsOffset=t.get(34853),this.xmp=t.get(700),this.iptc=t.get(33723),this.icc=t.get(34675),this.options.sanitize&&(t.delete(34665),t.delete(40965),t.delete(34853),t.delete(700),t.delete(33723),t.delete(34675)),t):void 0}async parseExifBlock(){if(this.exif)return;if(this.ifd0||await this.parseIfd0Block(),void 0===this.exifOffset)return;this.file.tiff&&await this.file.ensureChunk(this.exifOffset,S(this.options));let e=this.parseBlock(this.exifOffset,\"exif\");return this.interopOffset||(this.interopOffset=e.get(40965)),this.makerNote=e.get(37500),this.userComment=e.get(37510),this.options.sanitize&&(e.delete(40965),e.delete(37500),e.delete(37510)),this.unpack(e,41728),this.unpack(e,41729),e}unpack(e,t){let i=e.get(t);i&&1===i.length&&e.set(t,i[0])}async parseGpsBlock(){if(this.gps)return;if(this.ifd0||await this.parseIfd0Block(),void 0===this.gpsOffset)return;let e=this.parseBlock(this.gpsOffset,\"gps\");return e&&e.has(2)&&e.has(4)&&(e.set(\"latitude\",de(...e.get(2),e.get(1))),e.set(\"longitude\",de(...e.get(4),e.get(3)))),e}async parseInteropBlock(){if(!this.interop&&(this.ifd0||await this.parseIfd0Block(),void 0!==this.interopOffset||this.exif||await this.parseExifBlock(),void 0!==this.interopOffset))return this.parseBlock(this.interopOffset,\"interop\")}async parseThumbnailBlock(e=!1){if(!this.ifd1&&!this.ifd1Parsed&&(!this.options.mergeOutput||e))return this.findIfd1Offset(),this.ifd1Offset>0&&(this.parseBlock(this.ifd1Offset,\"ifd1\"),this.ifd1Parsed=!0),this.ifd1}async extractThumbnail(){if(this.headerParsed||this.parseHeader(),this.ifd1Parsed||await this.parseThumbnailBlock(!0),void 0===this.ifd1)return;let e=this.ifd1.get(513),t=this.ifd1.get(514);return this.chunk.getUint8Array(e,t)}get image(){return this.ifd0}get thumbnail(){return this.ifd1}createOutput(){let e,t,i,n={};for(t of H)if(e=this[t],!p(e))if(i=this.canTranslate?this.translateBlock(e,t):Object.fromEntries(e),this.options.mergeOutput){if(\"ifd1\"===t)continue;Object.assign(n,i)}else n[t]=i;return this.makerNote&&(n.makerNote=this.makerNote),this.userComment&&(n.userComment=this.userComment),n}assignToOutput(e,t){if(this.globalOptions.mergeOutput)Object.assign(e,t);else for(let[i,n]of Object.entries(t))this.assignObjectToOutput(e,i,n)}}function de(e,t,i,n){var s=e+t/60+i/3600;return\"S\"!==n&&\"W\"!==n||(s*=-1),s}c(fe,\"type\",\"tiff\"),c(fe,\"headerLength\",10),T.set(\"tiff\",fe);var pe=Object.freeze({__proto__:null,default:ne,Exifr:te,fileParsers:w,segmentParsers:T,fileReaders:A,tagKeys:E,tagValues:B,tagRevivers:N,createDictionary:U,extendDictionary:F,fetchUrlAsArrayBuffer:M,readBlobAsArrayBuffer:R,chunkedProps:G,otherSegments:V,segments:z,tiffBlocks:H,segmentsAndBlocks:j,tiffExtractables:W,inheritables:K,allFormatters:X,Options:q,parse:ie});const ge={ifd0:!1,ifd1:!1,exif:!1,gps:!1,interop:!1,sanitize:!1,reviveValues:!0,translateKeys:!1,translateValues:!1,mergeOutput:!1},me=Object.assign({},ge,{firstChunkSize:4e4,gps:[1,2,3,4]});async function Se(e){let t=new te(me);await t.read(e);let i=await t.parse();if(i&&i.gps){let{latitude:e,longitude:t}=i.gps;return{latitude:e,longitude:t}}}const Ce=Object.assign({},ge,{tiff:!1,ifd1:!0,mergeOutput:!1});async function ye(e){let t=new te(Ce);await t.read(e);let i=await t.extractThumbnail();return i&&a?s.from(i):i}async function be(e){let t=await this.thumbnail(e);if(void 0!==t){let e=new Blob([t]);return URL.createObjectURL(e)}}const Ie=Object.assign({},ge,{firstChunkSize:4e4,ifd0:[274]});async function Pe(e){let t=new te(Ie);await t.read(e);let i=await t.parse();if(i&&i.ifd0)return i.ifd0[274]}const ke=Object.freeze({1:{dimensionSwapped:!1,scaleX:1,scaleY:1,deg:0,rad:0},2:{dimensionSwapped:!1,scaleX:-1,scaleY:1,deg:0,rad:0},3:{dimensionSwapped:!1,scaleX:1,scaleY:1,deg:180,rad:180*Math.PI/180},4:{dimensionSwapped:!1,scaleX:-1,scaleY:1,deg:180,rad:180*Math.PI/180},5:{dimensionSwapped:!0,scaleX:1,scaleY:-1,deg:90,rad:90*Math.PI/180},6:{dimensionSwapped:!0,scaleX:1,scaleY:1,deg:90,rad:90*Math.PI/180},7:{dimensionSwapped:!0,scaleX:1,scaleY:-1,deg:270,rad:270*Math.PI/180},8:{dimensionSwapped:!0,scaleX:1,scaleY:1,deg:270,rad:270*Math.PI/180}});let we=!0,Te=!0;if(\"object\"==typeof navigator){let e=navigator.userAgent;if(e.includes(\"iPad\")||e.includes(\"iPhone\")){let t=e.match(/OS (\\d+)_(\\d+)/);if(t){let[,e,i]=t,n=Number(e)+.1*Number(i);we=n<13.4,Te=!1}}else if(e.includes(\"OS X 10\")){let[,t]=e.match(/OS X 10[_.](\\d+)/);we=Te=Number(t)<15}if(e.includes(\"Chrome/\")){let[,t]=e.match(/Chrome\\/(\\d+)/);we=Te=Number(t)<81}else if(e.includes(\"Firefox/\")){let[,t]=e.match(/Firefox\\/(\\d+)/);we=Te=Number(t)<77}}async function Ae(e){let t=await Pe(e);return Object.assign({canvas:we,css:Te},ke[t])}class De extends I{constructor(...e){super(...e),c(this,\"ranges\",new Oe),0!==this.byteLength&&this.ranges.add(0,this.byteLength)}_tryExtend(e,t,i){if(0===e&&0===this.byteLength&&i){let e=new DataView(i.buffer||i,i.byteOffset,i.byteLength);this._swapDataView(e)}else{let i=e+t;if(i>this.byteLength){let{dataView:e}=this._extend(i);this._swapDataView(e)}}}_extend(e){let t;t=a?s.allocUnsafe(e):new Uint8Array(e);let i=new DataView(t.buffer,t.byteOffset,t.byteLength);return t.set(new Uint8Array(this.buffer,this.byteOffset,this.byteLength),0),{uintView:t,dataView:i}}subarray(e,t,i=!1){return t=t||this._lengthToEnd(e),i&&this._tryExtend(e,t),this.ranges.add(e,t),super.subarray(e,t)}set(e,t,i=!1){i&&this._tryExtend(t,e.byteLength,e);let n=super.set(e,t);return this.ranges.add(t,n.byteLength),n}async ensureChunk(e,t){this.chunked&&(this.ranges.available(e,t)||await this.readChunk(e,t))}available(e,t){return this.ranges.available(e,t)}}class Oe{constructor(){c(this,\"list\",[])}get length(){return this.list.length}add(e,t,i=0){let n=e+t,s=this.list.filter((t=>xe(e,t.offset,n)||xe(e,t.end,n)));if(s.length>0){e=Math.min(e,...s.map((e=>e.offset))),n=Math.max(n,...s.map((e=>e.end))),t=n-e;let i=s.shift();i.offset=e,i.length=t,i.end=n,this.list=this.list.filter((e=>!s.includes(e)))}else this.list.push({offset:e,length:t,end:n})}available(e,t){let i=e+t;return this.list.some((t=>t.offset<=e&&i<=t.end))}}function xe(e,t,i){return e<=t&&t<=i}class ve extends De{constructor(e,t){super(0),c(this,\"chunksRead\",0),this.input=e,this.options=t}async readWhole(){this.chunked=!1,await this.readChunk(this.nextChunkOffset)}async readChunked(){this.chunked=!0,await this.readChunk(0,this.options.firstChunkSize)}async readNextChunk(e=this.nextChunkOffset){if(this.fullyRead)return this.chunksRead++,!1;let t=this.options.chunkSize,i=await this.readChunk(e,t);return!!i&&i.byteLength===t}async readChunk(e,t){if(this.chunksRead++,0!==(t=this.safeWrapAddress(e,t)))return this._readChunk(e,t)}safeWrapAddress(e,t){return void 0!==this.size&&e+t>this.size?Math.max(0,this.size-e):t}get nextChunkOffset(){if(0!==this.ranges.list.length)return this.ranges.list[0].length}get canReadNextChunk(){return this.chunksRead<this.options.chunkLimit}get fullyRead(){return void 0!==this.size&&this.nextChunkOffset===this.size}read(){return this.options.chunked?this.readChunked():this.readWhole()}close(){}}A.set(\"blob\",class extends ve{async readWhole(){this.chunked=!1;let e=await R(this.input);this._swapArrayBuffer(e)}readChunked(){return this.chunked=!0,this.size=this.input.size,super.readChunked()}async _readChunk(e,t){let i=t?e+t:void 0,n=this.input.slice(e,i),s=await R(n);return this.set(s,e,!0)}});var Me=Object.freeze({__proto__:null,default:pe,Exifr:te,fileParsers:w,segmentParsers:T,fileReaders:A,tagKeys:E,tagValues:B,tagRevivers:N,createDictionary:U,extendDictionary:F,fetchUrlAsArrayBuffer:M,readBlobAsArrayBuffer:R,chunkedProps:G,otherSegments:V,segments:z,tiffBlocks:H,segmentsAndBlocks:j,tiffExtractables:W,inheritables:K,allFormatters:X,Options:q,parse:ie,gpsOnlyOptions:me,gps:Se,thumbnailOnlyOptions:Ce,thumbnail:ye,thumbnailUrl:be,orientationOnlyOptions:Ie,orientation:Pe,rotations:ke,get rotateCanvas(){return we},get rotateCss(){return Te},rotation:Ae});A.set(\"url\",class extends ve{async readWhole(){this.chunked=!1;let e=await M(this.input);e instanceof ArrayBuffer?this._swapArrayBuffer(e):e instanceof Uint8Array&&this._swapBuffer(e)}async _readChunk(e,t){let i=t?e+t-1:void 0,n=this.options.httpHeaders||{};(e||i)&&(n.range=`bytes=${[e,i].join(\"-\")}`);let s=await h(this.input,{headers:n}),r=await s.arrayBuffer(),a=r.byteLength;if(416!==s.status)return a!==t&&(this.size=e+a),this.set(r,e,!0)}});I.prototype.getUint64=function(e){let t=this.getUint32(e),i=this.getUint32(e+4);return t<1048575?t<<32|i:void 0!==typeof r?(console.warn(\"Using BigInt because of type 64uint but JS can only handle 53b numbers.\"),r(t)<<r(32)|r(i)):void g(\"Trying to read 64b value but JS can only handle 53b numbers.\")};class Re extends se{parseBoxes(e=0){let t=[];for(;e<this.file.byteLength-4;){let i=this.parseBoxHead(e);if(t.push(i),0===i.length)break;e+=i.length}return t}parseSubBoxes(e){e.boxes=this.parseBoxes(e.start)}findBox(e,t){return void 0===e.boxes&&this.parseSubBoxes(e),e.boxes.find((e=>e.kind===t))}parseBoxHead(e){let t=this.file.getUint32(e),i=this.file.getString(e+4,4),n=e+8;return 1===t&&(t=this.file.getUint64(e+8),n+=8),{offset:e,length:t,kind:i,start:n}}parseBoxFullHead(e){if(void 0!==e.version)return;let t=this.file.getUint32(e.start);e.version=t>>24,e.start+=4}}class Le extends Re{static canHandle(e,t){if(0!==t)return!1;let i=e.getUint16(2);if(i>50)return!1;let n=16,s=[];for(;n<i;)s.push(e.getString(n,4)),n+=4;return s.includes(this.type)}async parse(){let e=this.file.getUint32(0),t=this.parseBoxHead(e);for(;\"meta\"!==t.kind;)e+=t.length,await this.file.ensureChunk(e,16),t=this.parseBoxHead(e);await this.file.ensureChunk(t.offset,t.length),this.parseBoxFullHead(t),this.parseSubBoxes(t),this.options.icc.enabled&&await this.findIcc(t),this.options.tiff.enabled&&await this.findExif(t)}async registerSegment(e,t,i){await this.file.ensureChunk(t,i);let n=this.file.subarray(t,i);this.createParser(e,n)}async findIcc(e){let t=this.findBox(e,\"iprp\");if(void 0===t)return;let i=this.findBox(t,\"ipco\");if(void 0===i)return;let n=this.findBox(i,\"colr\");void 0!==n&&await this.registerSegment(\"icc\",n.offset+12,n.length)}async findExif(e){let t=this.findBox(e,\"iinf\");if(void 0===t)return;let i=this.findBox(e,\"iloc\");if(void 0===i)return;let n=this.findExifLocIdInIinf(t),s=this.findExtentInIloc(i,n);if(void 0===s)return;let[r,a]=s;await this.file.ensureChunk(r,a);let o=4+this.file.getUint32(r);r+=o,a-=o,await this.registerSegment(\"tiff\",r,a)}findExifLocIdInIinf(e){this.parseBoxFullHead(e);let t,i,n,s,r=e.start,a=this.file.getUint16(r);for(r+=2;a--;){if(t=this.parseBoxHead(r),this.parseBoxFullHead(t),i=t.start,t.version>=2&&(n=3===t.version?4:2,s=this.file.getString(i+n+2,4),\"Exif\"===s))return this.file.getUintBytes(i,n);r+=t.length}}get8bits(e){let t=this.file.getUint8(e);return[t>>4,15&t]}findExtentInIloc(e,t){this.parseBoxFullHead(e);let i=e.start,[n,s]=this.get8bits(i++),[r,a]=this.get8bits(i++),o=2===e.version?4:2,l=1===e.version||2===e.version?2:0,h=a+n+s,u=2===e.version?4:2,c=this.file.getUintBytes(i,u);for(i+=u;c--;){let e=this.file.getUintBytes(i,o);i+=o+l+2+r;let u=this.file.getUint16(i);if(i+=2,e===t)return u>1&&console.warn(\"ILOC box has more than one extent but we're only processing one\\nPlease create an issue at https://github.com/MikeKovarik/exifr with this file\"),[this.file.getUintBytes(i+a,n),this.file.getUintBytes(i+a+n,s)];i+=u*h}}}class Ue extends Le{}c(Ue,\"type\",\"heic\");class Fe extends Le{}c(Fe,\"type\",\"avif\"),w.set(\"heic\",Ue),w.set(\"avif\",Fe),U(E,[\"ifd0\",\"ifd1\"],[[256,\"ImageWidth\"],[257,\"ImageHeight\"],[258,\"BitsPerSample\"],[259,\"Compression\"],[262,\"PhotometricInterpretation\"],[270,\"ImageDescription\"],[271,\"Make\"],[272,\"Model\"],[273,\"StripOffsets\"],[274,\"Orientation\"],[277,\"SamplesPerPixel\"],[278,\"RowsPerStrip\"],[279,\"StripByteCounts\"],[282,\"XResolution\"],[283,\"YResolution\"],[284,\"PlanarConfiguration\"],[296,\"ResolutionUnit\"],[301,\"TransferFunction\"],[305,\"Software\"],[306,\"ModifyDate\"],[315,\"Artist\"],[316,\"HostComputer\"],[317,\"Predictor\"],[318,\"WhitePoint\"],[319,\"PrimaryChromaticities\"],[513,\"ThumbnailOffset\"],[514,\"ThumbnailLength\"],[529,\"YCbCrCoefficients\"],[530,\"YCbCrSubSampling\"],[531,\"YCbCrPositioning\"],[532,\"ReferenceBlackWhite\"],[700,\"ApplicationNotes\"],[33432,\"Copyright\"],[33723,\"IPTC\"],[34665,\"ExifIFD\"],[34675,\"ICC\"],[34853,\"GpsIFD\"],[330,\"SubIFD\"],[40965,\"InteropIFD\"],[40091,\"XPTitle\"],[40092,\"XPComment\"],[40093,\"XPAuthor\"],[40094,\"XPKeywords\"],[40095,\"XPSubject\"]]),U(E,\"exif\",[[33434,\"ExposureTime\"],[33437,\"FNumber\"],[34850,\"ExposureProgram\"],[34852,\"SpectralSensitivity\"],[34855,\"ISO\"],[34858,\"TimeZoneOffset\"],[34859,\"SelfTimerMode\"],[34864,\"SensitivityType\"],[34865,\"StandardOutputSensitivity\"],[34866,\"RecommendedExposureIndex\"],[34867,\"ISOSpeed\"],[34868,\"ISOSpeedLatitudeyyy\"],[34869,\"ISOSpeedLatitudezzz\"],[36864,\"ExifVersion\"],[36867,\"DateTimeOriginal\"],[36868,\"CreateDate\"],[36873,\"GooglePlusUploadCode\"],[36880,\"OffsetTime\"],[36881,\"OffsetTimeOriginal\"],[36882,\"OffsetTimeDigitized\"],[37121,\"ComponentsConfiguration\"],[37122,\"CompressedBitsPerPixel\"],[37377,\"ShutterSpeedValue\"],[37378,\"ApertureValue\"],[37379,\"BrightnessValue\"],[37380,\"ExposureCompensation\"],[37381,\"MaxApertureValue\"],[37382,\"SubjectDistance\"],[37383,\"MeteringMode\"],[37384,\"LightSource\"],[37385,\"Flash\"],[37386,\"FocalLength\"],[37393,\"ImageNumber\"],[37394,\"SecurityClassification\"],[37395,\"ImageHistory\"],[37396,\"SubjectArea\"],[37500,\"MakerNote\"],[37510,\"UserComment\"],[37520,\"SubSecTime\"],[37521,\"SubSecTimeOriginal\"],[37522,\"SubSecTimeDigitized\"],[37888,\"AmbientTemperature\"],[37889,\"Humidity\"],[37890,\"Pressure\"],[37891,\"WaterDepth\"],[37892,\"Acceleration\"],[37893,\"CameraElevationAngle\"],[40960,\"FlashpixVersion\"],[40961,\"ColorSpace\"],[40962,\"ExifImageWidth\"],[40963,\"ExifImageHeight\"],[40964,\"RelatedSoundFile\"],[41483,\"FlashEnergy\"],[41486,\"FocalPlaneXResolution\"],[41487,\"FocalPlaneYResolution\"],[41488,\"FocalPlaneResolutionUnit\"],[41492,\"SubjectLocation\"],[41493,\"ExposureIndex\"],[41495,\"SensingMethod\"],[41728,\"FileSource\"],[41729,\"SceneType\"],[41730,\"CFAPattern\"],[41985,\"CustomRendered\"],[41986,\"ExposureMode\"],[41987,\"WhiteBalance\"],[41988,\"DigitalZoomRatio\"],[41989,\"FocalLengthIn35mmFormat\"],[41990,\"SceneCaptureType\"],[41991,\"GainControl\"],[41992,\"Contrast\"],[41993,\"Saturation\"],[41994,\"Sharpness\"],[41996,\"SubjectDistanceRange\"],[42016,\"ImageUniqueID\"],[42032,\"OwnerName\"],[42033,\"SerialNumber\"],[42034,\"LensInfo\"],[42035,\"LensMake\"],[42036,\"LensModel\"],[42037,\"LensSerialNumber\"],[42080,\"CompositeImage\"],[42081,\"CompositeImageCount\"],[42082,\"CompositeImageExposureTimes\"],[42240,\"Gamma\"],[59932,\"Padding\"],[59933,\"OffsetSchema\"],[65e3,\"OwnerName\"],[65001,\"SerialNumber\"],[65002,\"Lens\"],[65100,\"RawFile\"],[65101,\"Converter\"],[65102,\"WhiteBalance\"],[65105,\"Exposure\"],[65106,\"Shadows\"],[65107,\"Brightness\"],[65108,\"Contrast\"],[65109,\"Saturation\"],[65110,\"Sharpness\"],[65111,\"Smoothness\"],[65112,\"MoireFilter\"],[40965,\"InteropIFD\"]]),U(E,\"gps\",[[0,\"GPSVersionID\"],[1,\"GPSLatitudeRef\"],[2,\"GPSLatitude\"],[3,\"GPSLongitudeRef\"],[4,\"GPSLongitude\"],[5,\"GPSAltitudeRef\"],[6,\"GPSAltitude\"],[7,\"GPSTimeStamp\"],[8,\"GPSSatellites\"],[9,\"GPSStatus\"],[10,\"GPSMeasureMode\"],[11,\"GPSDOP\"],[12,\"GPSSpeedRef\"],[13,\"GPSSpeed\"],[14,\"GPSTrackRef\"],[15,\"GPSTrack\"],[16,\"GPSImgDirectionRef\"],[17,\"GPSImgDirection\"],[18,\"GPSMapDatum\"],[19,\"GPSDestLatitudeRef\"],[20,\"GPSDestLatitude\"],[21,\"GPSDestLongitudeRef\"],[22,\"GPSDestLongitude\"],[23,\"GPSDestBearingRef\"],[24,\"GPSDestBearing\"],[25,\"GPSDestDistanceRef\"],[26,\"GPSDestDistance\"],[27,\"GPSProcessingMethod\"],[28,\"GPSAreaInformation\"],[29,\"GPSDateStamp\"],[30,\"GPSDifferential\"],[31,\"GPSHPositioningError\"]]),U(B,[\"ifd0\",\"ifd1\"],[[274,{1:\"Horizontal (normal)\",2:\"Mirror horizontal\",3:\"Rotate 180\",4:\"Mirror vertical\",5:\"Mirror horizontal and rotate 270 CW\",6:\"Rotate 90 CW\",7:\"Mirror horizontal and rotate 90 CW\",8:\"Rotate 270 CW\"}],[296,{1:\"None\",2:\"inches\",3:\"cm\"}]]);let Ee=U(B,\"exif\",[[34850,{0:\"Not defined\",1:\"Manual\",2:\"Normal program\",3:\"Aperture priority\",4:\"Shutter priority\",5:\"Creative program\",6:\"Action program\",7:\"Portrait mode\",8:\"Landscape mode\"}],[37121,{0:\"-\",1:\"Y\",2:\"Cb\",3:\"Cr\",4:\"R\",5:\"G\",6:\"B\"}],[37383,{0:\"Unknown\",1:\"Average\",2:\"CenterWeightedAverage\",3:\"Spot\",4:\"MultiSpot\",5:\"Pattern\",6:\"Partial\",255:\"Other\"}],[37384,{0:\"Unknown\",1:\"Daylight\",2:\"Fluorescent\",3:\"Tungsten (incandescent light)\",4:\"Flash\",9:\"Fine weather\",10:\"Cloudy weather\",11:\"Shade\",12:\"Daylight fluorescent (D 5700 - 7100K)\",13:\"Day white fluorescent (N 4600 - 5400K)\",14:\"Cool white fluorescent (W 3900 - 4500K)\",15:\"White fluorescent (WW 3200 - 3700K)\",17:\"Standard light A\",18:\"Standard light B\",19:\"Standard light C\",20:\"D55\",21:\"D65\",22:\"D75\",23:\"D50\",24:\"ISO studio tungsten\",255:\"Other\"}],[37385,{0:\"Flash did not fire\",1:\"Flash fired\",5:\"Strobe return light not detected\",7:\"Strobe return light detected\",9:\"Flash fired, compulsory flash mode\",13:\"Flash fired, compulsory flash mode, return light not detected\",15:\"Flash fired, compulsory flash mode, return light detected\",16:\"Flash did not fire, compulsory flash mode\",24:\"Flash did not fire, auto mode\",25:\"Flash fired, auto mode\",29:\"Flash fired, auto mode, return light not detected\",31:\"Flash fired, auto mode, return light detected\",32:\"No flash function\",65:\"Flash fired, red-eye reduction mode\",69:\"Flash fired, red-eye reduction mode, return light not detected\",71:\"Flash fired, red-eye reduction mode, return light detected\",73:\"Flash fired, compulsory flash mode, red-eye reduction mode\",77:\"Flash fired, compulsory flash mode, red-eye reduction mode, return light not detected\",79:\"Flash fired, compulsory flash mode, red-eye reduction mode, return light detected\",89:\"Flash fired, auto mode, red-eye reduction mode\",93:\"Flash fired, auto mode, return light not detected, red-eye reduction mode\",95:\"Flash fired, auto mode, return light detected, red-eye reduction mode\"}],[41495,{1:\"Not defined\",2:\"One-chip color area sensor\",3:\"Two-chip color area sensor\",4:\"Three-chip color area sensor\",5:\"Color sequential area sensor\",7:\"Trilinear sensor\",8:\"Color sequential linear sensor\"}],[41728,{1:\"Film Scanner\",2:\"Reflection Print Scanner\",3:\"Digital Camera\"}],[41729,{1:\"Directly photographed\"}],[41985,{0:\"Normal\",1:\"Custom\",2:\"HDR (no original saved)\",3:\"HDR (original saved)\",4:\"Original (for HDR)\",6:\"Panorama\",7:\"Portrait HDR\",8:\"Portrait\"}],[41986,{0:\"Auto\",1:\"Manual\",2:\"Auto bracket\"}],[41987,{0:\"Auto\",1:\"Manual\"}],[41990,{0:\"Standard\",1:\"Landscape\",2:\"Portrait\",3:\"Night\",4:\"Other\"}],[41991,{0:\"None\",1:\"Low gain up\",2:\"High gain up\",3:\"Low gain down\",4:\"High gain down\"}],[41996,{0:\"Unknown\",1:\"Macro\",2:\"Close\",3:\"Distant\"}],[42080,{0:\"Unknown\",1:\"Not a Composite Image\",2:\"General Composite Image\",3:\"Composite Image Captured While Shooting\"}]]);const Be={1:\"No absolute unit of measurement\",2:\"Inch\",3:\"Centimeter\"};Ee.set(37392,Be),Ee.set(41488,Be);const Ne={0:\"Normal\",1:\"Low\",2:\"High\"};function Ge(e){return\"object\"==typeof e&&void 0!==e.length?e[0]:e}function Ve(e){let t=Array.from(e).slice(1);return t[1]>15&&(t=t.map((e=>String.fromCharCode(e)))),\"0\"!==t[2]&&0!==t[2]||t.pop(),t.join(\".\")}function ze(e){if(\"string\"==typeof e){var[t,i,n,s,r,a]=e.trim().split(/[-: ]/g).map(Number),o=new Date(t,i-1,n);return Number.isNaN(s)||Number.isNaN(r)||Number.isNaN(a)||(o.setHours(s),o.setMinutes(r),o.setSeconds(a)),Number.isNaN(+o)?e:o}}function He(e){if(\"string\"==typeof e)return e;let t=[];if(0===e[1]&&0===e[e.length-1])for(let i=0;i<e.length;i+=2)t.push(je(e[i+1],e[i]));else for(let i=0;i<e.length;i+=2)t.push(je(e[i],e[i+1]));return m(String.fromCodePoint(...t))}function je(e,t){return e<<8|t}Ee.set(41992,Ne),Ee.set(41993,Ne),Ee.set(41994,Ne),U(N,[\"ifd0\",\"ifd1\"],[[50827,function(e){return\"string\"!=typeof e?b(e):e}],[306,ze],[40091,He],[40092,He],[40093,He],[40094,He],[40095,He]]),U(N,\"exif\",[[40960,Ve],[36864,Ve],[36867,ze],[36868,ze],[40962,Ge],[40963,Ge]]),U(N,\"gps\",[[0,e=>Array.from(e).join(\".\")],[7,e=>Array.from(e).join(\":\")]]);class We extends re{static canHandle(e,t){return 225===e.getUint8(t+1)&&1752462448===e.getUint32(t+4)&&\"http://ns.adobe.com/\"===e.getString(t+4,\"http://ns.adobe.com/\".length)}static headerLength(e,t){return\"http://ns.adobe.com/xmp/extension/\"===e.getString(t+4,\"http://ns.adobe.com/xmp/extension/\".length)?79:4+\"http://ns.adobe.com/xap/1.0/\".length+1}static findPosition(e,t){let i=super.findPosition(e,t);return i.multiSegment=i.extended=79===i.headerLength,i.multiSegment?(i.chunkCount=e.getUint8(t+72),i.chunkNumber=e.getUint8(t+76),0!==e.getUint8(t+77)&&i.chunkNumber++):(i.chunkCount=1/0,i.chunkNumber=-1),i}static handleMultiSegments(e){return e.map((e=>e.chunk.getString())).join(\"\")}normalizeInput(e){return\"string\"==typeof e?e:I.from(e).getString()}parse(e=this.chunk){if(!this.localOptions.parse)return e;e=function(e){let t={},i={};for(let e of Ze)t[e]=[],i[e]=0;return e.replace(et,((e,n,s)=>{if(\"<\"===n){let n=++i[s];return t[s].push(n),`${e}#${n}`}return`${e}#${t[s].pop()}`}))}(e);let t=Xe.findAll(e,\"rdf\",\"Description\");0===t.length&&t.push(new Xe(\"rdf\",\"Description\",void 0,e));let i,n={};for(let e of t)for(let t of e.properties)i=Je(t.ns,n),_e(t,i);return function(e){let t;for(let i in e)t=e[i]=f(e[i]),void 0===t&&delete e[i];return f(e)}(n)}assignToOutput(e,t){if(this.localOptions.parse)for(let[i,n]of Object.entries(t))switch(i){case\"tiff\":this.assignObjectToOutput(e,\"ifd0\",n);break;case\"exif\":this.assignObjectToOutput(e,\"exif\",n);break;case\"xmlns\":break;default:this.assignObjectToOutput(e,i,n)}else e.xmp=t}}c(We,\"type\",\"xmp\"),c(We,\"multiSegment\",!0),T.set(\"xmp\",We);class Ke{static findAll(e){return qe(e,/([a-zA-Z0-9-]+):([a-zA-Z0-9-]+)=(\"[^\"]*\"|'[^']*')/gm).map(Ke.unpackMatch)}static unpackMatch(e){let t=e[1],i=e[2],n=e[3].slice(1,-1);return n=Qe(n),new Ke(t,i,n)}constructor(e,t,i){this.ns=e,this.name=t,this.value=i}serialize(){return this.value}}class Xe{static findAll(e,t,i){if(void 0!==t||void 0!==i){t=t||\"[\\\\w\\\\d-]+\",i=i||\"[\\\\w\\\\d-]+\";var n=new RegExp(`<(${t}):(${i})(#\\\\d+)?((\\\\s+?[\\\\w\\\\d-:]+=(\"[^\"]*\"|'[^']*'))*\\\\s*)(\\\\/>|>([\\\\s\\\\S]*?)<\\\\/\\\\1:\\\\2\\\\3>)`,\"gm\")}else n=/<([\\w\\d-]+):([\\w\\d-]+)(#\\d+)?((\\s+?[\\w\\d-:]+=(\"[^\"]*\"|'[^']*'))*\\s*)(\\/>|>([\\s\\S]*?)<\\/\\1:\\2\\3>)/gm;return qe(e,n).map(Xe.unpackMatch)}static unpackMatch(e){let t=e[1],i=e[2],n=e[4],s=e[8];return new Xe(t,i,n,s)}constructor(e,t,i,n){this.ns=e,this.name=t,this.attrString=i,this.innerXml=n,this.attrs=Ke.findAll(i),this.children=Xe.findAll(n),this.value=0===this.children.length?Qe(n):void 0,this.properties=[...this.attrs,...this.children]}get isPrimitive(){return void 0!==this.value&&0===this.attrs.length&&0===this.children.length}get isListContainer(){return 1===this.children.length&&this.children[0].isList}get isList(){let{ns:e,name:t}=this;return\"rdf\"===e&&(\"Seq\"===t||\"Bag\"===t||\"Alt\"===t)}get isListItem(){return\"rdf\"===this.ns&&\"li\"===this.name}serialize(){if(0===this.properties.length&&void 0===this.value)return;if(this.isPrimitive)return this.value;if(this.isListContainer)return this.children[0].serialize();if(this.isList)return $e(this.children.map(Ye));if(this.isListItem&&1===this.children.length&&0===this.attrs.length)return this.children[0].serialize();let e={};for(let t of this.properties)_e(t,e);return void 0!==this.value&&(e.value=this.value),f(e)}}function _e(e,t){let i=e.serialize();void 0!==i&&(t[e.name]=i)}var Ye=e=>e.serialize(),$e=e=>1===e.length?e[0]:e,Je=(e,t)=>t[e]?t[e]:t[e]={};function qe(e,t){let i,n=[];if(!e)return n;for(;null!==(i=t.exec(e));)n.push(i);return n}function Qe(e){if(function(e){return null==e||\"null\"===e||\"undefined\"===e||\"\"===e||\"\"===e.trim()}(e))return;let t=Number(e);if(!Number.isNaN(t))return t;let i=e.toLowerCase();return\"true\"===i||\"false\"!==i&&e.trim()}const Ze=[\"rdf:li\",\"rdf:Seq\",\"rdf:Bag\",\"rdf:Alt\",\"rdf:Description\"],et=new RegExp(`(<|\\\\/)(${Ze.join(\"|\")})`,\"g\");var tt=Object.freeze({__proto__:null,default:Me,Exifr:te,fileParsers:w,segmentParsers:T,fileReaders:A,tagKeys:E,tagValues:B,tagRevivers:N,createDictionary:U,extendDictionary:F,fetchUrlAsArrayBuffer:M,readBlobAsArrayBuffer:R,chunkedProps:G,otherSegments:V,segments:z,tiffBlocks:H,segmentsAndBlocks:j,tiffExtractables:W,inheritables:K,allFormatters:X,Options:q,parse:ie,gpsOnlyOptions:me,gps:Se,thumbnailOnlyOptions:Ce,thumbnail:ye,thumbnailUrl:be,orientationOnlyOptions:Ie,orientation:Pe,rotations:ke,get rotateCanvas(){return we},get rotateCss(){return Te},rotation:Ae});const it=[\"xmp\",\"icc\",\"iptc\",\"tiff\"],nt=()=>{};async function st(e,t,i){let n=new q(t);n.chunked=!1,void 0===i&&\"string\"==typeof e&&(i=function(e){let t=e.toLowerCase().split(\".\").pop();if(function(e){return\"exif\"===e||\"tiff\"===e||\"tif\"===e}(t))return\"tiff\";if(it.includes(t))return t}(e));let s=await D(e,n);if(i){if(it.includes(i))return rt(i,s,n);g(\"Invalid segment type\")}else{if(function(e){let t=e.getString(0,50).trim();return t.includes(\"<?xpacket\")||t.includes(\"<x:\")}(s))return rt(\"xmp\",s,n);for(let[e]of T){if(!it.includes(e))continue;let t=await rt(e,s,n).catch(nt);if(t)return t}g(\"Unknown file format\")}}async function rt(e,t,i){let n=i[e];return n.enabled=!0,n.parse=!0,T.get(e).parse(t,n)}let at=l(\"fs\",(e=>e.promises));A.set(\"fs\",class extends ve{async readWhole(){this.chunked=!1,this.fs=await at;let e=await this.fs.readFile(this.input);this._swapBuffer(e)}async readChunked(){this.chunked=!0,this.fs=await at,await this.open(),await this.readChunk(0,this.options.firstChunkSize)}async open(){void 0===this.fh&&(this.fh=await this.fs.open(this.input,\"r\"),this.size=(await this.fh.stat(this.input)).size)}async _readChunk(e,t){void 0===this.fh&&await this.open(),e+t>this.size&&(t=this.size-e);var i=this.subarray(e,t,!0);return await this.fh.read(i.dataView,0,t,e),i}async close(){if(this.fh){let e=this.fh;this.fh=void 0,await e.close()}}});A.set(\"base64\",class extends ve{constructor(...e){super(...e),this.input=this.input.replace(/^data:([^;]+);base64,/gim,\"\"),this.size=this.input.length/4*3,this.input.endsWith(\"==\")?this.size-=2:this.input.endsWith(\"=\")&&(this.size-=1)}async _readChunk(e,t){let i,n,r=this.input;void 0===e?(e=0,i=0,n=0):(i=4*Math.floor(e/3),n=e-i/4*3),void 0===t&&(t=this.size);let o=e+t,l=i+4*Math.ceil(o/3);r=r.slice(i,l);let h=Math.min(t,this.size-e);if(a){let t=s.from(r,\"base64\").slice(n,n+h);return this.set(t,e,!0)}{let t=this.subarray(e,h,!0),i=atob(r),s=t.toUint8();for(let e=0;e<h;e++)s[e]=i.charCodeAt(n+e);return t}}});class ot extends se{static canHandle(e,t){return 18761===t||19789===t}extendOptions(e){let{ifd0:t,xmp:i,iptc:n,icc:s}=e;i.enabled&&t.deps.add(700),n.enabled&&t.deps.add(33723),s.enabled&&t.deps.add(34675),t.finalizeFilters()}async parse(){let{tiff:e,xmp:t,iptc:i,icc:n}=this.options;if(e.enabled||t.enabled||i.enabled||n.enabled){let e=Math.max(S(this.options),this.options.chunkSize);await this.file.ensureChunk(0,e),this.createParser(\"tiff\",this.file),this.parsers.tiff.parseHeader(),await this.parsers.tiff.parseIfd0Block(),this.adaptTiffPropAsSegment(\"xmp\"),this.adaptTiffPropAsSegment(\"iptc\"),this.adaptTiffPropAsSegment(\"icc\")}}adaptTiffPropAsSegment(e){if(this.parsers.tiff[e]){let t=this.parsers.tiff[e];this.injectSegment(e,t)}}}c(ot,\"type\",\"tiff\"),w.set(\"tiff\",ot);let lt=l(\"zlib\");const ht=[\"ihdr\",\"iccp\",\"text\",\"itxt\",\"exif\"];class ut extends se{constructor(...e){super(...e),c(this,\"catchError\",(e=>this.errors.push(e))),c(this,\"metaChunks\",[]),c(this,\"unknownChunks\",[])}static canHandle(e,t){return 35152===t&&2303741511===e.getUint32(0)&&218765834===e.getUint32(4)}async parse(){let{file:e}=this;await this.findPngChunksInRange(\"PNG\\r\\n\u001a\\n\".length,e.byteLength),await this.readSegments(this.metaChunks),this.findIhdr(),this.parseTextChunks(),await this.findExif().catch(this.catchError),await this.findXmp().catch(this.catchError),await this.findIcc().catch(this.catchError)}async findPngChunksInRange(e,t){let{file:i}=this;for(;e<t;){let t=i.getUint32(e),n=i.getUint32(e+4),s=i.getString(e+4,4).toLowerCase(),r=t+4+4+4,a={type:s,offset:e,length:r,start:e+4+4,size:t,marker:n};ht.includes(s)?this.metaChunks.push(a):this.unknownChunks.push(a),e+=r}}parseTextChunks(){let e=this.metaChunks.filter((e=>\"text\"===e.type));for(let t of e){let[e,i]=this.file.getString(t.start,t.size).split(\"\\0\");this.injectKeyValToIhdr(e,i)}}injectKeyValToIhdr(e,t){let i=this.parsers.ihdr;i&&i.raw.set(e,t)}findIhdr(){let e=this.metaChunks.find((e=>\"ihdr\"===e.type));e&&!1!==this.options.ihdr.enabled&&this.createParser(\"ihdr\",e.chunk)}async findExif(){let e=this.metaChunks.find((e=>\"exif\"===e.type));e&&this.injectSegment(\"tiff\",e.chunk)}async findXmp(){let e=this.metaChunks.filter((e=>\"itxt\"===e.type));for(let t of e){\"XML:com.adobe.xmp\"===t.chunk.getString(0,\"XML:com.adobe.xmp\".length)&&this.injectSegment(\"xmp\",t.chunk)}}async findIcc(){let e=this.metaChunks.find((e=>\"iccp\"===e.type));if(!e)return;let{chunk:t}=e,i=t.getUint8Array(0,81),s=0;for(;s<80&&0!==i[s];)s++;let r=s+2,a=t.getString(0,s);if(this.injectKeyValToIhdr(\"ProfileName\",a),n){let e=await lt,i=t.getUint8Array(r);i=e.inflateSync(i),this.injectSegment(\"icc\",i)}}}c(ut,\"type\",\"png\"),w.set(\"png\",ut),U(E,\"interop\",[[1,\"InteropIndex\"],[2,\"InteropVersion\"],[4096,\"RelatedImageFileFormat\"],[4097,\"RelatedImageWidth\"],[4098,\"RelatedImageHeight\"]]),F(E,\"ifd0\",[[11,\"ProcessingSoftware\"],[254,\"SubfileType\"],[255,\"OldSubfileType\"],[263,\"Thresholding\"],[264,\"CellWidth\"],[265,\"CellLength\"],[266,\"FillOrder\"],[269,\"DocumentName\"],[280,\"MinSampleValue\"],[281,\"MaxSampleValue\"],[285,\"PageName\"],[286,\"XPosition\"],[287,\"YPosition\"],[290,\"GrayResponseUnit\"],[297,\"PageNumber\"],[321,\"HalftoneHints\"],[322,\"TileWidth\"],[323,\"TileLength\"],[332,\"InkSet\"],[337,\"TargetPrinter\"],[18246,\"Rating\"],[18249,\"RatingPercent\"],[33550,\"PixelScale\"],[34264,\"ModelTransform\"],[34377,\"PhotoshopSettings\"],[50706,\"DNGVersion\"],[50707,\"DNGBackwardVersion\"],[50708,\"UniqueCameraModel\"],[50709,\"LocalizedCameraModel\"],[50736,\"DNGLensInfo\"],[50739,\"ShadowScale\"],[50740,\"DNGPrivateData\"],[33920,\"IntergraphMatrix\"],[33922,\"ModelTiePoint\"],[34118,\"SEMInfo\"],[34735,\"GeoTiffDirectory\"],[34736,\"GeoTiffDoubleParams\"],[34737,\"GeoTiffAsciiParams\"],[50341,\"PrintIM\"],[50721,\"ColorMatrix1\"],[50722,\"ColorMatrix2\"],[50723,\"CameraCalibration1\"],[50724,\"CameraCalibration2\"],[50725,\"ReductionMatrix1\"],[50726,\"ReductionMatrix2\"],[50727,\"AnalogBalance\"],[50728,\"AsShotNeutral\"],[50729,\"AsShotWhiteXY\"],[50730,\"BaselineExposure\"],[50731,\"BaselineNoise\"],[50732,\"BaselineSharpness\"],[50734,\"LinearResponseLimit\"],[50735,\"CameraSerialNumber\"],[50741,\"MakerNoteSafety\"],[50778,\"CalibrationIlluminant1\"],[50779,\"CalibrationIlluminant2\"],[50781,\"RawDataUniqueID\"],[50827,\"OriginalRawFileName\"],[50828,\"OriginalRawFileData\"],[50831,\"AsShotICCProfile\"],[50832,\"AsShotPreProfileMatrix\"],[50833,\"CurrentICCProfile\"],[50834,\"CurrentPreProfileMatrix\"],[50879,\"ColorimetricReference\"],[50885,\"SRawType\"],[50898,\"PanasonicTitle\"],[50899,\"PanasonicTitle2\"],[50931,\"CameraCalibrationSig\"],[50932,\"ProfileCalibrationSig\"],[50933,\"ProfileIFD\"],[50934,\"AsShotProfileName\"],[50936,\"ProfileName\"],[50937,\"ProfileHueSatMapDims\"],[50938,\"ProfileHueSatMapData1\"],[50939,\"ProfileHueSatMapData2\"],[50940,\"ProfileToneCurve\"],[50941,\"ProfileEmbedPolicy\"],[50942,\"ProfileCopyright\"],[50964,\"ForwardMatrix1\"],[50965,\"ForwardMatrix2\"],[50966,\"PreviewApplicationName\"],[50967,\"PreviewApplicationVersion\"],[50968,\"PreviewSettingsName\"],[50969,\"PreviewSettingsDigest\"],[50970,\"PreviewColorSpace\"],[50971,\"PreviewDateTime\"],[50972,\"RawImageDigest\"],[50973,\"OriginalRawFileDigest\"],[50981,\"ProfileLookTableDims\"],[50982,\"ProfileLookTableData\"],[51043,\"TimeCodes\"],[51044,\"FrameRate\"],[51058,\"TStop\"],[51081,\"ReelName\"],[51089,\"OriginalDefaultFinalSize\"],[51090,\"OriginalBestQualitySize\"],[51091,\"OriginalDefaultCropSize\"],[51105,\"CameraLabel\"],[51107,\"ProfileHueSatMapEncoding\"],[51108,\"ProfileLookTableEncoding\"],[51109,\"BaselineExposureOffset\"],[51110,\"DefaultBlackRender\"],[51111,\"NewRawImageDigest\"],[51112,\"RawToPreviewGain\"]]);let ct=[[273,\"StripOffsets\"],[279,\"StripByteCounts\"],[288,\"FreeOffsets\"],[289,\"FreeByteCounts\"],[291,\"GrayResponseCurve\"],[292,\"T4Options\"],[293,\"T6Options\"],[300,\"ColorResponseUnit\"],[320,\"ColorMap\"],[324,\"TileOffsets\"],[325,\"TileByteCounts\"],[326,\"BadFaxLines\"],[327,\"CleanFaxData\"],[328,\"ConsecutiveBadFaxLines\"],[330,\"SubIFD\"],[333,\"InkNames\"],[334,\"NumberofInks\"],[336,\"DotRange\"],[338,\"ExtraSamples\"],[339,\"SampleFormat\"],[340,\"SMinSampleValue\"],[341,\"SMaxSampleValue\"],[342,\"TransferRange\"],[343,\"ClipPath\"],[344,\"XClipPathUnits\"],[345,\"YClipPathUnits\"],[346,\"Indexed\"],[347,\"JPEGTables\"],[351,\"OPIProxy\"],[400,\"GlobalParametersIFD\"],[401,\"ProfileType\"],[402,\"FaxProfile\"],[403,\"CodingMethods\"],[404,\"VersionYear\"],[405,\"ModeNumber\"],[433,\"Decode\"],[434,\"DefaultImageColor\"],[435,\"T82Options\"],[437,\"JPEGTables\"],[512,\"JPEGProc\"],[515,\"JPEGRestartInterval\"],[517,\"JPEGLosslessPredictors\"],[518,\"JPEGPointTransforms\"],[519,\"JPEGQTables\"],[520,\"JPEGDCTables\"],[521,\"JPEGACTables\"],[559,\"StripRowCounts\"],[999,\"USPTOMiscellaneous\"],[18247,\"XP_DIP_XML\"],[18248,\"StitchInfo\"],[28672,\"SonyRawFileType\"],[28688,\"SonyToneCurve\"],[28721,\"VignettingCorrection\"],[28722,\"VignettingCorrParams\"],[28724,\"ChromaticAberrationCorrection\"],[28725,\"ChromaticAberrationCorrParams\"],[28726,\"DistortionCorrection\"],[28727,\"DistortionCorrParams\"],[29895,\"SonyCropTopLeft\"],[29896,\"SonyCropSize\"],[32781,\"ImageID\"],[32931,\"WangTag1\"],[32932,\"WangAnnotation\"],[32933,\"WangTag3\"],[32934,\"WangTag4\"],[32953,\"ImageReferencePoints\"],[32954,\"RegionXformTackPoint\"],[32955,\"WarpQuadrilateral\"],[32956,\"AffineTransformMat\"],[32995,\"Matteing\"],[32996,\"DataType\"],[32997,\"ImageDepth\"],[32998,\"TileDepth\"],[33300,\"ImageFullWidth\"],[33301,\"ImageFullHeight\"],[33302,\"TextureFormat\"],[33303,\"WrapModes\"],[33304,\"FovCot\"],[33305,\"MatrixWorldToScreen\"],[33306,\"MatrixWorldToCamera\"],[33405,\"Model2\"],[33421,\"CFARepeatPatternDim\"],[33422,\"CFAPattern2\"],[33423,\"BatteryLevel\"],[33424,\"KodakIFD\"],[33445,\"MDFileTag\"],[33446,\"MDScalePixel\"],[33447,\"MDColorTable\"],[33448,\"MDLabName\"],[33449,\"MDSampleInfo\"],[33450,\"MDPrepDate\"],[33451,\"MDPrepTime\"],[33452,\"MDFileUnits\"],[33589,\"AdventScale\"],[33590,\"AdventRevision\"],[33628,\"UIC1Tag\"],[33629,\"UIC2Tag\"],[33630,\"UIC3Tag\"],[33631,\"UIC4Tag\"],[33918,\"IntergraphPacketData\"],[33919,\"IntergraphFlagRegisters\"],[33921,\"INGRReserved\"],[34016,\"Site\"],[34017,\"ColorSequence\"],[34018,\"IT8Header\"],[34019,\"RasterPadding\"],[34020,\"BitsPerRunLength\"],[34021,\"BitsPerExtendedRunLength\"],[34022,\"ColorTable\"],[34023,\"ImageColorIndicator\"],[34024,\"BackgroundColorIndicator\"],[34025,\"ImageColorValue\"],[34026,\"BackgroundColorValue\"],[34027,\"PixelIntensityRange\"],[34028,\"TransparencyIndicator\"],[34029,\"ColorCharacterization\"],[34030,\"HCUsage\"],[34031,\"TrapIndicator\"],[34032,\"CMYKEquivalent\"],[34152,\"AFCP_IPTC\"],[34232,\"PixelMagicJBIGOptions\"],[34263,\"JPLCartoIFD\"],[34306,\"WB_GRGBLevels\"],[34310,\"LeafData\"],[34687,\"TIFF_FXExtensions\"],[34688,\"MultiProfiles\"],[34689,\"SharedData\"],[34690,\"T88Options\"],[34732,\"ImageLayer\"],[34750,\"JBIGOptions\"],[34856,\"Opto-ElectricConvFactor\"],[34857,\"Interlace\"],[34908,\"FaxRecvParams\"],[34909,\"FaxSubAddress\"],[34910,\"FaxRecvTime\"],[34929,\"FedexEDR\"],[34954,\"LeafSubIFD\"],[37387,\"FlashEnergy\"],[37388,\"SpatialFrequencyResponse\"],[37389,\"Noise\"],[37390,\"FocalPlaneXResolution\"],[37391,\"FocalPlaneYResolution\"],[37392,\"FocalPlaneResolutionUnit\"],[37397,\"ExposureIndex\"],[37398,\"TIFF-EPStandardID\"],[37399,\"SensingMethod\"],[37434,\"CIP3DataFile\"],[37435,\"CIP3Sheet\"],[37436,\"CIP3Side\"],[37439,\"StoNits\"],[37679,\"MSDocumentText\"],[37680,\"MSPropertySetStorage\"],[37681,\"MSDocumentTextPosition\"],[37724,\"ImageSourceData\"],[40965,\"InteropIFD\"],[40976,\"SamsungRawPointersOffset\"],[40977,\"SamsungRawPointersLength\"],[41217,\"SamsungRawByteOrder\"],[41218,\"SamsungRawUnknown\"],[41484,\"SpatialFrequencyResponse\"],[41485,\"Noise\"],[41489,\"ImageNumber\"],[41490,\"SecurityClassification\"],[41491,\"ImageHistory\"],[41494,\"TIFF-EPStandardID\"],[41995,\"DeviceSettingDescription\"],[42112,\"GDALMetadata\"],[42113,\"GDALNoData\"],[44992,\"ExpandSoftware\"],[44993,\"ExpandLens\"],[44994,\"ExpandFilm\"],[44995,\"ExpandFilterLens\"],[44996,\"ExpandScanner\"],[44997,\"ExpandFlashLamp\"],[46275,\"HasselbladRawImage\"],[48129,\"PixelFormat\"],[48130,\"Transformation\"],[48131,\"Uncompressed\"],[48132,\"ImageType\"],[48256,\"ImageWidth\"],[48257,\"ImageHeight\"],[48258,\"WidthResolution\"],[48259,\"HeightResolution\"],[48320,\"ImageOffset\"],[48321,\"ImageByteCount\"],[48322,\"AlphaOffset\"],[48323,\"AlphaByteCount\"],[48324,\"ImageDataDiscard\"],[48325,\"AlphaDataDiscard\"],[50215,\"OceScanjobDesc\"],[50216,\"OceApplicationSelector\"],[50217,\"OceIDNumber\"],[50218,\"OceImageLogic\"],[50255,\"Annotations\"],[50459,\"HasselbladExif\"],[50547,\"OriginalFileName\"],[50560,\"USPTOOriginalContentType\"],[50656,\"CR2CFAPattern\"],[50710,\"CFAPlaneColor\"],[50711,\"CFALayout\"],[50712,\"LinearizationTable\"],[50713,\"BlackLevelRepeatDim\"],[50714,\"BlackLevel\"],[50715,\"BlackLevelDeltaH\"],[50716,\"BlackLevelDeltaV\"],[50717,\"WhiteLevel\"],[50718,\"DefaultScale\"],[50719,\"DefaultCropOrigin\"],[50720,\"DefaultCropSize\"],[50733,\"BayerGreenSplit\"],[50737,\"ChromaBlurRadius\"],[50738,\"AntiAliasStrength\"],[50752,\"RawImageSegmentation\"],[50780,\"BestQualityScale\"],[50784,\"AliasLayerMetadata\"],[50829,\"ActiveArea\"],[50830,\"MaskedAreas\"],[50935,\"NoiseReductionApplied\"],[50974,\"SubTileBlockSize\"],[50975,\"RowInterleaveFactor\"],[51008,\"OpcodeList1\"],[51009,\"OpcodeList2\"],[51022,\"OpcodeList3\"],[51041,\"NoiseProfile\"],[51114,\"CacheVersion\"],[51125,\"DefaultUserCrop\"],[51157,\"NikonNEFInfo\"],[65024,\"KdcIFD\"]];F(E,\"ifd0\",ct),F(E,\"exif\",ct),U(B,\"gps\",[[23,{M:\"Magnetic North\",T:\"True North\"}],[25,{K:\"Kilometers\",M:\"Miles\",N:\"Nautical Miles\"}]]);class ft extends re{static canHandle(e,t){return 224===e.getUint8(t+1)&&1246120262===e.getUint32(t+4)&&0===e.getUint8(t+8)}parse(){return this.parseTags(),this.translate(),this.output}parseTags(){this.raw=new Map([[0,this.chunk.getUint16(0)],[2,this.chunk.getUint8(2)],[3,this.chunk.getUint16(3)],[5,this.chunk.getUint16(5)],[7,this.chunk.getUint8(7)],[8,this.chunk.getUint8(8)]])}}c(ft,\"type\",\"jfif\"),c(ft,\"headerLength\",9),T.set(\"jfif\",ft),U(E,\"jfif\",[[0,\"JFIFVersion\"],[2,\"ResolutionUnit\"],[3,\"XResolution\"],[5,\"YResolution\"],[7,\"ThumbnailWidth\"],[8,\"ThumbnailHeight\"]]);class dt extends re{parse(){return this.parseTags(),this.translate(),this.output}parseTags(){this.raw=new Map([[0,this.chunk.getUint32(0)],[4,this.chunk.getUint32(4)],[8,this.chunk.getUint8(8)],[9,this.chunk.getUint8(9)],[10,this.chunk.getUint8(10)],[11,this.chunk.getUint8(11)],[12,this.chunk.getUint8(12)],...Array.from(this.raw)])}}c(dt,\"type\",\"ihdr\"),T.set(\"ihdr\",dt),U(E,\"ihdr\",[[0,\"ImageWidth\"],[4,\"ImageHeight\"],[8,\"BitDepth\"],[9,\"ColorType\"],[10,\"Compression\"],[11,\"Filter\"],[12,\"Interlace\"]]),U(B,\"ihdr\",[[9,{0:\"Grayscale\",2:\"RGB\",3:\"Palette\",4:\"Grayscale with Alpha\",6:\"RGB with Alpha\",DEFAULT:\"Unknown\"}],[10,{0:\"Deflate/Inflate\",DEFAULT:\"Unknown\"}],[11,{0:\"Adaptive\",DEFAULT:\"Unknown\"}],[12,{0:\"Noninterlaced\",1:\"Adam7 Interlace\",DEFAULT:\"Unknown\"}]]);class pt extends re{static canHandle(e,t){return 226===e.getUint8(t+1)&&1229144927===e.getUint32(t+4)}static findPosition(e,t){let i=super.findPosition(e,t);return i.chunkNumber=e.getUint8(t+16),i.chunkCount=e.getUint8(t+17),i.multiSegment=i.chunkCount>1,i}static handleMultiSegments(e){return function(e){let t=function(e){let t=e[0].constructor,i=0;for(let t of e)i+=t.length;let n=new t(i),s=0;for(let t of e)n.set(t,s),s+=t.length;return n}(e.map((e=>e.chunk.toUint8())));return new I(t)}(e)}parse(){return this.raw=new Map,this.parseHeader(),this.parseTags(),this.translate(),this.output}parseHeader(){let{raw:e}=this;this.chunk.byteLength<84&&g(\"ICC header is too short\");for(let[t,i]of Object.entries(gt)){t=parseInt(t,10);let n=i(this.chunk,t);\"\\0\\0\\0\\0\"!==n&&e.set(t,n)}}parseTags(){let e,t,i,n,s,{raw:r}=this,a=this.chunk.getUint32(128),o=132,l=this.chunk.byteLength;for(;a--;){if(e=this.chunk.getString(o,4),t=this.chunk.getUint32(o+4),i=this.chunk.getUint32(o+8),n=this.chunk.getString(t,4),t+i>l)return void console.warn(\"reached the end of the first ICC chunk. Enable options.tiff.multiSegment to read all ICC segments.\");s=this.parseTag(n,t,i),void 0!==s&&\"\\0\\0\\0\\0\"!==s&&r.set(e,s),o+=12}}parseTag(e,t,i){switch(e){case\"desc\":return this.parseDesc(t);case\"mluc\":return this.parseMluc(t);case\"text\":return this.parseText(t,i);case\"sig \":return this.parseSig(t)}if(!(t+i>this.chunk.byteLength))return this.chunk.getUint8Array(t,i)}parseDesc(e){let t=this.chunk.getUint32(e+8)-1;return m(this.chunk.getString(e+12,t))}parseText(e,t){return m(this.chunk.getString(e+8,t-8))}parseSig(e){return m(this.chunk.getString(e+8,4))}parseMluc(e){let{chunk:t}=this,i=t.getUint32(e+8),n=t.getUint32(e+12),s=e+16,r=[];for(let a=0;a<i;a++){let i=t.getString(s+0,2),a=t.getString(s+2,2),o=t.getUint32(s+4),l=t.getUint32(s+8)+e,h=m(t.getUnicodeString(l,o));r.push({lang:i,country:a,text:h}),s+=n}return 1===i?r[0].text:r}translateValue(e,t){return\"string\"==typeof e?t[e]||t[e.toLowerCase()]||e:t[e]||e}}c(pt,\"type\",\"icc\"),c(pt,\"multiSegment\",!0),c(pt,\"headerLength\",18);const gt={4:mt,8:function(e,t){return[e.getUint8(t),e.getUint8(t+1)>>4,e.getUint8(t+1)%16].map((e=>e.toString(10))).join(\".\")},12:mt,16:mt,20:mt,24:function(e,t){const i=e.getUint16(t),n=e.getUint16(t+2)-1,s=e.getUint16(t+4),r=e.getUint16(t+6),a=e.getUint16(t+8),o=e.getUint16(t+10);return new Date(Date.UTC(i,n,s,r,a,o))},36:mt,40:mt,48:mt,52:mt,64:(e,t)=>e.getUint32(t),80:mt};function mt(e,t){return m(e.getString(t,4))}T.set(\"icc\",pt),U(E,\"icc\",[[4,\"ProfileCMMType\"],[8,\"ProfileVersion\"],[12,\"ProfileClass\"],[16,\"ColorSpaceData\"],[20,\"ProfileConnectionSpace\"],[24,\"ProfileDateTime\"],[36,\"ProfileFileSignature\"],[40,\"PrimaryPlatform\"],[44,\"CMMFlags\"],[48,\"DeviceManufacturer\"],[52,\"DeviceModel\"],[56,\"DeviceAttributes\"],[64,\"RenderingIntent\"],[68,\"ConnectionSpaceIlluminant\"],[80,\"ProfileCreator\"],[84,\"ProfileID\"],[\"Header\",\"ProfileHeader\"],[\"MS00\",\"WCSProfiles\"],[\"bTRC\",\"BlueTRC\"],[\"bXYZ\",\"BlueMatrixColumn\"],[\"bfd\",\"UCRBG\"],[\"bkpt\",\"MediaBlackPoint\"],[\"calt\",\"CalibrationDateTime\"],[\"chad\",\"ChromaticAdaptation\"],[\"chrm\",\"Chromaticity\"],[\"ciis\",\"ColorimetricIntentImageState\"],[\"clot\",\"ColorantTableOut\"],[\"clro\",\"ColorantOrder\"],[\"clrt\",\"ColorantTable\"],[\"cprt\",\"ProfileCopyright\"],[\"crdi\",\"CRDInfo\"],[\"desc\",\"ProfileDescription\"],[\"devs\",\"DeviceSettings\"],[\"dmdd\",\"DeviceModelDesc\"],[\"dmnd\",\"DeviceMfgDesc\"],[\"dscm\",\"ProfileDescriptionML\"],[\"fpce\",\"FocalPlaneColorimetryEstimates\"],[\"gTRC\",\"GreenTRC\"],[\"gXYZ\",\"GreenMatrixColumn\"],[\"gamt\",\"Gamut\"],[\"kTRC\",\"GrayTRC\"],[\"lumi\",\"Luminance\"],[\"meas\",\"Measurement\"],[\"meta\",\"Metadata\"],[\"mmod\",\"MakeAndModel\"],[\"ncl2\",\"NamedColor2\"],[\"ncol\",\"NamedColor\"],[\"ndin\",\"NativeDisplayInfo\"],[\"pre0\",\"Preview0\"],[\"pre1\",\"Preview1\"],[\"pre2\",\"Preview2\"],[\"ps2i\",\"PS2RenderingIntent\"],[\"ps2s\",\"PostScript2CSA\"],[\"psd0\",\"PostScript2CRD0\"],[\"psd1\",\"PostScript2CRD1\"],[\"psd2\",\"PostScript2CRD2\"],[\"psd3\",\"PostScript2CRD3\"],[\"pseq\",\"ProfileSequenceDesc\"],[\"psid\",\"ProfileSequenceIdentifier\"],[\"psvm\",\"PS2CRDVMSize\"],[\"rTRC\",\"RedTRC\"],[\"rXYZ\",\"RedMatrixColumn\"],[\"resp\",\"OutputResponse\"],[\"rhoc\",\"ReflectionHardcopyOrigColorimetry\"],[\"rig0\",\"PerceptualRenderingIntentGamut\"],[\"rig2\",\"SaturationRenderingIntentGamut\"],[\"rpoc\",\"ReflectionPrintOutputColorimetry\"],[\"sape\",\"SceneAppearanceEstimates\"],[\"scoe\",\"SceneColorimetryEstimates\"],[\"scrd\",\"ScreeningDesc\"],[\"scrn\",\"Screening\"],[\"targ\",\"CharTarget\"],[\"tech\",\"Technology\"],[\"vcgt\",\"VideoCardGamma\"],[\"view\",\"ViewingConditions\"],[\"vued\",\"ViewingCondDesc\"],[\"wtpt\",\"MediaWhitePoint\"]]);const St={\"4d2p\":\"Erdt Systems\",AAMA:\"Aamazing Technologies\",ACER:\"Acer\",ACLT:\"Acolyte Color Research\",ACTI:\"Actix Sytems\",ADAR:\"Adara Technology\",ADBE:\"Adobe\",ADI:\"ADI Systems\",AGFA:\"Agfa Graphics\",ALMD:\"Alps Electric\",ALPS:\"Alps Electric\",ALWN:\"Alwan Color Expertise\",AMTI:\"Amiable Technologies\",AOC:\"AOC International\",APAG:\"Apago\",APPL:\"Apple Computer\",AST:\"AST\",\"AT&T\":\"AT&T\",BAEL:\"BARBIERI electronic\",BRCO:\"Barco NV\",BRKP:\"Breakpoint\",BROT:\"Brother\",BULL:\"Bull\",BUS:\"Bus Computer Systems\",\"C-IT\":\"C-Itoh\",CAMR:\"Intel\",CANO:\"Canon\",CARR:\"Carroll Touch\",CASI:\"Casio\",CBUS:\"Colorbus PL\",CEL:\"Crossfield\",CELx:\"Crossfield\",CGS:\"CGS Publishing Technologies International\",CHM:\"Rochester Robotics\",CIGL:\"Colour Imaging Group, London\",CITI:\"Citizen\",CL00:\"Candela\",CLIQ:\"Color IQ\",CMCO:\"Chromaco\",CMiX:\"CHROMiX\",COLO:\"Colorgraphic Communications\",COMP:\"Compaq\",COMp:\"Compeq/Focus Technology\",CONR:\"Conrac Display Products\",CORD:\"Cordata Technologies\",CPQ:\"Compaq\",CPRO:\"ColorPro\",CRN:\"Cornerstone\",CTX:\"CTX International\",CVIS:\"ColorVision\",CWC:\"Fujitsu Laboratories\",DARI:\"Darius Technology\",DATA:\"Dataproducts\",DCP:\"Dry Creek Photo\",DCRC:\"Digital Contents Resource Center, Chung-Ang University\",DELL:\"Dell Computer\",DIC:\"Dainippon Ink and Chemicals\",DICO:\"Diconix\",DIGI:\"Digital\",\"DL&C\":\"Digital Light & Color\",DPLG:\"Doppelganger\",DS:\"Dainippon Screen\",DSOL:\"DOOSOL\",DUPN:\"DuPont\",EPSO:\"Epson\",ESKO:\"Esko-Graphics\",ETRI:\"Electronics and Telecommunications Research Institute\",EVER:\"Everex Systems\",EXAC:\"ExactCODE\",Eizo:\"Eizo\",FALC:\"Falco Data Products\",FF:\"Fuji Photo Film\",FFEI:\"FujiFilm Electronic Imaging\",FNRD:\"Fnord Software\",FORA:\"Fora\",FORE:\"Forefront Technology\",FP:\"Fujitsu\",FPA:\"WayTech Development\",FUJI:\"Fujitsu\",FX:\"Fuji Xerox\",GCC:\"GCC Technologies\",GGSL:\"Global Graphics Software\",GMB:\"Gretagmacbeth\",GMG:\"GMG\",GOLD:\"GoldStar Technology\",GOOG:\"Google\",GPRT:\"Giantprint\",GTMB:\"Gretagmacbeth\",GVC:\"WayTech Development\",GW2K:\"Sony\",HCI:\"HCI\",HDM:\"Heidelberger Druckmaschinen\",HERM:\"Hermes\",HITA:\"Hitachi America\",HP:\"Hewlett-Packard\",HTC:\"Hitachi\",HiTi:\"HiTi Digital\",IBM:\"IBM\",IDNT:\"Scitex\",IEC:\"Hewlett-Packard\",IIYA:\"Iiyama North America\",IKEG:\"Ikegami Electronics\",IMAG:\"Image Systems\",IMI:\"Ingram Micro\",INTC:\"Intel\",INTL:\"N/A (INTL)\",INTR:\"Intra Electronics\",IOCO:\"Iocomm International Technology\",IPS:\"InfoPrint Solutions Company\",IRIS:\"Scitex\",ISL:\"Ichikawa Soft Laboratory\",ITNL:\"N/A (ITNL)\",IVM:\"IVM\",IWAT:\"Iwatsu Electric\",Idnt:\"Scitex\",Inca:\"Inca Digital Printers\",Iris:\"Scitex\",JPEG:\"Joint Photographic Experts Group\",JSFT:\"Jetsoft Development\",JVC:\"JVC Information Products\",KART:\"Scitex\",KFC:\"KFC Computek Components\",KLH:\"KLH Computers\",KMHD:\"Konica Minolta\",KNCA:\"Konica\",KODA:\"Kodak\",KYOC:\"Kyocera\",Kart:\"Scitex\",LCAG:\"Leica\",LCCD:\"Leeds Colour\",LDAK:\"Left Dakota\",LEAD:\"Leading Technology\",LEXM:\"Lexmark International\",LINK:\"Link Computer\",LINO:\"Linotronic\",LITE:\"Lite-On\",Leaf:\"Leaf\",Lino:\"Linotronic\",MAGC:\"Mag Computronic\",MAGI:\"MAG Innovision\",MANN:\"Mannesmann\",MICN:\"Micron Technology\",MICR:\"Microtek\",MICV:\"Microvitec\",MINO:\"Minolta\",MITS:\"Mitsubishi Electronics America\",MITs:\"Mitsuba\",MNLT:\"Minolta\",MODG:\"Modgraph\",MONI:\"Monitronix\",MONS:\"Monaco Systems\",MORS:\"Morse Technology\",MOTI:\"Motive Systems\",MSFT:\"Microsoft\",MUTO:\"MUTOH INDUSTRIES\",Mits:\"Mitsubishi Electric\",NANA:\"NANAO\",NEC:\"NEC\",NEXP:\"NexPress Solutions\",NISS:\"Nissei Sangyo America\",NKON:\"Nikon\",NONE:\"none\",OCE:\"Oce Technologies\",OCEC:\"OceColor\",OKI:\"Oki\",OKID:\"Okidata\",OKIP:\"Okidata\",OLIV:\"Olivetti\",OLYM:\"Olympus\",ONYX:\"Onyx Graphics\",OPTI:\"Optiquest\",PACK:\"Packard Bell\",PANA:\"Matsushita Electric Industrial\",PANT:\"Pantone\",PBN:\"Packard Bell\",PFU:\"PFU\",PHIL:\"Philips Consumer Electronics\",PNTX:\"HOYA\",POne:\"Phase One A/S\",PREM:\"Premier Computer Innovations\",PRIN:\"Princeton Graphic Systems\",PRIP:\"Princeton Publishing Labs\",QLUX:\"Hong Kong\",QMS:\"QMS\",QPCD:\"QPcard AB\",QUAD:\"QuadLaser\",QUME:\"Qume\",RADI:\"Radius\",RDDx:\"Integrated Color Solutions\",RDG:\"Roland DG\",REDM:\"REDMS Group\",RELI:\"Relisys\",RGMS:\"Rolf Gierling Multitools\",RICO:\"Ricoh\",RNLD:\"Edmund Ronald\",ROYA:\"Royal\",RPC:\"Ricoh Printing Systems\",RTL:\"Royal Information Electronics\",SAMP:\"Sampo\",SAMS:\"Samsung\",SANT:\"Jaime Santana Pomares\",SCIT:\"Scitex\",SCRN:\"Dainippon Screen\",SDP:\"Scitex\",SEC:\"Samsung\",SEIK:\"Seiko Instruments\",SEIk:\"Seikosha\",SGUY:\"ScanGuy.com\",SHAR:\"Sharp Laboratories\",SICC:\"International Color Consortium\",SONY:\"Sony\",SPCL:\"SpectraCal\",STAR:\"Star\",STC:\"Sampo Technology\",Scit:\"Scitex\",Sdp:\"Scitex\",Sony:\"Sony\",TALO:\"Talon Technology\",TAND:\"Tandy\",TATU:\"Tatung\",TAXA:\"TAXAN America\",TDS:\"Tokyo Denshi Sekei\",TECO:\"TECO Information Systems\",TEGR:\"Tegra\",TEKT:\"Tektronix\",TI:\"Texas Instruments\",TMKR:\"TypeMaker\",TOSB:\"Toshiba\",TOSH:\"Toshiba\",TOTK:\"TOTOKU ELECTRIC\",TRIU:\"Triumph\",TSBT:\"Toshiba\",TTX:\"TTX Computer Products\",TVM:\"TVM Professional Monitor\",TW:\"TW Casper\",ULSX:\"Ulead Systems\",UNIS:\"Unisys\",UTZF:\"Utz Fehlau & Sohn\",VARI:\"Varityper\",VIEW:\"Viewsonic\",VISL:\"Visual communication\",VIVO:\"Vivo Mobile Communication\",WANG:\"Wang\",WLBR:\"Wilbur Imaging\",WTG2:\"Ware To Go\",WYSE:\"WYSE Technology\",XERX:\"Xerox\",XRIT:\"X-Rite\",ZRAN:\"Zoran\",Zebr:\"Zebra Technologies\",appl:\"Apple Computer\",bICC:\"basICColor\",berg:\"bergdesign\",ceyd:\"Integrated Color Solutions\",clsp:\"MacDermid ColorSpan\",ds:\"Dainippon Screen\",dupn:\"DuPont\",ffei:\"FujiFilm Electronic Imaging\",flux:\"FluxData\",iris:\"Scitex\",kart:\"Scitex\",lcms:\"Little CMS\",lino:\"Linotronic\",none:\"none\",ob4d:\"Erdt Systems\",obic:\"Medigraph\",quby:\"Qubyx Sarl\",scit:\"Scitex\",scrn:\"Dainippon Screen\",sdp:\"Scitex\",siwi:\"SIWI GRAFIKA\",yxym:\"YxyMaster\"},Ct={scnr:\"Scanner\",mntr:\"Monitor\",prtr:\"Printer\",link:\"Device Link\",abst:\"Abstract\",spac:\"Color Space Conversion Profile\",nmcl:\"Named Color\",cenc:\"ColorEncodingSpace profile\",mid:\"MultiplexIdentification profile\",mlnk:\"MultiplexLink profile\",mvis:\"MultiplexVisualization profile\",nkpf:\"Nikon Input Device Profile (NON-STANDARD!)\"};U(B,\"icc\",[[4,St],[12,Ct],[40,Object.assign({},St,Ct)],[48,St],[80,St],[64,{0:\"Perceptual\",1:\"Relative Colorimetric\",2:\"Saturation\",3:\"Absolute Colorimetric\"}],[\"tech\",{amd:\"Active Matrix Display\",crt:\"Cathode Ray Tube Display\",kpcd:\"Photo CD\",pmd:\"Passive Matrix Display\",dcam:\"Digital Camera\",dcpj:\"Digital Cinema Projector\",dmpc:\"Digital Motion Picture Camera\",dsub:\"Dye Sublimation Printer\",epho:\"Electrophotographic Printer\",esta:\"Electrostatic Printer\",flex:\"Flexography\",fprn:\"Film Writer\",fscn:\"Film Scanner\",grav:\"Gravure\",ijet:\"Ink Jet Printer\",imgs:\"Photo Image Setter\",mpfr:\"Motion Picture Film Recorder\",mpfs:\"Motion Picture Film Scanner\",offs:\"Offset Lithography\",pjtv:\"Projection Television\",rpho:\"Photographic Paper Printer\",rscn:\"Reflective Scanner\",silk:\"Silkscreen\",twax:\"Thermal Wax Printer\",vidc:\"Video Camera\",vidm:\"Video Monitor\"}]]);class yt extends re{static canHandle(e,t,i){return 237===e.getUint8(t+1)&&\"Photoshop\"===e.getString(t+4,9)&&void 0!==this.containsIptc8bim(e,t,i)}static headerLength(e,t,i){let n,s=this.containsIptc8bim(e,t,i);if(void 0!==s)return n=e.getUint8(t+s+7),n%2!=0&&(n+=1),0===n&&(n=4),s+8+n}static containsIptc8bim(e,t,i){for(let n=0;n<i;n++)if(this.isIptcSegmentHead(e,t+n))return n}static isIptcSegmentHead(e,t){return 56===e.getUint8(t)&&943868237===e.getUint32(t)&&1028===e.getUint16(t+4)}parse(){let{raw:e}=this,t=this.chunk.byteLength-1,i=!1;for(let n=0;n<t;n++)if(28===this.chunk.getUint8(n)&&2===this.chunk.getUint8(n+1)){i=!0;let t=this.chunk.getUint16(n+3),s=this.chunk.getUint8(n+2),r=this.chunk.getLatin1String(n+5,t);e.set(s,this.pluralizeValue(e.get(s),r)),n+=4+t}else if(i)break;return this.translate(),this.output}pluralizeValue(e,t){return void 0!==e?e instanceof Array?(e.push(t),e):[e,t]:t}}c(yt,\"type\",\"iptc\"),c(yt,\"translateValues\",!1),c(yt,\"reviveValues\",!1),T.set(\"iptc\",yt),U(E,\"iptc\",[[0,\"ApplicationRecordVersion\"],[3,\"ObjectTypeReference\"],[4,\"ObjectAttributeReference\"],[5,\"ObjectName\"],[7,\"EditStatus\"],[8,\"EditorialUpdate\"],[10,\"Urgency\"],[12,\"SubjectReference\"],[15,\"Category\"],[20,\"SupplementalCategories\"],[22,\"FixtureIdentifier\"],[25,\"Keywords\"],[26,\"ContentLocationCode\"],[27,\"ContentLocationName\"],[30,\"ReleaseDate\"],[35,\"ReleaseTime\"],[37,\"ExpirationDate\"],[38,\"ExpirationTime\"],[40,\"SpecialInstructions\"],[42,\"ActionAdvised\"],[45,\"ReferenceService\"],[47,\"ReferenceDate\"],[50,\"ReferenceNumber\"],[55,\"DateCreated\"],[60,\"TimeCreated\"],[62,\"DigitalCreationDate\"],[63,\"DigitalCreationTime\"],[65,\"OriginatingProgram\"],[70,\"ProgramVersion\"],[75,\"ObjectCycle\"],[80,\"Byline\"],[85,\"BylineTitle\"],[90,\"City\"],[92,\"Sublocation\"],[95,\"State\"],[100,\"CountryCode\"],[101,\"Country\"],[103,\"OriginalTransmissionReference\"],[105,\"Headline\"],[110,\"Credit\"],[115,\"Source\"],[116,\"CopyrightNotice\"],[118,\"Contact\"],[120,\"Caption\"],[121,\"LocalCaption\"],[122,\"Writer\"],[125,\"RasterizedCaption\"],[130,\"ImageType\"],[131,\"ImageOrientation\"],[135,\"LanguageIdentifier\"],[150,\"AudioType\"],[151,\"AudioSamplingRate\"],[152,\"AudioSamplingResolution\"],[153,\"AudioDuration\"],[154,\"AudioOutcue\"],[184,\"JobID\"],[185,\"MasterDocumentID\"],[186,\"ShortDocumentID\"],[187,\"UniqueDocumentID\"],[188,\"OwnerID\"],[200,\"ObjectPreviewFileFormat\"],[201,\"ObjectPreviewFileVersion\"],[202,\"ObjectPreviewData\"],[221,\"Prefs\"],[225,\"ClassifyState\"],[228,\"SimilarityIndex\"],[230,\"DocumentNotes\"],[231,\"DocumentHistory\"],[232,\"ExifCameraInfo\"],[255,\"CatalogSets\"]]),U(B,\"iptc\",[[10,{0:\"0 (reserved)\",1:\"1 (most urgent)\",2:\"2\",3:\"3\",4:\"4\",5:\"5 (normal urgency)\",6:\"6\",7:\"7\",8:\"8 (least urgent)\",9:\"9 (user-defined priority)\"}],[75,{a:\"Morning\",b:\"Both Morning and Evening\",p:\"Evening\"}],[131,{L:\"Landscape\",P:\"Portrait\",S:\"Square\"}]]);/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (tt);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/exifr/dist/full.esm.mjs\n"));

/***/ })

}]);