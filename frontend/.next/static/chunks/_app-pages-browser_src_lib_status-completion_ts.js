"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_src_lib_status-completion_ts"],{

/***/ "(app-pages-browser)/./src/lib/status-completion.ts":
/*!**************************************!*\
  !*** ./src/lib/status-completion.ts ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkAndCompleteProject: () => (/* binding */ checkAndCompleteProject),\n/* harmony export */   checkAndCompleteShoot: () => (/* binding */ checkAndCompleteShoot),\n/* harmony export */   handleTaskStatusChange: () => (/* binding */ handleTaskStatusChange)\n/* harmony export */ });\n/* harmony import */ var _auth__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./auth */ \"(app-pages-browser)/./src/lib/auth.ts\");\n\nconst supabase = (0,_auth__WEBPACK_IMPORTED_MODULE_0__.createClientSupabaseClient)();\n/**\n * Check if all shoot-based tasks for a specific shoot are completed\n * and automatically complete the shoot if they are\n */ async function checkAndCompleteShoot(shootId) {\n    try {\n        console.log('Checking shoot completion for shoot:', shootId);\n        // Get the shoot details\n        const { data: shoot, error: shootError } = await supabase.from('shoots').select('id, status, project_id').eq('id', shootId).single();\n        if (shootError || !shoot) {\n            console.error('Error fetching shoot:', shootError);\n            return;\n        }\n        // Skip if shoot is already completed\n        if (shoot.status === 'completed') {\n            console.log('Shoot is already completed');\n            return;\n        }\n        // Get all shoot-based tasks for this shoot\n        const { data: shootTasks, error: tasksError } = await supabase.from('tasks').select('id, title, status').eq('shoot_id', shootId).neq('status', 'cancelled') // Exclude cancelled tasks\n        ;\n        if (tasksError) {\n            console.error('Error fetching shoot tasks:', tasksError);\n            return;\n        }\n        if (!shootTasks || shootTasks.length === 0) {\n            console.log('No shoot-based tasks found for shoot:', shootId);\n            return;\n        }\n        // Check if all shoot-based tasks are completed\n        const allCompleted = shootTasks.every((task)=>task.status === 'completed');\n        console.log('Shoot tasks status:', shootTasks.map((t)=>({\n                title: t.title,\n                status: t.status\n            })));\n        console.log('All shoot tasks completed:', allCompleted);\n        if (allCompleted) {\n            // Automatically complete the shoot\n            const { error: updateError } = await supabase.from('shoots').update({\n                status: 'completed',\n                actual_date: new Date().toISOString()\n            }).eq('id', shootId);\n            if (updateError) {\n                console.error('Error updating shoot status:', updateError);\n                return;\n            }\n            console.log('Shoot automatically completed:', shootId);\n            // Now check if the project should be completed\n            await checkAndCompleteProject(shoot.project_id);\n        }\n    } catch (error) {\n        console.error('Error in checkAndCompleteShoot:', error);\n    }\n}\n/**\n * Check if all shoots and project-level tasks for a project are completed\n * and automatically complete the project if they are\n */ async function checkAndCompleteProject(projectId) {\n    try {\n        console.log('Checking project completion for project:', projectId);\n        // Get the project details\n        const { data: project, error: projectError } = await supabase.from('projects').select('id, status').eq('id', projectId).single();\n        if (projectError || !project) {\n            console.error('Error fetching project:', projectError);\n            return;\n        }\n        // Skip if project is already completed\n        if (project.status === 'completed') {\n            console.log('Project is already completed');\n            return;\n        }\n        // Get all shoots for this project\n        const { data: shoots, error: shootsError } = await supabase.from('shoots').select('id, status').eq('project_id', projectId).neq('status', 'cancelled') // Exclude cancelled shoots\n        ;\n        if (shootsError) {\n            console.error('Error fetching project shoots:', shootsError);\n            return;\n        }\n        var _shoots_every;\n        // Check if all shoots are completed\n        const allShootsCompleted = (_shoots_every = shoots === null || shoots === void 0 ? void 0 : shoots.every((shoot)=>shoot.status === 'completed')) !== null && _shoots_every !== void 0 ? _shoots_every : true;\n        console.log('Project shoots status:', shoots === null || shoots === void 0 ? void 0 : shoots.map((s)=>({\n                id: s.id,\n                status: s.status\n            })));\n        console.log('All shoots completed:', allShootsCompleted);\n        if (!allShootsCompleted) {\n            console.log('Not all shoots are completed yet');\n            return;\n        }\n        // Get all project-level tasks (tasks without shoot_id)\n        const { data: projectTasks, error: projectTasksError } = await supabase.from('tasks').select('id, title, status').eq('project_id', projectId).is('shoot_id', null) // Project-level tasks have no shoot_id\n        .neq('status', 'cancelled') // Exclude cancelled tasks\n        ;\n        if (projectTasksError) {\n            console.error('Error fetching project tasks:', projectTasksError);\n            return;\n        }\n        var _projectTasks_every;\n        // Check if all project-level tasks are completed\n        const allProjectTasksCompleted = (_projectTasks_every = projectTasks === null || projectTasks === void 0 ? void 0 : projectTasks.every((task)=>task.status === 'completed')) !== null && _projectTasks_every !== void 0 ? _projectTasks_every : true;\n        console.log('Project tasks status:', projectTasks === null || projectTasks === void 0 ? void 0 : projectTasks.map((t)=>({\n                title: t.title,\n                status: t.status\n            })));\n        console.log('All project tasks completed:', allProjectTasksCompleted);\n        if (allShootsCompleted && allProjectTasksCompleted) {\n            // Automatically complete the project\n            const { error: updateError } = await supabase.from('projects').update({\n                status: 'completed'\n            }).eq('id', projectId);\n            if (updateError) {\n                console.error('Error updating project status:', updateError);\n                return;\n            }\n            console.log('Project automatically completed:', projectId);\n        }\n    } catch (error) {\n        console.error('Error in checkAndCompleteProject:', error);\n    }\n}\n/**\n * Handle task status change and trigger automatic completion checks\n */ async function handleTaskStatusChange(task, newStatus) {\n    try {\n        console.log('Handling task status change:', {\n            taskId: task.id,\n            title: task.title,\n            newStatus\n        });\n        // If task is completed, check for automatic completion\n        if (newStatus === 'completed') {\n            // If it's a shoot-based task, check shoot completion\n            if (task.shoot_id) {\n                await checkAndCompleteShoot(task.shoot_id);\n            } else if (task.project_id) {\n                await checkAndCompleteProject(task.project_id);\n            }\n        }\n    } catch (error) {\n        console.error('Error in handleTaskStatusChange:', error);\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/status-completion.ts\n"));

/***/ })

}]);