"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_src_lib_heatmap-automation_ts";
exports.ids = ["_ssr_src_lib_heatmap-automation_ts"];
exports.modules = {

/***/ "(ssr)/./src/lib/heatmap-automation.ts":
/*!***************************************!*\
  !*** ./src/lib/heatmap-automation.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   processHeatmapAutomation: () => (/* binding */ processHeatmapAutomation),\n/* harmony export */   queueHeatmapAutomation: () => (/* binding */ queueHeatmapAutomation)\n/* harmony export */ });\n/* harmony import */ var _lib_microsoft_graph__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/microsoft-graph */ \"(ssr)/./src/lib/microsoft-graph.ts\");\n\n// Constants for SharePoint configuration\nconst SHAREPOINT_SITE_URL = 'https://zn6bn.sharepoint.com/sites/files';\nconst DRIVE_ID = 'b!6BliBHB7rkqGeijn571F56igGQK5UQ9GkI_AIFbTSvvooGrFp94hQr6gPCGK1yYV';\n// File type mappings\nconst FILE_TYPE_MAPPINGS = {\n    photos: [\n        '.jpg',\n        '.jpeg',\n        '.png'\n    ],\n    videos: [\n        '.mp4',\n        '.mov'\n    ],\n    srt: [\n        '.srt'\n    ],\n    lrf: [\n        '.lrf'\n    ]\n};\n/**\n * Get the Site ID from SharePoint site URL\n */ async function getSiteIdInternal(accessToken) {\n    try {\n        const url = new URL(SHAREPOINT_SITE_URL);\n        const hostname = url.hostname;\n        const sitePath = url.pathname;\n        const siteIdentifier = `${hostname}:${sitePath}:`;\n        const response = await fetch(`https://graph.microsoft.com/v1.0/sites/${siteIdentifier}`, {\n            headers: {\n                Authorization: `Bearer ${accessToken}`\n            }\n        });\n        if (!response.ok) {\n            throw new Error(`HTTP error! status: ${response.status}`);\n        }\n        const data = await response.json();\n        return data.id;\n    } catch (error) {\n        console.error('Error getting Site ID:', error);\n        throw new Error('Failed to get SharePoint Site ID');\n    }\n}\n/**\n * List all files in a SharePoint folder\n */ async function listFilesInFolder(accessToken, folderId) {\n    try {\n        const siteId = await getSiteIdInternal(accessToken);\n        const response = await fetch(`https://graph.microsoft.com/v1.0/sites/${siteId}/drives/${DRIVE_ID}/items/${folderId}/children`, {\n            headers: {\n                Authorization: `Bearer ${accessToken}`\n            }\n        });\n        if (!response.ok) {\n            throw new Error(`HTTP error! status: ${response.status}`);\n        }\n        const data = await response.json();\n        return data.value || [];\n    } catch (error) {\n        console.error('Error listing files in folder:', error);\n        throw error;\n    }\n}\n/**\n * Create a folder in SharePoint\n */ async function createFolder(accessToken, parentFolderId, folderName) {\n    try {\n        const siteId = await getSiteIdInternal(accessToken);\n        const response = await fetch(`https://graph.microsoft.com/v1.0/sites/${siteId}/drives/${DRIVE_ID}/items/${parentFolderId}/children`, {\n            method: 'POST',\n            headers: {\n                Authorization: `Bearer ${accessToken}`,\n                'Content-Type': 'application/json'\n            },\n            body: JSON.stringify({\n                name: folderName,\n                folder: {}\n            })\n        });\n        if (!response.ok) {\n            const errorText = await response.text();\n            console.error('Create folder error:', response.status, errorText);\n            throw new Error(`HTTP error! status: ${response.status}`);\n        }\n        const data = await response.json();\n        return data.id;\n    } catch (error) {\n        console.error(`Error creating folder \"${folderName}\":`, error);\n        throw error;\n    }\n}\n/**\n * Move a file to a different folder\n */ async function moveFile(accessToken, fileId, targetFolderId, newName) {\n    try {\n        const siteId = await getSiteIdInternal(accessToken);\n        const updateData = {\n            parentReference: {\n                id: targetFolderId\n            }\n        };\n        if (newName) {\n            updateData.name = newName;\n        }\n        const response = await fetch(`https://graph.microsoft.com/v1.0/sites/${siteId}/drives/${DRIVE_ID}/items/${fileId}`, {\n            method: 'PATCH',\n            headers: {\n                Authorization: `Bearer ${accessToken}`,\n                'Content-Type': 'application/json'\n            },\n            body: JSON.stringify(updateData)\n        });\n        if (!response.ok) {\n            const errorText = await response.text();\n            console.error('Move file error:', response.status, errorText);\n            throw new Error(`HTTP error! status: ${response.status}`);\n        }\n    } catch (error) {\n        console.error('Error moving file:', error);\n        throw error;\n    }\n}\n/**\n * Get file type based on extension\n */ function getFileType(filename) {\n    const extension = '.' + filename.split('.').pop()?.toLowerCase();\n    for (const [type, extensions] of Object.entries(FILE_TYPE_MAPPINGS)){\n        if (extensions.includes(extension)) {\n            return type;\n        }\n    }\n    return null;\n}\n/**\n * Generate a unique filename if duplicate exists\n */ function generateUniqueFilename(originalName, existingFiles) {\n    if (!existingFiles.includes(originalName)) {\n        return originalName;\n    }\n    const nameParts = originalName.split('.');\n    const extension = nameParts.pop();\n    const baseName = nameParts.join('.');\n    let counter = 1;\n    let newName;\n    do {\n        newName = `${baseName}_(${counter}).${extension}`;\n        counter++;\n    }while (existingFiles.includes(newName));\n    return newName;\n}\n/**\n * Create or get folder structure for file organization\n */ async function ensureFolderStructure(accessToken, scheduleFolderId) {\n    try {\n        // List existing folders\n        const existingItems = await listFilesInFolder(accessToken, scheduleFolderId);\n        const existingFolders = existingItems.filter((item)=>item.folder).map((item)=>item.name);\n        const folderStructure = {\n            photos: '',\n            videos: '',\n            srt: '',\n            lrf: ''\n        };\n        // Create Raw folder if it doesn't exist\n        let rawFolderId = existingItems.find((item)=>item.folder && item.name === 'Raw')?.id;\n        if (!rawFolderId) {\n            rawFolderId = await createFolder(accessToken, scheduleFolderId, 'Raw');\n        }\n        // Create subfolders under Raw\n        const rawItems = await listFilesInFolder(accessToken, rawFolderId);\n        const rawFolders = rawItems.filter((item)=>item.folder).map((item)=>item.name);\n        const subfolders = [\n            'Photos',\n            'Videos',\n            'SRT',\n            'LRF'\n        ];\n        for (const subfolder of subfolders){\n            let subfolderId = rawItems.find((item)=>item.folder && item.name === subfolder)?.id;\n            if (!subfolderId) {\n                subfolderId = await createFolder(accessToken, rawFolderId, subfolder);\n            }\n            const key = subfolder.toLowerCase();\n            folderStructure[key] = subfolderId;\n        }\n        return folderStructure;\n    } catch (error) {\n        console.error('Error ensuring folder structure:', error);\n        throw error;\n    }\n}\n/**\n * Download file content as buffer for metadata extraction\n */ async function downloadFileContent(accessToken, fileId) {\n    try {\n        const siteId = await getSiteIdInternal(accessToken);\n        const response = await fetch(`https://graph.microsoft.com/v1.0/sites/${siteId}/drives/${DRIVE_ID}/items/${fileId}/content`, {\n            headers: {\n                Authorization: `Bearer ${accessToken}`\n            }\n        });\n        if (!response.ok) {\n            throw new Error(`HTTP error! status: ${response.status}`);\n        }\n        const arrayBuffer = await response.arrayBuffer();\n        return Buffer.from(arrayBuffer);\n    } catch (error) {\n        console.error('Error downloading file content:', error);\n        throw error;\n    }\n}\n/**\n * Extract EXIF GPS data from image buffer\n */ async function extractImageGPS(buffer) {\n    try {\n        // Use exifr library for EXIF extraction\n        const exifr = await __webpack_require__.e(/*! import() */ \"vendor-chunks/exifr\").then(__webpack_require__.bind(__webpack_require__, /*! exifr */ \"(ssr)/./node_modules/exifr/dist/full.esm.mjs\"));\n        const gpsData = await exifr.gps(buffer);\n        if (gpsData && gpsData.latitude && gpsData.longitude) {\n            return {\n                lat: gpsData.latitude,\n                lng: gpsData.longitude,\n                timestamp: gpsData.DateTimeOriginal?.toISOString()\n            };\n        }\n        return null;\n    } catch (error) {\n        console.error('Error extracting GPS from image:', error);\n        return null;\n    }\n}\n/**\n * Parse SRT file for GPS coordinates\n */ async function parseSRTFile(buffer) {\n    try {\n        const srtContent = buffer.toString('utf-8');\n        const parser = await __webpack_require__.e(/*! import() */ \"vendor-chunks/srt-parser-2\").then(__webpack_require__.bind(__webpack_require__, /*! srt-parser-2 */ \"(ssr)/./node_modules/srt-parser-2/dist/index.js\"));\n        const srtParser = new parser.default();\n        const subtitles = srtParser.fromSrt(srtContent);\n        const gpsCoordinates = [];\n        // Extract GPS coordinates from SRT content\n        for (const subtitle of subtitles){\n            const text = subtitle.text;\n            // Multiple regex patterns for different GPS coordinate formats in drone SRT files\n            const gpsPatterns = [\n                // Standard GPS format: GPS(lat,lng) or GPS: lat, lng\n                /GPS[\\s:]*\\(?(-?\\d+\\.?\\d*)[,\\s]+(-?\\d+\\.?\\d*)\\)?/i,\n                // Latitude/Longitude format: LAT: xx.xxx LON: yy.yyy\n                /LAT(?:ITUDE)?[\\s:]*(-?\\d+\\.?\\d*)[,\\s]*LON(?:GITUDE)?[\\s:]*(-?\\d+\\.?\\d*)/i,\n                // DJI format: [GPS] lat,lng\n                /\\[GPS\\][\\s:]*(-?\\d+\\.?\\d*)[,\\s]+(-?\\d+\\.?\\d*)/i,\n                // Coordinate format: lat,lng (simple comma-separated)\n                /^(-?\\d{1,3}\\.\\d+)[,\\s]+(-?\\d{1,3}\\.\\d+)$/,\n                // Home format: HOME(lat,lng)\n                /HOME[\\s:]*\\(?(-?\\d+\\.?\\d*)[,\\s]+(-?\\d+\\.?\\d*)\\)?/i,\n                // Position format: POS: lat lng\n                /POS(?:ITION)?[\\s:]*(-?\\d+\\.?\\d*)[,\\s]+(-?\\d+\\.?\\d*)/i\n            ];\n            for (const pattern of gpsPatterns){\n                const match = text.match(pattern);\n                if (match) {\n                    const lat = parseFloat(match[1]);\n                    const lng = parseFloat(match[2]);\n                    if (!isNaN(lat) && !isNaN(lng) && lat >= -90 && lat <= 90 && lng >= -180 && lng <= 180) {\n                        gpsCoordinates.push({\n                            lat,\n                            lng\n                        });\n                        break; // Found valid coordinates, move to next subtitle\n                    }\n                }\n            }\n        }\n        if (gpsCoordinates.length > 0) {\n            return {\n                start: gpsCoordinates[0],\n                end: gpsCoordinates[gpsCoordinates.length - 1]\n            };\n        }\n        return null;\n    } catch (error) {\n        console.error('Error parsing SRT file:', error);\n        return null;\n    }\n}\n/**\n * Create a public share link for a file\n */ async function createPublicShareLink(accessToken, fileId) {\n    try {\n        const siteId = await getSiteIdInternal(accessToken);\n        const response = await fetch(`https://graph.microsoft.com/v1.0/sites/${siteId}/drives/${DRIVE_ID}/items/${fileId}/createLink`, {\n            method: 'POST',\n            headers: {\n                Authorization: `Bearer ${accessToken}`,\n                'Content-Type': 'application/json'\n            },\n            body: JSON.stringify({\n                type: 'view',\n                scope: 'anonymous'\n            })\n        });\n        if (!response.ok) {\n            throw new Error(`HTTP error! status: ${response.status}`);\n        }\n        const data = await response.json();\n        return data.link.webUrl;\n    } catch (error) {\n        console.error('Error creating public share link:', error);\n        throw error;\n    }\n}\n/**\n * Upload geo metadata JSON file to SharePoint\n */ async function uploadGeoMetadataFile(accessToken, scheduleFolderId, metadata) {\n    try {\n        const siteId = await getSiteIdInternal(accessToken);\n        const jsonContent = JSON.stringify(metadata, null, 2);\n        const response = await fetch(`https://graph.microsoft.com/v1.0/sites/${siteId}/drives/${DRIVE_ID}/items/${scheduleFolderId}:/geo_metadata.json:/content`, {\n            method: 'PUT',\n            headers: {\n                Authorization: `Bearer ${accessToken}`,\n                'Content-Type': 'application/json'\n            },\n            body: jsonContent\n        });\n        if (!response.ok) {\n            throw new Error(`HTTP error! status: ${response.status}`);\n        }\n        const data = await response.json();\n        return data.id;\n    } catch (error) {\n        console.error('Error uploading geo metadata file:', error);\n        throw error;\n    }\n}\n/**\n * Main function to process heatmap automation for a schedule\n */ async function processHeatmapAutomation(scheduleId) {\n    try {\n        console.log('🗺️ Starting heatmap automation for schedule:', scheduleId);\n        // Get access token\n        const accessToken = await (0,_lib_microsoft_graph__WEBPACK_IMPORTED_MODULE_0__.getMicrosoftGraphAccessToken)();\n        // Get schedule folder information from database\n        const { createClient } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @supabase/supabase-js */ \"(ssr)/./node_modules/@supabase/supabase-js/dist/module/index.js\"));\n        const supabaseUrl = \"https://qxgfulribhhohmggyroq.supabase.co\";\n        const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;\n        const supabase = createClient(supabaseUrl, supabaseServiceKey);\n        const { data: schedule, error } = await supabase.from('schedules').select(`\n        id,\n        custom_id,\n        sharepoint_folder_id,\n        projects!inner (\n          id,\n          custom_id,\n          name,\n          clients!inner (\n            id,\n            custom_id,\n            name\n          )\n        )\n      `).eq('id', scheduleId).single();\n        if (error || !schedule || !schedule.sharepoint_folder_id) {\n            throw new Error('Schedule not found or SharePoint folder not created');\n        }\n        const scheduleFolderId = schedule.sharepoint_folder_id;\n        // 1. List all files in the schedule folder\n        console.log('📂 Listing files in schedule folder...');\n        const allFiles = await listFilesInFolder(accessToken, scheduleFolderId);\n        const files = allFiles.filter((item)=>!item.folder) // Only files, not folders\n        ;\n        if (files.length === 0) {\n            return {\n                success: true,\n                message: 'No files found to process'\n            };\n        }\n        // 2. Create folder structure\n        console.log('📁 Creating folder structure...');\n        const folderStructure = await ensureFolderStructure(accessToken, scheduleFolderId);\n        // 3. Organize files by type\n        console.log('🗂️ Organizing files by type...');\n        const organizedFiles = {\n            photos: [],\n            videos: [],\n            srt: [],\n            lrf: [],\n            other: []\n        };\n        for (const file of files){\n            const fileType = getFileType(file.name);\n            if (fileType) {\n                organizedFiles[fileType].push(file);\n            } else {\n                organizedFiles.other.push(file);\n            }\n        }\n        // Move files to appropriate folders\n        for (const [type, typeFiles] of Object.entries(organizedFiles)){\n            if (type === 'other' || typeFiles.length === 0) continue;\n            const targetFolderId = folderStructure[type];\n            if (!targetFolderId) continue;\n            // Get existing files in target folder to handle duplicates\n            const existingFiles = await listFilesInFolder(accessToken, targetFolderId);\n            const existingFilenames = existingFiles.map((f)=>f.name);\n            for (const file of typeFiles){\n                const uniqueName = generateUniqueFilename(file.name, existingFilenames);\n                await moveFile(accessToken, file.id, targetFolderId, uniqueName !== file.name ? uniqueName : undefined);\n                existingFilenames.push(uniqueName);\n                console.log(`📦 Moved ${file.name} to ${type} folder`);\n            }\n        }\n        // 4. Extract metadata from organized files (with parallel processing)\n        console.log('🌍 Extracting geolocation metadata...');\n        const geoMetadata = [];\n        // Process photos in parallel (batches of 10 for performance)\n        const photoBatches = [];\n        for(let i = 0; i < organizedFiles.photos.length; i += 10){\n            photoBatches.push(organizedFiles.photos.slice(i, i + 10));\n        }\n        for (const batch of photoBatches){\n            const photoPromises = batch.map(async (photoFile)=>{\n                try {\n                    const buffer = await downloadFileContent(accessToken, photoFile.id);\n                    const gpsData = await extractImageGPS(buffer);\n                    if (gpsData) {\n                        console.log(`📸 Extracted GPS from photo: ${photoFile.name}`);\n                        return {\n                            type: 'photo',\n                            filename: photoFile.name,\n                            lat: gpsData.lat,\n                            lng: gpsData.lng,\n                            timestamp: gpsData.timestamp\n                        };\n                    }\n                } catch (error) {\n                    console.warn(`⚠️ Failed to extract GPS from photo ${photoFile.name}:`, error);\n                }\n                return null;\n            });\n            const batchResults = await Promise.all(photoPromises);\n            geoMetadata.push(...batchResults.filter((result)=>result !== null));\n        }\n        // Process videos with SRT files in parallel\n        const videoPromises = organizedFiles.videos.map(async (videoFile)=>{\n            try {\n                // Find matching SRT file\n                const videoBaseName = videoFile.name.replace(/\\.[^/.]+$/, '');\n                const matchingSRT = organizedFiles.srt.find((srtFile)=>srtFile.name.toLowerCase().includes(videoBaseName.toLowerCase()));\n                if (matchingSRT) {\n                    const buffer = await downloadFileContent(accessToken, matchingSRT.id);\n                    const gpsData = await parseSRTFile(buffer);\n                    if (gpsData) {\n                        console.log(`🎥 Extracted GPS from video: ${videoFile.name}`);\n                        return {\n                            type: 'video',\n                            filename: videoFile.name,\n                            start: gpsData.start,\n                            end: gpsData.end\n                        };\n                    }\n                }\n            } catch (error) {\n                console.warn(`⚠️ Failed to extract GPS from video ${videoFile.name}:`, error);\n            }\n            return null;\n        });\n        const videoResults = await Promise.all(videoPromises);\n        geoMetadata.push(...videoResults.filter((result)=>result !== null));\n        // 5. Generate and upload geo_metadata.json\n        console.log('📄 Generating geo_metadata.json...');\n        const metadataFileId = await uploadGeoMetadataFile(accessToken, scheduleFolderId, geoMetadata);\n        // 6. Create public share link\n        console.log('🔗 Creating public share link...');\n        const shareLink = await createPublicShareLink(accessToken, metadataFileId);\n        // 7. Store share link in database\n        await supabase.from('schedules').update({\n            geo_metadata_link: shareLink,\n            updated_at: new Date().toISOString()\n        }).eq('id', scheduleId);\n        console.log('✅ Heatmap automation completed successfully');\n        return {\n            success: true,\n            message: `Processed ${geoMetadata.length} geotagged files and created metadata file`,\n            shareLink\n        };\n    } catch (error) {\n        console.error('❌ Heatmap automation failed:', error);\n        return {\n            success: false,\n            message: error instanceof Error ? error.message : 'Unknown error occurred'\n        };\n    }\n}\n/**\n * Queue heatmap automation as a background job\n */ function queueHeatmapAutomation(scheduleId) {\n    const { queueBackgroundJob } = __webpack_require__(/*! @/lib/background-jobs */ \"(ssr)/./src/lib/background-jobs.ts\");\n    return queueBackgroundJob('heatmap_automation', 'schedule', scheduleId, {\n        scheduleId\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/heatmap-automation.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/microsoft-graph.ts":
/*!************************************!*\
  !*** ./src/lib/microsoft-graph.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkIfFolderExists: () => (/* binding */ folderExists),\n/* harmony export */   createClientFolder: () => (/* binding */ createClientFolder),\n/* harmony export */   createFolder: () => (/* binding */ createFolder),\n/* harmony export */   createProjectFolder: () => (/* binding */ createProjectFolder),\n/* harmony export */   createProjectFolderStructure: () => (/* binding */ createProjectFolderStructure),\n/* harmony export */   createScheduleFolder: () => (/* binding */ createScheduleFolder),\n/* harmony export */   createSubfolderIfNotExists: () => (/* binding */ createSubfolderIfNotExists),\n/* harmony export */   getClientsFolderId: () => (/* binding */ getClientsFolderId),\n/* harmony export */   getMicrosoftGraphAccessToken: () => (/* binding */ getAccessToken),\n/* harmony export */   getRootFolderId: () => (/* binding */ getRootFolderId),\n/* harmony export */   getScheduleFolderLinks: () => (/* binding */ getScheduleFolderLinks),\n/* harmony export */   storeClientFolderInDatabase: () => (/* binding */ storeClientFolderInDatabase),\n/* harmony export */   storeProjectFolderInDatabase: () => (/* binding */ storeProjectFolderInDatabase)\n/* harmony export */ });\n// Function to load environment variables from .env.local if not already loaded\nfunction loadEnvIfNeeded() {\n    // Only run on server side\n    if (false) {}\n    // Check if Microsoft Graph credentials are already loaded\n    if (process.env.MICROSOFT_CLIENT_ID && process.env.MICROSOFT_CLIENT_SECRET && process.env.MICROSOFT_TENANT_ID) {\n        return;\n    }\n    // Temporarily disabled to fix chunk loading issues\n    console.log('Microsoft Graph environment loading temporarily disabled');\n}\n// Constants for SharePoint configuration\nconst SHAREPOINT_SITE_URL = 'https://zn6bn.sharepoint.com/sites/files';\nconst DRIVE_ID = 'b!6BliBHB7rkqGeijn571F56igGQK5UQ9GkI_AIFbTSvvooGrFp94hQr6gPCGK1yYV';\n// Cache for site ID and access tokens to avoid repeated API calls\nlet cachedSiteId = null;\nlet cachedAccessToken = null;\nlet tokenExpiry = null;\n// Token refresh threshold (5 minutes before expiry)\nconst TOKEN_REFRESH_THRESHOLD = 5 * 60 * 1000;\n/**\n * Get the Site ID from SharePoint site URL\n * @param accessToken Microsoft Graph API access token\n * @returns Site ID\n */ async function getSiteId(accessToken) {\n    if (cachedSiteId) {\n        return cachedSiteId;\n    }\n    try {\n        // Extract hostname and site path from the SharePoint URL\n        const url = new URL(SHAREPOINT_SITE_URL);\n        const hostname = url.hostname;\n        const sitePath = url.pathname; // This will be '/sites/files'\n        // Format for Microsoft Graph API: hostname:/sites/sitename:\n        const siteIdentifier = `${hostname}:${sitePath}:`;\n        console.log('Getting Site ID for:', siteIdentifier);\n        const response = await fetch(`https://graph.microsoft.com/v1.0/sites/${siteIdentifier}`, {\n            headers: {\n                Authorization: `Bearer ${accessToken}`\n            }\n        });\n        if (!response.ok) {\n            const errorText = await response.text();\n            console.error('Site ID request failed:', response.status, errorText);\n            throw new Error(`HTTP error! status: ${response.status}`);\n        }\n        const data = await response.json();\n        console.log('Site ID retrieved successfully:', data.id);\n        cachedSiteId = data.id;\n        return data.id;\n    } catch (error) {\n        console.error('Error getting Site ID:', error);\n        throw new Error('Failed to get SharePoint Site ID');\n    }\n}\n/**\n * Get Microsoft Graph API access token using client credentials flow\n * @returns Access token for Microsoft Graph API\n */ async function getAccessToken() {\n    // Check if we have a cached token that's still valid\n    const now = Date.now();\n    if (cachedAccessToken && tokenExpiry && now < tokenExpiry - TOKEN_REFRESH_THRESHOLD) {\n        return cachedAccessToken;\n    }\n    // Ensure environment variables are loaded\n    loadEnvIfNeeded();\n    const clientId = process.env.MICROSOFT_CLIENT_ID;\n    const clientSecret = process.env.MICROSOFT_CLIENT_SECRET;\n    const tenantId = process.env.MICROSOFT_TENANT_ID;\n    if (!clientId || !clientSecret || !tenantId) {\n        throw new Error('Missing Microsoft Graph API credentials in environment variables');\n    }\n    try {\n        const response = await fetch(`https://login.microsoftonline.com/${tenantId}/oauth2/v2.0/token`, {\n            method: 'POST',\n            headers: {\n                'Content-Type': 'application/x-www-form-urlencoded'\n            },\n            body: new URLSearchParams({\n                client_id: clientId,\n                scope: 'https://graph.microsoft.com/.default',\n                client_secret: clientSecret,\n                grant_type: 'client_credentials'\n            })\n        });\n        if (!response.ok) {\n            throw new Error(`HTTP error! status: ${response.status}`);\n        }\n        const data = await response.json();\n        // Cache the token and its expiry time (typically 1 hour)\n        cachedAccessToken = data.access_token;\n        tokenExpiry = now + data.expires_in * 1000;\n        return data.access_token;\n    } catch (error) {\n        console.error('Error getting Microsoft Graph API access token:', error);\n        throw new Error('Failed to authenticate with Microsoft Graph API');\n    }\n}\n/**\n * Check if a folder exists in SharePoint\n * @param accessToken Microsoft Graph API access token\n * @param folderPath Path to check\n * @returns Folder information if exists, null otherwise\n */ async function folderExists(accessToken, folderPath) {\n    try {\n        const siteId = await getSiteId(accessToken);\n        const response = await fetch(`https://graph.microsoft.com/v1.0/sites/${siteId}/drives/${DRIVE_ID}/root:${folderPath}`, {\n            headers: {\n                Authorization: `Bearer ${accessToken}`\n            }\n        });\n        if (!response.ok) {\n            // If folder doesn't exist, Graph API returns 404\n            if (response.status === 404) {\n                return null;\n            }\n            // Re-throw other errors\n            throw new Error(`HTTP error! status: ${response.status}`);\n        }\n        const data = await response.json();\n        return {\n            id: data.id,\n            webUrl: data.webUrl\n        };\n    } catch (error) {\n        console.error(`Error checking if folder exists at \"${folderPath}\":`, error);\n        throw error;\n    }\n}\n/**\n * Create a folder in SharePoint with retry logic\n * @param accessToken Microsoft Graph API access token\n * @param parentFolderId ID of parent folder\n * @param folderName Name of folder to create\n * @returns Created folder information\n */ async function createFolder(accessToken, parentFolderId, folderName) {\n    const maxRetries = 3;\n    let lastError;\n    for(let attempt = 1; attempt <= maxRetries; attempt++){\n        try {\n            const siteId = await getSiteId(accessToken);\n            const response = await fetch(`https://graph.microsoft.com/v1.0/sites/${siteId}/drives/${DRIVE_ID}/items/${parentFolderId}/children`, {\n                method: 'POST',\n                headers: {\n                    Authorization: `Bearer ${accessToken}`,\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    name: folderName,\n                    folder: {}\n                })\n            });\n            if (!response.ok) {\n                throw new Error(`HTTP error! status: ${response.status}`);\n            }\n            const data = await response.json();\n            return {\n                id: data.id,\n                webUrl: data.webUrl\n            };\n        } catch (error) {\n            console.error(`Error creating folder \"${folderName}\" (attempt ${attempt}/${maxRetries}):`, error);\n            lastError = error;\n            // Wait before retrying (exponential backoff)\n            if (attempt < maxRetries) {\n                await new Promise((resolve)=>setTimeout(resolve, Math.pow(2, attempt) * 1000));\n            }\n        }\n    }\n    throw new Error(`Failed to create folder \"${folderName}\" after ${maxRetries} attempts: ${lastError instanceof Error ? lastError.message : 'Unknown error'}`);\n}\n/**\n * Create a share link for a folder with \"anyone with the link\" permissions\n * @param accessToken Microsoft Graph API access token\n * @param folderId ID of the folder to create share link for\n * @returns Share link URL\n */ async function createShareLink(accessToken, folderId) {\n    try {\n        const siteId = await getSiteId(accessToken);\n        const response = await fetch(`https://graph.microsoft.com/v1.0/sites/${siteId}/drives/${DRIVE_ID}/items/${folderId}/createLink`, {\n            method: 'POST',\n            headers: {\n                Authorization: `Bearer ${accessToken}`,\n                'Content-Type': 'application/json'\n            },\n            body: JSON.stringify({\n                type: 'view',\n                scope: 'anonymous'\n            })\n        });\n        if (!response.ok) {\n            throw new Error(`HTTP error! status: ${response.status}`);\n        }\n        const data = await response.json();\n        return data.link.webUrl;\n    } catch (error) {\n        console.error(`Error creating share link for folder ID \"${folderId}\":`, error);\n        throw new Error(`Failed to create share link for folder`);\n    }\n}\n/**\n * Get the ID of the root drive folder\n * @param accessToken Microsoft Graph API access token\n * @returns ID of the root folder\n */ async function getRootFolderId(accessToken) {\n    try {\n        const siteId = await getSiteId(accessToken);\n        const response = await fetch(`https://graph.microsoft.com/v1.0/sites/${siteId}/drives/${DRIVE_ID}/root`, {\n            headers: {\n                Authorization: `Bearer ${accessToken}`\n            }\n        });\n        if (!response.ok) {\n            throw new Error(`HTTP error! status: ${response.status}`);\n        }\n        const data = await response.json();\n        return data.id;\n    } catch (error) {\n        console.error('Error getting root folder ID:', error);\n        throw new Error('Failed to get root folder ID');\n    }\n}\n/**\n * Create a client folder in SharePoint root directory\n * @param clientId ID of the client to associate with the folder\n * @param clientCustomId Custom ID of the client (e.g., CYMCL-25-001)\n * @param clientName Name of the client\n * @returns Object containing folder information\n */ async function createClientFolder(clientId, clientCustomId, clientName) {\n    try {\n        // Get access token\n        const accessToken = await getAccessToken();\n        // Get the root folder ID\n        const rootFolderId = await getRootFolderId(accessToken);\n        // Create folder name: {ClientCustomID} {ClientName}\n        const folderName = `${clientCustomId} ${clientName}`;\n        // Check if folder already exists\n        const existingFolder = await folderExists(accessToken, `/${folderName}`);\n        let clientFolder;\n        if (existingFolder) {\n            console.log(`Client folder \"${folderName}\" already exists`);\n            clientFolder = existingFolder;\n        } else {\n            console.log(`Creating client folder \"${folderName}\"`);\n            clientFolder = await createFolder(accessToken, rootFolderId, folderName);\n        }\n        // Create share link for the client folder\n        let shareLink;\n        try {\n            shareLink = await createShareLink(accessToken, clientFolder.id);\n            console.log(`Created share link for client folder: ${shareLink}`);\n        } catch (error) {\n            console.warn('Failed to create share link for client folder:', error);\n        // Continue without share link\n        }\n        // Store folder information in the database\n        await storeClientFolderInDatabase(clientId, clientFolder.id, clientFolder.webUrl, shareLink);\n        return {\n            folder: {\n                ...clientFolder,\n                shareLink\n            }\n        };\n    } catch (error) {\n        console.error('Error creating client folder:', error);\n        throw new Error(`Failed to create client folder: ${error instanceof Error ? error.message : 'Unknown error'}`);\n    }\n}\n/**\n * Create a project folder in SharePoint\n * @param projectId ID of the project\n * @param projectCustomId Custom ID of the project\n * @param projectName Name of the project\n * @param clientId ID of the client (parent folder)\n * @returns Result containing folder information\n */ async function createProjectFolder(projectId, projectCustomId, projectName, clientId) {\n    try {\n        // Get access token\n        const accessToken = await getAccessToken();\n        // Get client information from database using server-side client\n        const { createClient } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @supabase/supabase-js */ \"(ssr)/./node_modules/@supabase/supabase-js/dist/module/index.js\"));\n        // Validate environment variables with better error context\n        const supabaseUrl = \"https://qxgfulribhhohmggyroq.supabase.co\";\n        const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;\n        console.log('🔍 Environment variable check in storeProjectFolderInDatabase:', {\n            supabaseUrl: supabaseUrl ? 'Present' : 'Missing',\n            supabaseServiceKey: supabaseServiceKey ? 'Present' : 'Missing',\n            nodeEnv: \"development\",\n            context: 'background-job'\n        });\n        if (!supabaseUrl) {\n            console.error('❌ NEXT_PUBLIC_SUPABASE_URL is missing in background job context');\n            throw new Error('NEXT_PUBLIC_SUPABASE_URL is required');\n        }\n        if (!supabaseServiceKey) {\n            console.error('❌ SUPABASE_SERVICE_ROLE_KEY is missing in background job context');\n            throw new Error('SUPABASE_SERVICE_ROLE_KEY is required');\n        }\n        const supabase = createClient(supabaseUrl, supabaseServiceKey);\n        const { data: client, error: clientError } = await supabase.from('clients').select('custom_id, name').eq('id', clientId).single();\n        if (clientError || !client) {\n            throw new Error(`Failed to get client information: ${clientError?.message || 'Client not found'}`);\n        }\n        // Create client folder name and project folder name\n        const clientFolderName = `${client.custom_id} ${client.name}`;\n        const projectFolderName = `${projectCustomId} ${projectName}`;\n        // Check if client folder exists, create if not\n        const clientFolderPath = `/${clientFolderName}`;\n        let clientFolder = await folderExists(accessToken, clientFolderPath);\n        if (!clientFolder) {\n            const rootFolderId = await getRootFolderId(accessToken);\n            clientFolder = await createFolder(accessToken, rootFolderId, clientFolderName);\n        }\n        // Check if project folder already exists\n        const projectFolderPath = `/${clientFolderName}/${projectFolderName}`;\n        const existingProjectFolder = await folderExists(accessToken, projectFolderPath);\n        let projectFolder;\n        if (existingProjectFolder) {\n            console.log(`Project folder \"${projectFolderName}\" already exists`);\n            projectFolder = existingProjectFolder;\n        } else {\n            console.log(`Creating project folder \"${projectFolderName}\"`);\n            projectFolder = await createFolder(accessToken, clientFolder.id, projectFolderName);\n        }\n        // Create share link for the project folder\n        let shareLink;\n        try {\n            shareLink = await createShareLink(accessToken, projectFolder.id);\n            console.log(`Created share link for project folder: ${shareLink}`);\n        } catch (error) {\n            console.warn('Failed to create share link for project folder:', error);\n        // Continue without share link\n        }\n        // Store folder information in the database\n        await storeProjectFolderInDatabase(projectId, projectFolder.id, projectFolder.webUrl, shareLink);\n        return {\n            folder: {\n                ...projectFolder,\n                shareLink\n            }\n        };\n    } catch (error) {\n        console.error('Error creating project folder:', error);\n        throw new Error(`Failed to create project folder: ${error instanceof Error ? error.message : 'Unknown error'}`);\n    }\n}\n/**\n * Create a schedule folder inside the project folder\n * @param scheduleId ID of the schedule to associate with the folder\n * @param scheduleCustomId Custom ID of the schedule (e.g., CYM-25-005)\n * @param scheduleDate Date of the schedule (YYYY-MM-DD format)\n * @param projectCustomId Custom ID of the project\n * @param projectName Name of the project\n * @param clientCustomId Custom ID of the client\n * @param clientName Name of the client\n * @returns Object containing folder information for the main schedule folder\n */ async function createScheduleFolder(scheduleId, scheduleCustomId, scheduleDate, projectCustomId, projectName, clientCustomId, clientName) {\n    try {\n        // Get access token\n        const accessToken = await getAccessToken();\n        // Create folder names\n        const clientFolderName = `${clientCustomId} ${clientName}`;\n        const projectFolderName = `${projectCustomId} ${projectName}`;\n        const scheduleFolderName = `${scheduleCustomId} ${scheduleDate}`;\n        // Ensure client folder exists\n        const clientFolderPath = `/${clientFolderName}`;\n        let clientFolder = await folderExists(accessToken, clientFolderPath);\n        if (!clientFolder) {\n            const rootFolderId = await getRootFolderId(accessToken);\n            clientFolder = await createFolder(accessToken, rootFolderId, clientFolderName);\n        }\n        // Ensure project folder exists\n        const projectFolderPath = `/${clientFolderName}/${projectFolderName}`;\n        let projectFolder = await folderExists(accessToken, projectFolderPath);\n        if (!projectFolder) {\n            projectFolder = await createFolder(accessToken, clientFolder.id, projectFolderName);\n        }\n        // Check if schedule folder already exists\n        const scheduleFolderPath = `/${clientFolderName}/${projectFolderName}/${scheduleFolderName}`;\n        const existingScheduleFolder = await folderExists(accessToken, scheduleFolderPath);\n        let scheduleFolder;\n        if (existingScheduleFolder) {\n            console.log(`Schedule folder \"${scheduleFolderName}\" already exists`);\n            scheduleFolder = existingScheduleFolder;\n        } else {\n            console.log(`Creating schedule folder \"${scheduleFolderName}\"`);\n            scheduleFolder = await createFolder(accessToken, projectFolder.id, scheduleFolderName);\n        }\n        // Create Raw and Output subfolders inside the schedule folder\n        try {\n            console.log(`Creating \"Raw\" subfolder in schedule folder \"${scheduleFolderName}\"`);\n            await createFolder(accessToken, scheduleFolder.id, 'Raw');\n            console.log(`Successfully created \"Raw\" subfolder`);\n            console.log(`Creating \"Output\" subfolder in schedule folder \"${scheduleFolderName}\"`);\n            await createFolder(accessToken, scheduleFolder.id, 'Output');\n            console.log(`Successfully created \"Output\" subfolder`);\n        } catch (subfolderError) {\n            console.warn('Failed to create subfolders (Raw/Output) in schedule folder:', subfolderError);\n        // Don't fail the entire operation if subfolder creation fails\n        }\n        // Create share link for the schedule folder\n        let scheduleShareLink;\n        try {\n            scheduleShareLink = await createShareLink(accessToken, scheduleFolder.id);\n            console.log(`Created share link for schedule folder: ${scheduleShareLink}`);\n        } catch (error) {\n            console.warn('Failed to create share link for schedule folder:', error);\n        }\n        // Store folder information in the database (only main schedule folder, not subfolders)\n        await storeScheduleFolderLinksInDatabase(scheduleId, scheduleFolder.id, scheduleFolder.webUrl, scheduleShareLink);\n        return {\n            folder: {\n                ...scheduleFolder,\n                shareLink: scheduleShareLink\n            }\n        };\n    } catch (error) {\n        console.error('Error creating schedule folder:', error);\n        throw new Error(`Failed to create schedule folder: ${error instanceof Error ? error.message : 'Unknown error'}`);\n    }\n}\n/**\n * Create folder structure in SharePoint for a project and store links in database\n * @param scheduleId ID of the schedule to associate with the folders\n * @param clientName Name of the client\n * @param projectName Name of the project\n * @param date Date of the project (YYYY-MM-DD format)\n * @returns Object containing folder information for the main schedule folder\n */ async function createProjectFolderStructure(scheduleId, clientName, projectName, date) {\n    try {\n        // Get access token\n        const accessToken = await getAccessToken();\n        // Get the Clients folder ID (root for our structure)\n        let currentFolderId = await getClientsFolderId(accessToken);\n        // Build the folder path: /Clients/<clientName>/<projectName>/<date>/\n        const folderPath = `/${clientName}/${projectName}/${date}`;\n        const fullPath = `/Clients${folderPath}`;\n        // Create each folder in the path if it doesn't exist\n        const pathParts = [\n            clientName,\n            projectName,\n            date\n        ];\n        for (const part of pathParts){\n            const currentPath = `/Clients/${pathParts.slice(0, pathParts.indexOf(part) + 1).join('/')}`;\n            const existingFolder = await folderExists(accessToken, currentPath);\n            if (existingFolder) {\n                console.log(`Folder \"${part}\" already exists at ${currentPath}`);\n                currentFolderId = existingFolder.id;\n            } else {\n                console.log(`Creating folder \"${part}\" at ${currentPath}`);\n                const newFolder = await createFolder(accessToken, currentFolderId, part);\n                currentFolderId = newFolder.id;\n            }\n        }\n        // Create share link for the main folder\n        const shareLink = await createShareLink(accessToken, currentFolderId);\n        // Get the main folder information\n        const mainFolder = {\n            id: currentFolderId,\n            webUrl: fullPath,\n            shareLink: shareLink\n        };\n        // Store folder information in the database\n        await storeScheduleFolderLinksInDatabase(scheduleId, mainFolder.id, mainFolder.webUrl, mainFolder.shareLink);\n        return mainFolder;\n    } catch (error) {\n        console.error('Error creating project folder structure:', error);\n        throw new Error(`Failed to create folder structure: ${error instanceof Error ? error.message : 'Unknown error'}`);\n    }\n}\n/**\n * Create a subfolder if it doesn't already exist\n * @param accessToken Microsoft Graph API access token\n * @param parentFolderId ID of parent folder\n * @param folderName Name of folder to create\n * @returns Folder information\n */ async function createSubfolderIfNotExists(accessToken, parentFolderId, folderName) {\n    try {\n        // Try to get the folder first\n        const siteId = await getSiteId(accessToken);\n        const response = await fetch(`https://graph.microsoft.com/v1.0/sites/${siteId}/drives/${DRIVE_ID}/items/${parentFolderId}/children?$filter=name eq '${folderName}'`, {\n            headers: {\n                Authorization: `Bearer ${accessToken}`\n            }\n        });\n        if (!response.ok) {\n            // If we get an error other than 404, re-throw it\n            if (response.status !== 404) {\n                throw new Error(`HTTP error! status: ${response.status}`);\n            }\n        // If 404, continue to create the folder\n        } else {\n            const data = await response.json();\n            // Check if folder exists in response\n            if (data.value && data.value.length > 0) {\n                const existingFolder = data.value[0];\n                if (existingFolder.folder) {\n                    console.log(`Folder \"${folderName}\" already exists`);\n                    return {\n                        id: existingFolder.id,\n                        webUrl: existingFolder.webUrl\n                    };\n                }\n            }\n        }\n        // Folder doesn't exist, create it\n        console.log(`Creating folder \"${folderName}\"`);\n        return await createFolder(accessToken, parentFolderId, folderName);\n    } catch (error) {\n        console.error(`Error checking/creating folder \"${folderName}\":`, error);\n        // Try to create the folder directly\n        return await createFolder(accessToken, parentFolderId, folderName);\n    }\n}\n/**\n * Store client folder information in the database\n * @param clientId ID of the client to update\n * @param folderId SharePoint ID of the client folder\n * @param folderUrl SharePoint URL of the client folder\n * @param shareLink Public share link for the client folder (optional)\n */ async function storeClientFolderInDatabase(clientId, folderId, folderUrl, shareLink) {\n    try {\n        // Use server-side Supabase client\n        const { createClient } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @supabase/supabase-js */ \"(ssr)/./node_modules/@supabase/supabase-js/dist/module/index.js\"));\n        // Validate environment variables with better error context\n        const supabaseUrl = \"https://qxgfulribhhohmggyroq.supabase.co\";\n        const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;\n        console.log('🔍 Environment variable check in storeClientFolderInDatabase:', {\n            supabaseUrl: supabaseUrl ? 'Present' : 'Missing',\n            supabaseServiceKey: supabaseServiceKey ? 'Present' : 'Missing',\n            nodeEnv: \"development\",\n            context: 'background-job'\n        });\n        if (!supabaseUrl) {\n            console.error('❌ NEXT_PUBLIC_SUPABASE_URL is missing in background job context');\n            throw new Error('NEXT_PUBLIC_SUPABASE_URL is required');\n        }\n        if (!supabaseServiceKey) {\n            console.error('❌ SUPABASE_SERVICE_ROLE_KEY is missing in background job context');\n            throw new Error('SUPABASE_SERVICE_ROLE_KEY is required');\n        }\n        const supabase = createClient(supabaseUrl, supabaseServiceKey);\n        const updateData = {\n            sharepoint_folder_id: folderId,\n            sharepoint_folder_url: folderUrl\n        };\n        if (shareLink) {\n            updateData.sharepoint_share_link = shareLink;\n        }\n        const { error } = await supabase.from('clients').update(updateData).eq('id', clientId);\n        if (error) {\n            console.error('Error storing client folder in database:', error);\n            throw new Error('Failed to store client folder in database');\n        }\n        console.log('Successfully stored client folder in database for client:', clientId);\n    } catch (error) {\n        console.error('Error in storeClientFolderInDatabase:', error);\n        throw error;\n    }\n}\n/**\n * Store project folder information in the database\n * @param projectId ID of the project to update\n * @param folderId SharePoint ID of the project folder\n * @param folderUrl SharePoint URL of the project folder\n * @param shareLink Public share link for the project folder (optional)\n */ async function storeProjectFolderInDatabase(projectId, folderId, folderUrl, shareLink) {\n    try {\n        // Use server-side Supabase client\n        const { createClient } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @supabase/supabase-js */ \"(ssr)/./node_modules/@supabase/supabase-js/dist/module/index.js\"));\n        // Validate environment variables with better error context\n        const supabaseUrl = \"https://qxgfulribhhohmggyroq.supabase.co\";\n        const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;\n        console.log('🔍 Environment variable check in getScheduleFolderLinks:', {\n            supabaseUrl: supabaseUrl ? 'Present' : 'Missing',\n            supabaseServiceKey: supabaseServiceKey ? 'Present' : 'Missing',\n            nodeEnv: \"development\",\n            context: 'background-job'\n        });\n        if (!supabaseUrl) {\n            console.error('❌ NEXT_PUBLIC_SUPABASE_URL is missing in background job context');\n            throw new Error('NEXT_PUBLIC_SUPABASE_URL is required');\n        }\n        if (!supabaseServiceKey) {\n            console.error('❌ SUPABASE_SERVICE_ROLE_KEY is missing in background job context');\n            throw new Error('SUPABASE_SERVICE_ROLE_KEY is required');\n        }\n        const supabase = createClient(supabaseUrl, supabaseServiceKey);\n        const updateData = {\n            sharepoint_folder_id: folderId,\n            sharepoint_folder_url: folderUrl\n        };\n        if (shareLink) {\n            updateData.sharepoint_share_link = shareLink;\n        }\n        const { error } = await supabase.from('projects').update(updateData).eq('id', projectId);\n        if (error) {\n            console.error('Error storing project folder in database:', error);\n            throw new Error('Failed to store project folder in database');\n        }\n        console.log('Successfully stored project folder in database for project:', projectId);\n    } catch (error) {\n        console.error('Error in storeProjectFolderInDatabase:', error);\n        throw error;\n    }\n}\n/**\n * Store schedule folder links in the schedules table\n * @param scheduleId ID of the schedule\n * @param scheduleFolderId SharePoint ID of the schedule folder\n * @param scheduleFolderUrl SharePoint URL of the schedule folder\n * @param scheduleShareLink Public share link for the schedule folder\n */ async function storeScheduleFolderLinksInDatabase(scheduleId, scheduleFolderId, scheduleFolderUrl, scheduleShareLink) {\n    try {\n        // Use server-side Supabase client\n        const { createClient } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @supabase/supabase-js */ \"(ssr)/./node_modules/@supabase/supabase-js/dist/module/index.js\"));\n        // Validate environment variables with better error context\n        const supabaseUrl = \"https://qxgfulribhhohmggyroq.supabase.co\";\n        const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;\n        console.log('🔍 Environment variable check in storeScheduleFolderLinksInDatabase:', {\n            supabaseUrl: supabaseUrl ? 'Present' : 'Missing',\n            supabaseServiceKey: supabaseServiceKey ? 'Present' : 'Missing',\n            nodeEnv: \"development\",\n            context: 'background-job'\n        });\n        if (!supabaseUrl) {\n            console.error('❌ NEXT_PUBLIC_SUPABASE_URL is missing in background job context');\n            throw new Error('NEXT_PUBLIC_SUPABASE_URL is required');\n        }\n        if (!supabaseServiceKey) {\n            console.error('❌ SUPABASE_SERVICE_ROLE_KEY is missing in background job context');\n            throw new Error('SUPABASE_SERVICE_ROLE_KEY is required');\n        }\n        const supabase = createClient(supabaseUrl, supabaseServiceKey);\n        // Prepare update data for schedules table\n        const updateData = {\n            sharepoint_folder_id: scheduleFolderId,\n            sharepoint_folder_url: scheduleFolderUrl\n        };\n        if (scheduleShareLink) {\n            updateData.sharepoint_share_link = scheduleShareLink;\n        }\n        // Update the schedules table\n        const { error } = await supabase.from('schedules').update(updateData).eq('id', scheduleId);\n        if (error) {\n            console.error('Error storing schedule folder links in database:', error);\n            throw new Error('Failed to store schedule folder links in database');\n        }\n        console.log('Successfully stored schedule folder links in schedules table for schedule:', scheduleId);\n    } catch (error) {\n        console.error('Error in storeScheduleFolderLinksInDatabase:', error);\n        throw error;\n    }\n}\n/**\n * Get schedule folder links from the database\n * @param scheduleId ID of the schedule\n * @returns Object containing folder information for the main schedule folder, or null if not found\n */ async function getScheduleFolderLinks(scheduleId) {\n    // Skip on client side\n    if (false) {}\n    try {\n        // Use server-side Supabase client\n        const { createClient } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @supabase/supabase-js */ \"(ssr)/./node_modules/@supabase/supabase-js/dist/module/index.js\"));\n        // Validate environment variables\n        const supabaseUrl = \"https://qxgfulribhhohmggyroq.supabase.co\";\n        const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;\n        if (!supabaseUrl) {\n            throw new Error('NEXT_PUBLIC_SUPABASE_URL is required');\n        }\n        if (!supabaseServiceKey) {\n            throw new Error('SUPABASE_SERVICE_ROLE_KEY is required');\n        }\n        const supabase = createClient(supabaseUrl, supabaseServiceKey);\n        const { data, error } = await supabase.from('schedules').select(`\n        sharepoint_folder_id,\n        sharepoint_folder_url,\n        sharepoint_share_link\n      `).eq('id', scheduleId).single();\n        if (error) {\n            console.error('Error getting schedule folder links from database:', error);\n            return null;\n        }\n        if (!data || !data.sharepoint_folder_id || !data.sharepoint_folder_url) {\n            console.log('No schedule folder links found for schedule:', scheduleId);\n            return null;\n        }\n        const result = {\n            id: data.sharepoint_folder_id,\n            webUrl: data.sharepoint_folder_url,\n            shareLink: data.sharepoint_share_link\n        };\n        return result;\n    } catch (error) {\n        console.error('Error in getScheduleFolderLinks:', error);\n        return null;\n    }\n}\n/**\n * Get the ID of the Clients folder (legacy function for compatibility)\n * @param accessToken Microsoft Graph API access token\n * @returns ID of the Clients folder\n */ async function getClientsFolderId(accessToken) {\n    // For backward compatibility, return the root folder ID\n    return await getRootFolderId(accessToken);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/microsoft-graph.ts\n");

/***/ })

};
;