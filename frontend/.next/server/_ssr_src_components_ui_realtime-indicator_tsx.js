"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_src_components_ui_realtime-indicator_tsx";
exports.ids = ["_ssr_src_components_ui_realtime-indicator_tsx"];
exports.modules = {

/***/ "(ssr)/./src/components/ui/realtime-indicator.tsx":
/*!**************************************************!*\
  !*** ./src/components/ui/realtime-indicator.tsx ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RealtimeIndicator: () => (/* binding */ RealtimeIndicator),\n/* harmony export */   useRealtimeStatus: () => (/* binding */ useRealtimeStatus)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/auth */ \"(ssr)/./src/lib/auth.ts\");\n/* harmony import */ var _barrel_optimize_names_Activity_Wifi_WifiOff_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Wifi,WifiOff,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/wifi-off.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Wifi_WifiOff_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Wifi,WifiOff,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/wifi.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Wifi_WifiOff_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Wifi,WifiOff,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Wifi_WifiOff_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Wifi,WifiOff,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ RealtimeIndicator,useRealtimeStatus auto */ \n\n\n\n\nfunction RealtimeIndicator({ className, showText = false, size = 'sm' }) {\n    const [isConnected, setIsConnected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isOnline, setIsOnline] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [lastActivity, setLastActivity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const supabase = (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.createClientSupabaseClient)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"RealtimeIndicator.useEffect\": ()=>{\n            // Check initial online status\n            setIsOnline(navigator.onLine);\n            // Listen for online/offline events\n            const handleOnline = {\n                \"RealtimeIndicator.useEffect.handleOnline\": ()=>setIsOnline(true)\n            }[\"RealtimeIndicator.useEffect.handleOnline\"];\n            const handleOffline = {\n                \"RealtimeIndicator.useEffect.handleOffline\": ()=>setIsOnline(false)\n            }[\"RealtimeIndicator.useEffect.handleOffline\"];\n            window.addEventListener('online', handleOnline);\n            window.addEventListener('offline', handleOffline);\n            // Set up Supabase connection monitoring\n            const channel = supabase.channel('connection-monitor').on('presence', {\n                event: 'sync'\n            }, {\n                \"RealtimeIndicator.useEffect.channel\": ()=>{\n                    setIsConnected(true);\n                    setLastActivity(new Date());\n                }\n            }[\"RealtimeIndicator.useEffect.channel\"]).on('presence', {\n                event: 'join'\n            }, {\n                \"RealtimeIndicator.useEffect.channel\": ()=>{\n                    setIsConnected(true);\n                    setLastActivity(new Date());\n                }\n            }[\"RealtimeIndicator.useEffect.channel\"]).on('presence', {\n                event: 'leave'\n            }, {\n                \"RealtimeIndicator.useEffect.channel\": ()=>{\n                    setIsConnected(false);\n                }\n            }[\"RealtimeIndicator.useEffect.channel\"]).subscribe({\n                \"RealtimeIndicator.useEffect.channel\": (status)=>{\n                    if (status === 'SUBSCRIBED') {\n                        setIsConnected(true);\n                        setLastActivity(new Date());\n                    } else if (status === 'CHANNEL_ERROR' || status === 'TIMED_OUT') {\n                        setIsConnected(false);\n                    }\n                }\n            }[\"RealtimeIndicator.useEffect.channel\"]);\n            // Monitor real-time activity with a test channel\n            const activityChannel = supabase.channel('activity-monitor').on('broadcast', {\n                event: 'ping'\n            }, {\n                \"RealtimeIndicator.useEffect.activityChannel\": ()=>{\n                    setLastActivity(new Date());\n                }\n            }[\"RealtimeIndicator.useEffect.activityChannel\"]).subscribe();\n            // Send periodic pings to test connectivity\n            const pingInterval = setInterval({\n                \"RealtimeIndicator.useEffect.pingInterval\": ()=>{\n                    if (isOnline) {\n                        activityChannel.send({\n                            type: 'broadcast',\n                            event: 'ping',\n                            payload: {\n                                timestamp: new Date().toISOString()\n                            }\n                        });\n                    }\n                }\n            }[\"RealtimeIndicator.useEffect.pingInterval\"], 30000) // Ping every 30 seconds\n            ;\n            return ({\n                \"RealtimeIndicator.useEffect\": ()=>{\n                    window.removeEventListener('online', handleOnline);\n                    window.removeEventListener('offline', handleOffline);\n                    clearInterval(pingInterval);\n                    supabase.removeChannel(channel);\n                    supabase.removeChannel(activityChannel);\n                }\n            })[\"RealtimeIndicator.useEffect\"];\n        }\n    }[\"RealtimeIndicator.useEffect\"], [\n        supabase,\n        isOnline\n    ]);\n    const getStatusColor = ()=>{\n        if (!isOnline) return 'text-red-500';\n        if (!isConnected) return 'text-yellow-500';\n        return 'text-green-500';\n    };\n    const getStatusText = ()=>{\n        if (!isOnline) return 'Offline';\n        if (!isConnected) return 'Connecting...';\n        return 'Real-time';\n    };\n    const getIcon = ()=>{\n        if (!isOnline) return _barrel_optimize_names_Activity_Wifi_WifiOff_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"];\n        if (!isConnected) return _barrel_optimize_names_Activity_Wifi_WifiOff_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"];\n        return _barrel_optimize_names_Activity_Wifi_WifiOff_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"];\n    };\n    const getSizeClasses = ()=>{\n        switch(size){\n            case 'sm':\n                return 'w-3 h-3';\n            case 'md':\n                return 'w-4 h-4';\n            case 'lg':\n                return 'w-5 h-5';\n            default:\n                return 'w-3 h-3';\n        }\n    };\n    const Icon = getIcon();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)('flex items-center space-x-1', className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(getSizeClasses(), getStatusColor(), isConnected && isOnline && 'animate-pulse')\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/realtime-indicator.tsx\",\n                        lineNumber: 125,\n                        columnNumber: 9\n                    }, this),\n                    isConnected && isOnline && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)('absolute inset-0 rounded-full animate-ping', getSizeClasses(), 'bg-green-400 opacity-20')\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/realtime-indicator.tsx\",\n                        lineNumber: 133,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/realtime-indicator.tsx\",\n                lineNumber: 124,\n                columnNumber: 7\n            }, this),\n            showText && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)('text-xs font-medium', getStatusColor()),\n                children: getStatusText()\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/realtime-indicator.tsx\",\n                lineNumber: 142,\n                columnNumber: 9\n            }, this),\n            lastActivity && isConnected && isOnline && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-1\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Wifi_WifiOff_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        className: \"w-2 h-2 text-green-400\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/realtime-indicator.tsx\",\n                        lineNumber: 153,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-xs text-gray-500\",\n                        children: lastActivity.toLocaleTimeString([], {\n                            hour: '2-digit',\n                            minute: '2-digit'\n                        })\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/realtime-indicator.tsx\",\n                        lineNumber: 154,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/realtime-indicator.tsx\",\n                lineNumber: 152,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/realtime-indicator.tsx\",\n        lineNumber: 120,\n        columnNumber: 5\n    }, this);\n}\n// Hook to get real-time connection status\nfunction useRealtimeStatus() {\n    const [isConnected, setIsConnected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isOnline, setIsOnline] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [lastActivity, setLastActivity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const supabase = (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.createClientSupabaseClient)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"useRealtimeStatus.useEffect\": ()=>{\n            setIsOnline(navigator.onLine);\n            const handleOnline = {\n                \"useRealtimeStatus.useEffect.handleOnline\": ()=>setIsOnline(true)\n            }[\"useRealtimeStatus.useEffect.handleOnline\"];\n            const handleOffline = {\n                \"useRealtimeStatus.useEffect.handleOffline\": ()=>setIsOnline(false)\n            }[\"useRealtimeStatus.useEffect.handleOffline\"];\n            window.addEventListener('online', handleOnline);\n            window.addEventListener('offline', handleOffline);\n            const channel = supabase.channel('status-monitor').on('presence', {\n                event: 'sync'\n            }, {\n                \"useRealtimeStatus.useEffect.channel\": ()=>{\n                    setIsConnected(true);\n                    setLastActivity(new Date());\n                }\n            }[\"useRealtimeStatus.useEffect.channel\"]).subscribe({\n                \"useRealtimeStatus.useEffect.channel\": (status)=>{\n                    setIsConnected(status === 'SUBSCRIBED');\n                    if (status === 'SUBSCRIBED') {\n                        setLastActivity(new Date());\n                    }\n                }\n            }[\"useRealtimeStatus.useEffect.channel\"]);\n            return ({\n                \"useRealtimeStatus.useEffect\": ()=>{\n                    window.removeEventListener('online', handleOnline);\n                    window.removeEventListener('offline', handleOffline);\n                    supabase.removeChannel(channel);\n                }\n            })[\"useRealtimeStatus.useEffect\"];\n        }\n    }[\"useRealtimeStatus.useEffect\"], [\n        supabase\n    ]);\n    return {\n        isConnected,\n        isOnline,\n        lastActivity,\n        status: !isOnline ? 'offline' : !isConnected ? 'connecting' : 'connected'\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/realtime-indicator.tsx\n");

/***/ })

};
;