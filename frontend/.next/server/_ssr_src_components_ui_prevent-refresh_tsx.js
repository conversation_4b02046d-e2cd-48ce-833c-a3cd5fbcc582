"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_src_components_ui_prevent-refresh_tsx";
exports.ids = ["_ssr_src_components_ui_prevent-refresh_tsx"];
exports.modules = {

/***/ "(ssr)/./src/components/ui/prevent-refresh.tsx":
/*!***********************************************!*\
  !*** ./src/components/ui/prevent-refresh.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PreventRefresh: () => (/* binding */ PreventRefresh),\n/* harmony export */   useUnsavedChanges: () => (/* binding */ useUnsavedChanges)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_RefreshCw_Wifi_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=RefreshCw,Wifi,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_RefreshCw_Wifi_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=RefreshCw,Wifi,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_RefreshCw_Wifi_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=RefreshCw,Wifi,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/wifi.js\");\n/* harmony import */ var _components_ui_realtime_indicator__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/realtime-indicator */ \"(ssr)/./src/components/ui/realtime-indicator.tsx\");\n/* __next_internal_client_entry_do_not_use__ PreventRefresh,useUnsavedChanges auto */ \n\n\n\n\nfunction PreventRefresh() {\n    const [showWarning, setShowWarning] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [hasUnsavedChanges, setHasUnsavedChanges] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { isConnected, isOnline } = (0,_components_ui_realtime_indicator__WEBPACK_IMPORTED_MODULE_3__.useRealtimeStatus)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PreventRefresh.useEffect\": ()=>{\n            // Prevent accidental refresh when there are unsaved changes\n            const handleBeforeUnload = {\n                \"PreventRefresh.useEffect.handleBeforeUnload\": (e)=>{\n                    if (hasUnsavedChanges) {\n                        e.preventDefault();\n                        e.returnValue = 'You have unsaved changes. Are you sure you want to leave?';\n                        return e.returnValue;\n                    }\n                }\n            }[\"PreventRefresh.useEffect.handleBeforeUnload\"];\n            // Show warning when user tries to refresh\n            const handleKeyDown = {\n                \"PreventRefresh.useEffect.handleKeyDown\": (e)=>{\n                    // Detect Ctrl+R, F5, or Cmd+R\n                    if (e.ctrlKey && e.key === 'r' || e.metaKey && e.key === 'r' || e.key === 'F5') {\n                        e.preventDefault();\n                        setShowWarning(true);\n                    }\n                }\n            }[\"PreventRefresh.useEffect.handleKeyDown\"];\n            window.addEventListener('beforeunload', handleBeforeUnload);\n            window.addEventListener('keydown', handleKeyDown);\n            return ({\n                \"PreventRefresh.useEffect\": ()=>{\n                    window.removeEventListener('beforeunload', handleBeforeUnload);\n                    window.removeEventListener('keydown', handleKeyDown);\n                }\n            })[\"PreventRefresh.useEffect\"];\n        }\n    }[\"PreventRefresh.useEffect\"], [\n        hasUnsavedChanges\n    ]);\n    // Auto-hide warning after 5 seconds\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PreventRefresh.useEffect\": ()=>{\n            if (showWarning) {\n                const timer = setTimeout({\n                    \"PreventRefresh.useEffect.timer\": ()=>{\n                        setShowWarning(false);\n                    }\n                }[\"PreventRefresh.useEffect.timer\"], 5000);\n                return ({\n                    \"PreventRefresh.useEffect\": ()=>clearTimeout(timer)\n                })[\"PreventRefresh.useEffect\"];\n            }\n        }\n    }[\"PreventRefresh.useEffect\"], [\n        showWarning\n    ]);\n    // Listen for form changes to set unsaved changes flag\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PreventRefresh.useEffect\": ()=>{\n            const handleFormChange = {\n                \"PreventRefresh.useEffect.handleFormChange\": ()=>{\n                    setHasUnsavedChanges(true);\n                }\n            }[\"PreventRefresh.useEffect.handleFormChange\"];\n            const handleFormSubmit = {\n                \"PreventRefresh.useEffect.handleFormSubmit\": ()=>{\n                    setHasUnsavedChanges(false);\n                }\n            }[\"PreventRefresh.useEffect.handleFormSubmit\"];\n            // Listen for form inputs\n            document.addEventListener('input', handleFormChange);\n            document.addEventListener('submit', handleFormSubmit);\n            return ({\n                \"PreventRefresh.useEffect\": ()=>{\n                    document.removeEventListener('input', handleFormChange);\n                    document.removeEventListener('submit', handleFormSubmit);\n                }\n            })[\"PreventRefresh.useEffect\"];\n        }\n    }[\"PreventRefresh.useEffect\"], []);\n    const handleForceRefresh = ()=>{\n        window.location.reload();\n    };\n    const handleDismiss = ()=>{\n        setShowWarning(false);\n    };\n    if (!showWarning) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed top-4 right-4 z-50 max-w-sm\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg p-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-start justify-between mb-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_RefreshCw_Wifi_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    className: \"w-5 h-5 text-blue-500\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/prevent-refresh.tsx\",\n                                    lineNumber: 90,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"font-semibold text-sm\",\n                                    children: \"Refresh Not Needed\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/prevent-refresh.tsx\",\n                                    lineNumber: 91,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/prevent-refresh.tsx\",\n                            lineNumber: 89,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            variant: \"ghost\",\n                            size: \"sm\",\n                            onClick: handleDismiss,\n                            className: \"h-6 w-6 p-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_RefreshCw_Wifi_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                className: \"w-4 h-4\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/prevent-refresh.tsx\",\n                                lineNumber: 99,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/prevent-refresh.tsx\",\n                            lineNumber: 93,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/prevent-refresh.tsx\",\n                    lineNumber: 88,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-muted-foreground\",\n                            children: \"This app updates automatically in real-time. No need to refresh!\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/prevent-refresh.tsx\",\n                            lineNumber: 104,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2 text-xs\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_RefreshCw_Wifi_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: `w-3 h-3 ${isConnected && isOnline ? 'text-green-500' : 'text-red-500'}`\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/prevent-refresh.tsx\",\n                                    lineNumber: 109,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: isConnected && isOnline ? 'text-green-600' : 'text-red-600',\n                                    children: isConnected && isOnline ? 'Real-time updates active' : 'Connection issues detected'\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/prevent-refresh.tsx\",\n                                    lineNumber: 110,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/prevent-refresh.tsx\",\n                            lineNumber: 108,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"outline\",\n                                    size: \"sm\",\n                                    onClick: handleDismiss,\n                                    className: \"flex-1 text-xs\",\n                                    children: \"Got it\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/prevent-refresh.tsx\",\n                                    lineNumber: 116,\n                                    columnNumber: 13\n                                }, this),\n                                (!isConnected || !isOnline) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"outline\",\n                                    size: \"sm\",\n                                    onClick: handleForceRefresh,\n                                    className: \"flex-1 text-xs text-orange-600 hover:text-orange-700\",\n                                    children: \"Force Refresh\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/prevent-refresh.tsx\",\n                                    lineNumber: 125,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/prevent-refresh.tsx\",\n                            lineNumber: 115,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/prevent-refresh.tsx\",\n                    lineNumber: 103,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/prevent-refresh.tsx\",\n            lineNumber: 87,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/prevent-refresh.tsx\",\n        lineNumber: 86,\n        columnNumber: 5\n    }, this);\n}\n// Hook to manage unsaved changes state\nfunction useUnsavedChanges() {\n    const [hasUnsavedChanges, setHasUnsavedChanges] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const markAsChanged = ()=>setHasUnsavedChanges(true);\n    const markAsSaved = ()=>setHasUnsavedChanges(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"useUnsavedChanges.useEffect\": ()=>{\n            const handleBeforeUnload = {\n                \"useUnsavedChanges.useEffect.handleBeforeUnload\": (e)=>{\n                    if (hasUnsavedChanges) {\n                        e.preventDefault();\n                        e.returnValue = 'You have unsaved changes. Are you sure you want to leave?';\n                        return e.returnValue;\n                    }\n                }\n            }[\"useUnsavedChanges.useEffect.handleBeforeUnload\"];\n            window.addEventListener('beforeunload', handleBeforeUnload);\n            return ({\n                \"useUnsavedChanges.useEffect\": ()=>window.removeEventListener('beforeunload', handleBeforeUnload)\n            })[\"useUnsavedChanges.useEffect\"];\n        }\n    }[\"useUnsavedChanges.useEffect\"], [\n        hasUnsavedChanges\n    ]);\n    return {\n        hasUnsavedChanges,\n        markAsChanged,\n        markAsSaved\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy91aS9wcmV2ZW50LXJlZnJlc2gudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7OztBQUUyQztBQUNJO0FBQ0U7QUFDcUI7QUFFL0QsU0FBU087SUFDZCxNQUFNLENBQUNDLGFBQWFDLGVBQWUsR0FBR1IsK0NBQVFBLENBQUM7SUFDL0MsTUFBTSxDQUFDUyxtQkFBbUJDLHFCQUFxQixHQUFHViwrQ0FBUUEsQ0FBQztJQUMzRCxNQUFNLEVBQUVXLFdBQVcsRUFBRUMsUUFBUSxFQUFFLEdBQUdQLG9GQUFpQkE7SUFFbkROLGdEQUFTQTtvQ0FBQztZQUNSLDREQUE0RDtZQUM1RCxNQUFNYzsrREFBcUIsQ0FBQ0M7b0JBQzFCLElBQUlMLG1CQUFtQjt3QkFDckJLLEVBQUVDLGNBQWM7d0JBQ2hCRCxFQUFFRSxXQUFXLEdBQUc7d0JBQ2hCLE9BQU9GLEVBQUVFLFdBQVc7b0JBQ3RCO2dCQUNGOztZQUVBLDBDQUEwQztZQUMxQyxNQUFNQzswREFBZ0IsQ0FBQ0g7b0JBQ3JCLDhCQUE4QjtvQkFDOUIsSUFDRSxFQUFHSSxPQUFPLElBQUlKLEVBQUVLLEdBQUcsS0FBSyxPQUN2QkwsRUFBRU0sT0FBTyxJQUFJTixFQUFFSyxHQUFHLEtBQUssT0FDeEJMLEVBQUVLLEdBQUcsS0FBSyxNQUNWO3dCQUNBTCxFQUFFQyxjQUFjO3dCQUNoQlAsZUFBZTtvQkFDakI7Z0JBQ0Y7O1lBRUFhLE9BQU9DLGdCQUFnQixDQUFDLGdCQUFnQlQ7WUFDeENRLE9BQU9DLGdCQUFnQixDQUFDLFdBQVdMO1lBRW5DOzRDQUFPO29CQUNMSSxPQUFPRSxtQkFBbUIsQ0FBQyxnQkFBZ0JWO29CQUMzQ1EsT0FBT0UsbUJBQW1CLENBQUMsV0FBV047Z0JBQ3hDOztRQUNGO21DQUFHO1FBQUNSO0tBQWtCO0lBRXRCLG9DQUFvQztJQUNwQ1YsZ0RBQVNBO29DQUFDO1lBQ1IsSUFBSVEsYUFBYTtnQkFDZixNQUFNaUIsUUFBUUM7c0RBQVc7d0JBQ3ZCakIsZUFBZTtvQkFDakI7cURBQUc7Z0JBQ0g7Z0RBQU8sSUFBTWtCLGFBQWFGOztZQUM1QjtRQUNGO21DQUFHO1FBQUNqQjtLQUFZO0lBRWhCLHNEQUFzRDtJQUN0RFIsZ0RBQVNBO29DQUFDO1lBQ1IsTUFBTTRCOzZEQUFtQjtvQkFDdkJqQixxQkFBcUI7Z0JBQ3ZCOztZQUVBLE1BQU1rQjs2REFBbUI7b0JBQ3ZCbEIscUJBQXFCO2dCQUN2Qjs7WUFFQSx5QkFBeUI7WUFDekJtQixTQUFTUCxnQkFBZ0IsQ0FBQyxTQUFTSztZQUNuQ0UsU0FBU1AsZ0JBQWdCLENBQUMsVUFBVU07WUFFcEM7NENBQU87b0JBQ0xDLFNBQVNOLG1CQUFtQixDQUFDLFNBQVNJO29CQUN0Q0UsU0FBU04sbUJBQW1CLENBQUMsVUFBVUs7Z0JBQ3pDOztRQUNGO21DQUFHLEVBQUU7SUFFTCxNQUFNRSxxQkFBcUI7UUFDekJULE9BQU9VLFFBQVEsQ0FBQ0MsTUFBTTtJQUN4QjtJQUVBLE1BQU1DLGdCQUFnQjtRQUNwQnpCLGVBQWU7SUFDakI7SUFFQSxJQUFJLENBQUNELGFBQWEsT0FBTztJQUV6QixxQkFDRSw4REFBQzJCO1FBQUlDLFdBQVU7a0JBQ2IsNEVBQUNEO1lBQUlDLFdBQVU7OzhCQUNiLDhEQUFDRDtvQkFBSUMsV0FBVTs7c0NBQ2IsOERBQUNEOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ2hDLDRGQUFTQTtvQ0FBQ2dDLFdBQVU7Ozs7Ozs4Q0FDckIsOERBQUNDO29DQUFHRCxXQUFVOzhDQUF3Qjs7Ozs7Ozs7Ozs7O3NDQUV4Qyw4REFBQ2xDLHlEQUFNQTs0QkFDTG9DLFNBQVE7NEJBQ1JDLE1BQUs7NEJBQ0xDLFNBQVNOOzRCQUNURSxXQUFVO3NDQUVWLDRFQUFDakMsNEZBQUNBO2dDQUFDaUMsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7OEJBSWpCLDhEQUFDRDtvQkFBSUMsV0FBVTs7c0NBQ2IsOERBQUNLOzRCQUFFTCxXQUFVO3NDQUFnQzs7Ozs7O3NDQUk3Qyw4REFBQ0Q7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDL0IsNEZBQUlBO29DQUFDK0IsV0FBVyxDQUFDLFFBQVEsRUFBRXhCLGVBQWVDLFdBQVcsbUJBQW1CLGdCQUFnQjs7Ozs7OzhDQUN6Riw4REFBQzZCO29DQUFLTixXQUFXeEIsZUFBZUMsV0FBVyxtQkFBbUI7OENBQzNERCxlQUFlQyxXQUFXLDZCQUE2Qjs7Ozs7Ozs7Ozs7O3NDQUk1RCw4REFBQ3NCOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ2xDLHlEQUFNQTtvQ0FDTG9DLFNBQVE7b0NBQ1JDLE1BQUs7b0NBQ0xDLFNBQVNOO29DQUNURSxXQUFVOzhDQUNYOzs7Ozs7Z0NBR0MsRUFBQ3hCLGVBQWUsQ0FBQ0MsUUFBTyxtQkFDeEIsOERBQUNYLHlEQUFNQTtvQ0FDTG9DLFNBQVE7b0NBQ1JDLE1BQUs7b0NBQ0xDLFNBQVNUO29DQUNUSyxXQUFVOzhDQUNYOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQVNmO0FBRUEsdUNBQXVDO0FBQ2hDLFNBQVNPO0lBQ2QsTUFBTSxDQUFDakMsbUJBQW1CQyxxQkFBcUIsR0FBR1YsK0NBQVFBLENBQUM7SUFFM0QsTUFBTTJDLGdCQUFnQixJQUFNakMscUJBQXFCO0lBQ2pELE1BQU1rQyxjQUFjLElBQU1sQyxxQkFBcUI7SUFFL0NYLGdEQUFTQTt1Q0FBQztZQUNSLE1BQU1jO2tFQUFxQixDQUFDQztvQkFDMUIsSUFBSUwsbUJBQW1CO3dCQUNyQkssRUFBRUMsY0FBYzt3QkFDaEJELEVBQUVFLFdBQVcsR0FBRzt3QkFDaEIsT0FBT0YsRUFBRUUsV0FBVztvQkFDdEI7Z0JBQ0Y7O1lBRUFLLE9BQU9DLGdCQUFnQixDQUFDLGdCQUFnQlQ7WUFDeEM7K0NBQU8sSUFBTVEsT0FBT0UsbUJBQW1CLENBQUMsZ0JBQWdCVjs7UUFDMUQ7c0NBQUc7UUFBQ0o7S0FBa0I7SUFFdEIsT0FBTztRQUNMQTtRQUNBa0M7UUFDQUM7SUFDRjtBQUNGIiwic291cmNlcyI6WyIvVXNlcnMvdmlqYXl5YXNvL0RvY3VtZW50cy9EZXZlbG9wbWVudC9jeW1hdGljcy9mcm9udGVuZC9zcmMvY29tcG9uZW50cy91aS9wcmV2ZW50LXJlZnJlc2gudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50J1xuXG5pbXBvcnQgeyB1c2VFZmZlY3QsIHVzZVN0YXRlIH0gZnJvbSAncmVhY3QnXG5pbXBvcnQgeyBCdXR0b24gfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvYnV0dG9uJ1xuaW1wb3J0IHsgWCwgUmVmcmVzaEN3LCBXaWZpIH0gZnJvbSAnbHVjaWRlLXJlYWN0J1xuaW1wb3J0IHsgdXNlUmVhbHRpbWVTdGF0dXMgfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvcmVhbHRpbWUtaW5kaWNhdG9yJ1xuXG5leHBvcnQgZnVuY3Rpb24gUHJldmVudFJlZnJlc2goKSB7XG4gIGNvbnN0IFtzaG93V2FybmluZywgc2V0U2hvd1dhcm5pbmddID0gdXNlU3RhdGUoZmFsc2UpXG4gIGNvbnN0IFtoYXNVbnNhdmVkQ2hhbmdlcywgc2V0SGFzVW5zYXZlZENoYW5nZXNdID0gdXNlU3RhdGUoZmFsc2UpXG4gIGNvbnN0IHsgaXNDb25uZWN0ZWQsIGlzT25saW5lIH0gPSB1c2VSZWFsdGltZVN0YXR1cygpXG5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICAvLyBQcmV2ZW50IGFjY2lkZW50YWwgcmVmcmVzaCB3aGVuIHRoZXJlIGFyZSB1bnNhdmVkIGNoYW5nZXNcbiAgICBjb25zdCBoYW5kbGVCZWZvcmVVbmxvYWQgPSAoZTogQmVmb3JlVW5sb2FkRXZlbnQpID0+IHtcbiAgICAgIGlmIChoYXNVbnNhdmVkQ2hhbmdlcykge1xuICAgICAgICBlLnByZXZlbnREZWZhdWx0KClcbiAgICAgICAgZS5yZXR1cm5WYWx1ZSA9ICdZb3UgaGF2ZSB1bnNhdmVkIGNoYW5nZXMuIEFyZSB5b3Ugc3VyZSB5b3Ugd2FudCB0byBsZWF2ZT8nXG4gICAgICAgIHJldHVybiBlLnJldHVyblZhbHVlXG4gICAgICB9XG4gICAgfVxuXG4gICAgLy8gU2hvdyB3YXJuaW5nIHdoZW4gdXNlciB0cmllcyB0byByZWZyZXNoXG4gICAgY29uc3QgaGFuZGxlS2V5RG93biA9IChlOiBLZXlib2FyZEV2ZW50KSA9PiB7XG4gICAgICAvLyBEZXRlY3QgQ3RybCtSLCBGNSwgb3IgQ21kK1JcbiAgICAgIGlmIChcbiAgICAgICAgKGUuY3RybEtleSAmJiBlLmtleSA9PT0gJ3InKSB8fFxuICAgICAgICAoZS5tZXRhS2V5ICYmIGUua2V5ID09PSAncicpIHx8XG4gICAgICAgIGUua2V5ID09PSAnRjUnXG4gICAgICApIHtcbiAgICAgICAgZS5wcmV2ZW50RGVmYXVsdCgpXG4gICAgICAgIHNldFNob3dXYXJuaW5nKHRydWUpXG4gICAgICB9XG4gICAgfVxuXG4gICAgd2luZG93LmFkZEV2ZW50TGlzdGVuZXIoJ2JlZm9yZXVubG9hZCcsIGhhbmRsZUJlZm9yZVVubG9hZClcbiAgICB3aW5kb3cuYWRkRXZlbnRMaXN0ZW5lcigna2V5ZG93bicsIGhhbmRsZUtleURvd24pXG5cbiAgICByZXR1cm4gKCkgPT4ge1xuICAgICAgd2luZG93LnJlbW92ZUV2ZW50TGlzdGVuZXIoJ2JlZm9yZXVubG9hZCcsIGhhbmRsZUJlZm9yZVVubG9hZClcbiAgICAgIHdpbmRvdy5yZW1vdmVFdmVudExpc3RlbmVyKCdrZXlkb3duJywgaGFuZGxlS2V5RG93bilcbiAgICB9XG4gIH0sIFtoYXNVbnNhdmVkQ2hhbmdlc10pXG5cbiAgLy8gQXV0by1oaWRlIHdhcm5pbmcgYWZ0ZXIgNSBzZWNvbmRzXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgaWYgKHNob3dXYXJuaW5nKSB7XG4gICAgICBjb25zdCB0aW1lciA9IHNldFRpbWVvdXQoKCkgPT4ge1xuICAgICAgICBzZXRTaG93V2FybmluZyhmYWxzZSlcbiAgICAgIH0sIDUwMDApXG4gICAgICByZXR1cm4gKCkgPT4gY2xlYXJUaW1lb3V0KHRpbWVyKVxuICAgIH1cbiAgfSwgW3Nob3dXYXJuaW5nXSlcblxuICAvLyBMaXN0ZW4gZm9yIGZvcm0gY2hhbmdlcyB0byBzZXQgdW5zYXZlZCBjaGFuZ2VzIGZsYWdcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBjb25zdCBoYW5kbGVGb3JtQ2hhbmdlID0gKCkgPT4ge1xuICAgICAgc2V0SGFzVW5zYXZlZENoYW5nZXModHJ1ZSlcbiAgICB9XG5cbiAgICBjb25zdCBoYW5kbGVGb3JtU3VibWl0ID0gKCkgPT4ge1xuICAgICAgc2V0SGFzVW5zYXZlZENoYW5nZXMoZmFsc2UpXG4gICAgfVxuXG4gICAgLy8gTGlzdGVuIGZvciBmb3JtIGlucHV0c1xuICAgIGRvY3VtZW50LmFkZEV2ZW50TGlzdGVuZXIoJ2lucHV0JywgaGFuZGxlRm9ybUNoYW5nZSlcbiAgICBkb2N1bWVudC5hZGRFdmVudExpc3RlbmVyKCdzdWJtaXQnLCBoYW5kbGVGb3JtU3VibWl0KVxuXG4gICAgcmV0dXJuICgpID0+IHtcbiAgICAgIGRvY3VtZW50LnJlbW92ZUV2ZW50TGlzdGVuZXIoJ2lucHV0JywgaGFuZGxlRm9ybUNoYW5nZSlcbiAgICAgIGRvY3VtZW50LnJlbW92ZUV2ZW50TGlzdGVuZXIoJ3N1Ym1pdCcsIGhhbmRsZUZvcm1TdWJtaXQpXG4gICAgfVxuICB9LCBbXSlcblxuICBjb25zdCBoYW5kbGVGb3JjZVJlZnJlc2ggPSAoKSA9PiB7XG4gICAgd2luZG93LmxvY2F0aW9uLnJlbG9hZCgpXG4gIH1cblxuICBjb25zdCBoYW5kbGVEaXNtaXNzID0gKCkgPT4ge1xuICAgIHNldFNob3dXYXJuaW5nKGZhbHNlKVxuICB9XG5cbiAgaWYgKCFzaG93V2FybmluZykgcmV0dXJuIG51bGxcblxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwiZml4ZWQgdG9wLTQgcmlnaHQtNCB6LTUwIG1heC13LXNtXCI+XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXdoaXRlIGRhcms6YmctZ3JheS04MDAgYm9yZGVyIGJvcmRlci1ncmF5LTIwMCBkYXJrOmJvcmRlci1ncmF5LTcwMCByb3VuZGVkLWxnIHNoYWRvdy1sZyBwLTRcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLXN0YXJ0IGp1c3RpZnktYmV0d2VlbiBtYi0zXCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTJcIj5cbiAgICAgICAgICAgIDxSZWZyZXNoQ3cgY2xhc3NOYW1lPVwidy01IGgtNSB0ZXh0LWJsdWUtNTAwXCIgLz5cbiAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJmb250LXNlbWlib2xkIHRleHQtc21cIj5SZWZyZXNoIE5vdCBOZWVkZWQ8L2gzPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgIHZhcmlhbnQ9XCJnaG9zdFwiXG4gICAgICAgICAgICBzaXplPVwic21cIlxuICAgICAgICAgICAgb25DbGljaz17aGFuZGxlRGlzbWlzc31cbiAgICAgICAgICAgIGNsYXNzTmFtZT1cImgtNiB3LTYgcC0wXCJcbiAgICAgICAgICA+XG4gICAgICAgICAgICA8WCBjbGFzc05hbWU9XCJ3LTQgaC00XCIgLz5cbiAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgPC9kaXY+XG4gICAgICAgIFxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktM1wiPlxuICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1tdXRlZC1mb3JlZ3JvdW5kXCI+XG4gICAgICAgICAgICBUaGlzIGFwcCB1cGRhdGVzIGF1dG9tYXRpY2FsbHkgaW4gcmVhbC10aW1lLiBObyBuZWVkIHRvIHJlZnJlc2ghXG4gICAgICAgICAgPC9wPlxuICAgICAgICAgIFxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yIHRleHQteHNcIj5cbiAgICAgICAgICAgIDxXaWZpIGNsYXNzTmFtZT17YHctMyBoLTMgJHtpc0Nvbm5lY3RlZCAmJiBpc09ubGluZSA/ICd0ZXh0LWdyZWVuLTUwMCcgOiAndGV4dC1yZWQtNTAwJ31gfSAvPlxuICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPXtpc0Nvbm5lY3RlZCAmJiBpc09ubGluZSA/ICd0ZXh0LWdyZWVuLTYwMCcgOiAndGV4dC1yZWQtNjAwJ30+XG4gICAgICAgICAgICAgIHtpc0Nvbm5lY3RlZCAmJiBpc09ubGluZSA/ICdSZWFsLXRpbWUgdXBkYXRlcyBhY3RpdmUnIDogJ0Nvbm5lY3Rpb24gaXNzdWVzIGRldGVjdGVkJ31cbiAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICBcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggc3BhY2UteC0yXCI+XG4gICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgIHZhcmlhbnQ9XCJvdXRsaW5lXCJcbiAgICAgICAgICAgICAgc2l6ZT1cInNtXCJcbiAgICAgICAgICAgICAgb25DbGljaz17aGFuZGxlRGlzbWlzc31cbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZmxleC0xIHRleHQteHNcIlxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICBHb3QgaXRcbiAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgeyghaXNDb25uZWN0ZWQgfHwgIWlzT25saW5lKSAmJiAoXG4gICAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgICB2YXJpYW50PVwib3V0bGluZVwiXG4gICAgICAgICAgICAgICAgc2l6ZT1cInNtXCJcbiAgICAgICAgICAgICAgICBvbkNsaWNrPXtoYW5kbGVGb3JjZVJlZnJlc2h9XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZmxleC0xIHRleHQteHMgdGV4dC1vcmFuZ2UtNjAwIGhvdmVyOnRleHQtb3JhbmdlLTcwMFwiXG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICBGb3JjZSBSZWZyZXNoXG4gICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgKX1cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cbiAgICA8L2Rpdj5cbiAgKVxufVxuXG4vLyBIb29rIHRvIG1hbmFnZSB1bnNhdmVkIGNoYW5nZXMgc3RhdGVcbmV4cG9ydCBmdW5jdGlvbiB1c2VVbnNhdmVkQ2hhbmdlcygpIHtcbiAgY29uc3QgW2hhc1Vuc2F2ZWRDaGFuZ2VzLCBzZXRIYXNVbnNhdmVkQ2hhbmdlc10gPSB1c2VTdGF0ZShmYWxzZSlcblxuICBjb25zdCBtYXJrQXNDaGFuZ2VkID0gKCkgPT4gc2V0SGFzVW5zYXZlZENoYW5nZXModHJ1ZSlcbiAgY29uc3QgbWFya0FzU2F2ZWQgPSAoKSA9PiBzZXRIYXNVbnNhdmVkQ2hhbmdlcyhmYWxzZSlcblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGNvbnN0IGhhbmRsZUJlZm9yZVVubG9hZCA9IChlOiBCZWZvcmVVbmxvYWRFdmVudCkgPT4ge1xuICAgICAgaWYgKGhhc1Vuc2F2ZWRDaGFuZ2VzKSB7XG4gICAgICAgIGUucHJldmVudERlZmF1bHQoKVxuICAgICAgICBlLnJldHVyblZhbHVlID0gJ1lvdSBoYXZlIHVuc2F2ZWQgY2hhbmdlcy4gQXJlIHlvdSBzdXJlIHlvdSB3YW50IHRvIGxlYXZlPydcbiAgICAgICAgcmV0dXJuIGUucmV0dXJuVmFsdWVcbiAgICAgIH1cbiAgICB9XG5cbiAgICB3aW5kb3cuYWRkRXZlbnRMaXN0ZW5lcignYmVmb3JldW5sb2FkJywgaGFuZGxlQmVmb3JlVW5sb2FkKVxuICAgIHJldHVybiAoKSA9PiB3aW5kb3cucmVtb3ZlRXZlbnRMaXN0ZW5lcignYmVmb3JldW5sb2FkJywgaGFuZGxlQmVmb3JlVW5sb2FkKVxuICB9LCBbaGFzVW5zYXZlZENoYW5nZXNdKVxuXG4gIHJldHVybiB7XG4gICAgaGFzVW5zYXZlZENoYW5nZXMsXG4gICAgbWFya0FzQ2hhbmdlZCxcbiAgICBtYXJrQXNTYXZlZFxuICB9XG59XG4iXSwibmFtZXMiOlsidXNlRWZmZWN0IiwidXNlU3RhdGUiLCJCdXR0b24iLCJYIiwiUmVmcmVzaEN3IiwiV2lmaSIsInVzZVJlYWx0aW1lU3RhdHVzIiwiUHJldmVudFJlZnJlc2giLCJzaG93V2FybmluZyIsInNldFNob3dXYXJuaW5nIiwiaGFzVW5zYXZlZENoYW5nZXMiLCJzZXRIYXNVbnNhdmVkQ2hhbmdlcyIsImlzQ29ubmVjdGVkIiwiaXNPbmxpbmUiLCJoYW5kbGVCZWZvcmVVbmxvYWQiLCJlIiwicHJldmVudERlZmF1bHQiLCJyZXR1cm5WYWx1ZSIsImhhbmRsZUtleURvd24iLCJjdHJsS2V5Iiwia2V5IiwibWV0YUtleSIsIndpbmRvdyIsImFkZEV2ZW50TGlzdGVuZXIiLCJyZW1vdmVFdmVudExpc3RlbmVyIiwidGltZXIiLCJzZXRUaW1lb3V0IiwiY2xlYXJUaW1lb3V0IiwiaGFuZGxlRm9ybUNoYW5nZSIsImhhbmRsZUZvcm1TdWJtaXQiLCJkb2N1bWVudCIsImhhbmRsZUZvcmNlUmVmcmVzaCIsImxvY2F0aW9uIiwicmVsb2FkIiwiaGFuZGxlRGlzbWlzcyIsImRpdiIsImNsYXNzTmFtZSIsImgzIiwidmFyaWFudCIsInNpemUiLCJvbkNsaWNrIiwicCIsInNwYW4iLCJ1c2VVbnNhdmVkQ2hhbmdlcyIsIm1hcmtBc0NoYW5nZWQiLCJtYXJrQXNTYXZlZCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/prevent-refresh.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/realtime-indicator.tsx":
/*!**************************************************!*\
  !*** ./src/components/ui/realtime-indicator.tsx ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RealtimeIndicator: () => (/* binding */ RealtimeIndicator),\n/* harmony export */   useRealtimeStatus: () => (/* binding */ useRealtimeStatus)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/auth */ \"(ssr)/./src/lib/auth.ts\");\n/* harmony import */ var _barrel_optimize_names_Activity_Wifi_WifiOff_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Wifi,WifiOff,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/wifi-off.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Wifi_WifiOff_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Wifi,WifiOff,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/wifi.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Wifi_WifiOff_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Wifi,WifiOff,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Wifi_WifiOff_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Wifi,WifiOff,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ RealtimeIndicator,useRealtimeStatus auto */ \n\n\n\n\nfunction RealtimeIndicator({ className, showText = false, size = 'sm' }) {\n    const [isConnected, setIsConnected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isOnline, setIsOnline] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [lastActivity, setLastActivity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const supabase = (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.createClientSupabaseClient)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"RealtimeIndicator.useEffect\": ()=>{\n            // Check initial online status\n            setIsOnline(navigator.onLine);\n            // Listen for online/offline events\n            const handleOnline = {\n                \"RealtimeIndicator.useEffect.handleOnline\": ()=>setIsOnline(true)\n            }[\"RealtimeIndicator.useEffect.handleOnline\"];\n            const handleOffline = {\n                \"RealtimeIndicator.useEffect.handleOffline\": ()=>setIsOnline(false)\n            }[\"RealtimeIndicator.useEffect.handleOffline\"];\n            window.addEventListener('online', handleOnline);\n            window.addEventListener('offline', handleOffline);\n            // Set up Supabase connection monitoring\n            const channel = supabase.channel('connection-monitor').on('presence', {\n                event: 'sync'\n            }, {\n                \"RealtimeIndicator.useEffect.channel\": ()=>{\n                    setIsConnected(true);\n                    setLastActivity(new Date());\n                }\n            }[\"RealtimeIndicator.useEffect.channel\"]).on('presence', {\n                event: 'join'\n            }, {\n                \"RealtimeIndicator.useEffect.channel\": ()=>{\n                    setIsConnected(true);\n                    setLastActivity(new Date());\n                }\n            }[\"RealtimeIndicator.useEffect.channel\"]).on('presence', {\n                event: 'leave'\n            }, {\n                \"RealtimeIndicator.useEffect.channel\": ()=>{\n                    setIsConnected(false);\n                }\n            }[\"RealtimeIndicator.useEffect.channel\"]).subscribe({\n                \"RealtimeIndicator.useEffect.channel\": (status)=>{\n                    if (status === 'SUBSCRIBED') {\n                        setIsConnected(true);\n                        setLastActivity(new Date());\n                    } else if (status === 'CHANNEL_ERROR' || status === 'TIMED_OUT') {\n                        setIsConnected(false);\n                    }\n                }\n            }[\"RealtimeIndicator.useEffect.channel\"]);\n            // Monitor real-time activity with a test channel\n            const activityChannel = supabase.channel('activity-monitor').on('broadcast', {\n                event: 'ping'\n            }, {\n                \"RealtimeIndicator.useEffect.activityChannel\": ()=>{\n                    setLastActivity(new Date());\n                }\n            }[\"RealtimeIndicator.useEffect.activityChannel\"]).subscribe();\n            // Send periodic pings to test connectivity\n            const pingInterval = setInterval({\n                \"RealtimeIndicator.useEffect.pingInterval\": ()=>{\n                    if (isOnline) {\n                        activityChannel.send({\n                            type: 'broadcast',\n                            event: 'ping',\n                            payload: {\n                                timestamp: new Date().toISOString()\n                            }\n                        });\n                    }\n                }\n            }[\"RealtimeIndicator.useEffect.pingInterval\"], 30000) // Ping every 30 seconds\n            ;\n            return ({\n                \"RealtimeIndicator.useEffect\": ()=>{\n                    window.removeEventListener('online', handleOnline);\n                    window.removeEventListener('offline', handleOffline);\n                    clearInterval(pingInterval);\n                    supabase.removeChannel(channel);\n                    supabase.removeChannel(activityChannel);\n                }\n            })[\"RealtimeIndicator.useEffect\"];\n        }\n    }[\"RealtimeIndicator.useEffect\"], [\n        supabase,\n        isOnline\n    ]);\n    const getStatusColor = ()=>{\n        if (!isOnline) return 'text-red-500';\n        if (!isConnected) return 'text-yellow-500';\n        return 'text-green-500';\n    };\n    const getStatusText = ()=>{\n        if (!isOnline) return 'Offline';\n        if (!isConnected) return 'Connecting...';\n        return 'Real-time';\n    };\n    const getIcon = ()=>{\n        if (!isOnline) return _barrel_optimize_names_Activity_Wifi_WifiOff_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"];\n        if (!isConnected) return _barrel_optimize_names_Activity_Wifi_WifiOff_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"];\n        return _barrel_optimize_names_Activity_Wifi_WifiOff_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"];\n    };\n    const getSizeClasses = ()=>{\n        switch(size){\n            case 'sm':\n                return 'w-3 h-3';\n            case 'md':\n                return 'w-4 h-4';\n            case 'lg':\n                return 'w-5 h-5';\n            default:\n                return 'w-3 h-3';\n        }\n    };\n    const Icon = getIcon();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)('flex items-center space-x-1', className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(getSizeClasses(), getStatusColor(), isConnected && isOnline && 'animate-pulse')\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/realtime-indicator.tsx\",\n                        lineNumber: 125,\n                        columnNumber: 9\n                    }, this),\n                    isConnected && isOnline && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)('absolute inset-0 rounded-full animate-ping', getSizeClasses(), 'bg-green-400 opacity-20')\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/realtime-indicator.tsx\",\n                        lineNumber: 133,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/realtime-indicator.tsx\",\n                lineNumber: 124,\n                columnNumber: 7\n            }, this),\n            showText && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)('text-xs font-medium', getStatusColor()),\n                children: getStatusText()\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/realtime-indicator.tsx\",\n                lineNumber: 142,\n                columnNumber: 9\n            }, this),\n            lastActivity && isConnected && isOnline && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-1\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Wifi_WifiOff_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        className: \"w-2 h-2 text-green-400\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/realtime-indicator.tsx\",\n                        lineNumber: 153,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-xs text-gray-500\",\n                        children: lastActivity.toLocaleTimeString([], {\n                            hour: '2-digit',\n                            minute: '2-digit'\n                        })\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/realtime-indicator.tsx\",\n                        lineNumber: 154,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/realtime-indicator.tsx\",\n                lineNumber: 152,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/realtime-indicator.tsx\",\n        lineNumber: 120,\n        columnNumber: 5\n    }, this);\n}\n// Hook to get real-time connection status\nfunction useRealtimeStatus() {\n    const [isConnected, setIsConnected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isOnline, setIsOnline] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [lastActivity, setLastActivity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const supabase = (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.createClientSupabaseClient)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"useRealtimeStatus.useEffect\": ()=>{\n            setIsOnline(navigator.onLine);\n            const handleOnline = {\n                \"useRealtimeStatus.useEffect.handleOnline\": ()=>setIsOnline(true)\n            }[\"useRealtimeStatus.useEffect.handleOnline\"];\n            const handleOffline = {\n                \"useRealtimeStatus.useEffect.handleOffline\": ()=>setIsOnline(false)\n            }[\"useRealtimeStatus.useEffect.handleOffline\"];\n            window.addEventListener('online', handleOnline);\n            window.addEventListener('offline', handleOffline);\n            const channel = supabase.channel('status-monitor').on('presence', {\n                event: 'sync'\n            }, {\n                \"useRealtimeStatus.useEffect.channel\": ()=>{\n                    setIsConnected(true);\n                    setLastActivity(new Date());\n                }\n            }[\"useRealtimeStatus.useEffect.channel\"]).subscribe({\n                \"useRealtimeStatus.useEffect.channel\": (status)=>{\n                    setIsConnected(status === 'SUBSCRIBED');\n                    if (status === 'SUBSCRIBED') {\n                        setLastActivity(new Date());\n                    }\n                }\n            }[\"useRealtimeStatus.useEffect.channel\"]);\n            return ({\n                \"useRealtimeStatus.useEffect\": ()=>{\n                    window.removeEventListener('online', handleOnline);\n                    window.removeEventListener('offline', handleOffline);\n                    supabase.removeChannel(channel);\n                }\n            })[\"useRealtimeStatus.useEffect\"];\n        }\n    }[\"useRealtimeStatus.useEffect\"], [\n        supabase\n    ]);\n    return {\n        isConnected,\n        isOnline,\n        lastActivity,\n        status: !isOnline ? 'offline' : !isConnected ? 'connecting' : 'connected'\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/realtime-indicator.tsx\n");

/***/ })

};
;