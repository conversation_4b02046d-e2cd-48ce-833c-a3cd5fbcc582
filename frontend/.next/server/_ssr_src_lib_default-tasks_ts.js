"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_src_lib_default-tasks_ts";
exports.ids = ["_ssr_src_lib_default-tasks_ts"];
exports.modules = {

/***/ "(ssr)/./src/lib/default-tasks.ts":
/*!**********************************!*\
  !*** ./src/lib/default-tasks.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DEFAULT_TASK_TEMPLATES: () => (/* binding */ DEFAULT_TASK_TEMPLATES),\n/* harmony export */   createTasksFromTemplates: () => (/* binding */ createTasksFromTemplates),\n/* harmony export */   getClientTypesWithDefaultTasks: () => (/* binding */ getClientTypesWithDefaultTasks),\n/* harmony export */   getDefaultTasksForClientType: () => (/* binding */ getDefaultTasksForClientType),\n/* harmony export */   getDefaultTasksForScheduleType: () => (/* binding */ getDefaultTasksForScheduleType)\n/* harmony export */ });\n// Default task templates for each schedule type (based on requirements/tasks.md)\nconst DEFAULT_TASK_TEMPLATES = {\n    // Wedding, Movie, Surveillance, Events, News, Collaboration\n    Wedding: [\n        {\n            title: 'Shoot',\n            description: 'Conduct the wedding shoot',\n            assigned_role: 'pilot',\n            priority: 'high',\n            order: 1,\n            isProjectTask: false\n        },\n        {\n            title: 'File Upload',\n            description: 'Upload captured files to storage',\n            assigned_role: 'pilot',\n            priority: 'medium',\n            order: 2,\n            dueDaysAfterShoot: 1,\n            isProjectTask: false\n        },\n        {\n            title: 'File Backup',\n            description: 'Create backup of all files',\n            assigned_role: 'pilot',\n            priority: 'medium',\n            order: 3,\n            dueDaysAfterShoot: 1,\n            isProjectTask: false\n        },\n        // Project-level tasks (created once per project)\n        {\n            title: 'Deliver Files',\n            description: 'Deliver final files to client',\n            assigned_role: 'admin',\n            priority: 'medium',\n            order: 100,\n            dueDaysAfterShoot: 7,\n            isProjectTask: true\n        },\n        {\n            title: 'Payment Collect',\n            description: 'Collect payment from client',\n            assigned_role: 'admin',\n            priority: 'high',\n            order: 101,\n            dueDaysAfterTask: 'Deliver Files',\n            dueDaysAfterShoot: 7,\n            isProjectTask: true\n        }\n    ],\n    Movie: [\n        {\n            title: 'Shoot',\n            description: 'Conduct the movie shoot',\n            assigned_role: 'pilot',\n            priority: 'high',\n            order: 1,\n            isProjectTask: false\n        },\n        {\n            title: 'File Upload',\n            description: 'Upload captured files to storage',\n            assigned_role: 'pilot',\n            priority: 'medium',\n            order: 2,\n            dueDaysAfterShoot: 1,\n            isProjectTask: false\n        },\n        {\n            title: 'File Backup',\n            description: 'Create backup of all files',\n            assigned_role: 'pilot',\n            priority: 'medium',\n            order: 3,\n            dueDaysAfterShoot: 1,\n            isProjectTask: false\n        },\n        // Project-level tasks (created once per project)\n        {\n            title: 'Deliver Files',\n            description: 'Deliver final files to client',\n            assigned_role: 'admin',\n            priority: 'medium',\n            order: 100,\n            dueDaysAfterShoot: 7,\n            isProjectTask: true\n        },\n        {\n            title: 'Payment Collect',\n            description: 'Collect payment from client',\n            assigned_role: 'admin',\n            priority: 'high',\n            order: 101,\n            dueDaysAfterTask: 'Deliver Files',\n            dueDaysAfterShoot: 7,\n            isProjectTask: true\n        }\n    ],\n    Surveillance: [\n        {\n            title: 'Shoot',\n            description: 'Conduct the surveillance shoot',\n            assigned_role: 'pilot',\n            priority: 'high',\n            order: 1,\n            isProjectTask: false\n        },\n        {\n            title: 'File Upload',\n            description: 'Upload captured files to storage',\n            assigned_role: 'pilot',\n            priority: 'medium',\n            order: 2,\n            dueDaysAfterShoot: 1,\n            isProjectTask: false\n        },\n        {\n            title: 'File Backup',\n            description: 'Create backup of all files',\n            assigned_role: 'pilot',\n            priority: 'medium',\n            order: 3,\n            dueDaysAfterShoot: 1,\n            isProjectTask: false\n        },\n        // Project-level tasks (created once per project)\n        {\n            title: 'Deliver Files',\n            description: 'Deliver final files to client',\n            assigned_role: 'admin',\n            priority: 'medium',\n            order: 100,\n            dueDaysAfterShoot: 7,\n            isProjectTask: true\n        },\n        {\n            title: 'Payment Collect',\n            description: 'Collect payment from client',\n            assigned_role: 'admin',\n            priority: 'high',\n            order: 101,\n            dueDaysAfterTask: 'Deliver Files',\n            dueDaysAfterShoot: 7,\n            isProjectTask: true\n        }\n    ],\n    Event: [\n        {\n            title: 'Shoot',\n            description: 'Conduct the event shoot',\n            assigned_role: 'pilot',\n            priority: 'high',\n            order: 1,\n            isProjectTask: false\n        },\n        {\n            title: 'File Upload',\n            description: 'Upload captured files to storage',\n            assigned_role: 'pilot',\n            priority: 'medium',\n            order: 2,\n            dueDaysAfterShoot: 1,\n            isProjectTask: false\n        },\n        {\n            title: 'File Backup',\n            description: 'Create backup of all files',\n            assigned_role: 'pilot',\n            priority: 'medium',\n            order: 3,\n            dueDaysAfterShoot: 1,\n            isProjectTask: false\n        },\n        // Project-level tasks (created once per project)\n        {\n            title: 'Deliver Files',\n            description: 'Deliver final files to client',\n            assigned_role: 'admin',\n            priority: 'medium',\n            order: 100,\n            dueDaysAfterShoot: 7,\n            isProjectTask: true\n        },\n        {\n            title: 'Payment Collect',\n            description: 'Collect payment from client',\n            assigned_role: 'admin',\n            priority: 'high',\n            order: 101,\n            dueDaysAfterTask: 'Deliver Files',\n            dueDaysAfterShoot: 7,\n            isProjectTask: true\n        }\n    ],\n    News: [\n        {\n            title: 'Shoot',\n            description: 'Conduct the news shoot',\n            assigned_role: 'pilot',\n            priority: 'high',\n            order: 1,\n            isProjectTask: false\n        },\n        {\n            title: 'File Upload',\n            description: 'Upload captured files to storage',\n            assigned_role: 'pilot',\n            priority: 'medium',\n            order: 2,\n            dueDaysAfterShoot: 1,\n            isProjectTask: false\n        },\n        {\n            title: 'File Backup',\n            description: 'Create backup of all files',\n            assigned_role: 'pilot',\n            priority: 'medium',\n            order: 3,\n            dueDaysAfterShoot: 1,\n            isProjectTask: false\n        },\n        // Project-level tasks (created once per project)\n        {\n            title: 'Deliver Files',\n            description: 'Deliver final files to client',\n            assigned_role: 'admin',\n            priority: 'medium',\n            order: 100,\n            dueDaysAfterShoot: 7,\n            isProjectTask: true\n        },\n        {\n            title: 'Payment Collect',\n            description: 'Collect payment from client',\n            assigned_role: 'admin',\n            priority: 'high',\n            order: 101,\n            dueDaysAfterTask: 'Deliver Files',\n            dueDaysAfterShoot: 7,\n            isProjectTask: true\n        }\n    ],\n    Collaboration: [\n        {\n            title: 'Shoot',\n            description: 'Conduct the collaboration shoot',\n            assigned_role: 'pilot',\n            priority: 'high',\n            order: 1,\n            isProjectTask: false\n        },\n        {\n            title: 'File Upload',\n            description: 'Upload captured files to storage',\n            assigned_role: 'pilot',\n            priority: 'medium',\n            order: 2,\n            dueDaysAfterShoot: 1,\n            isProjectTask: false\n        },\n        {\n            title: 'File Backup',\n            description: 'Create backup of all files',\n            assigned_role: 'pilot',\n            priority: 'medium',\n            order: 3,\n            dueDaysAfterShoot: 1,\n            isProjectTask: false\n        },\n        // Project-level tasks (created once per project)\n        {\n            title: 'Deliver Files',\n            description: 'Deliver final files to client',\n            assigned_role: 'admin',\n            priority: 'medium',\n            order: 100,\n            dueDaysAfterShoot: 7,\n            isProjectTask: true\n        },\n        {\n            title: 'Payment Collect',\n            description: 'Collect payment from client',\n            assigned_role: 'admin',\n            priority: 'high',\n            order: 101,\n            dueDaysAfterTask: 'Deliver Files',\n            dueDaysAfterShoot: 7,\n            isProjectTask: true\n        }\n    ],\n    // Corporate, Real Estate (Script Confirmation + Shoot + File Upload + File Backup + Edit)\n    Corporate: [\n        {\n            title: 'Script Confirmation',\n            description: 'Confirm script and requirements with client',\n            assigned_role: 'pilot',\n            priority: 'high',\n            order: 1,\n            isProjectTask: false\n        },\n        {\n            title: 'Shoot',\n            description: 'Conduct the corporate shoot',\n            assigned_role: 'pilot',\n            priority: 'high',\n            order: 2,\n            isProjectTask: false\n        },\n        {\n            title: 'File Upload',\n            description: 'Upload captured files to storage',\n            assigned_role: 'pilot',\n            priority: 'medium',\n            order: 3,\n            dueDaysAfterShoot: 1,\n            isProjectTask: false\n        },\n        {\n            title: 'File Backup',\n            description: 'Create backup of all files',\n            assigned_role: 'pilot',\n            priority: 'medium',\n            order: 4,\n            dueDaysAfterShoot: 1,\n            isProjectTask: false\n        },\n        {\n            title: 'Edit',\n            description: 'Edit and post-process the footage',\n            assigned_role: 'editor',\n            priority: 'high',\n            order: 5,\n            dueDaysAfterShoot: 7,\n            isProjectTask: false\n        },\n        // Project-level tasks (created once per project)\n        {\n            title: 'Deliver Files',\n            description: 'Deliver final files to client',\n            assigned_role: 'admin',\n            priority: 'medium',\n            order: 100,\n            dueDaysAfterShoot: 7,\n            isProjectTask: true\n        },\n        {\n            title: 'Payment Collect',\n            description: 'Collect payment from client',\n            assigned_role: 'admin',\n            priority: 'high',\n            order: 101,\n            dueDaysAfterTask: 'Deliver Files',\n            dueDaysAfterShoot: 7,\n            isProjectTask: true\n        }\n    ],\n    'Real Estate': [\n        {\n            title: 'Script Confirmation',\n            description: 'Confirm property details and requirements',\n            assigned_role: 'pilot',\n            priority: 'high',\n            order: 1,\n            isProjectTask: false\n        },\n        {\n            title: 'Shoot',\n            description: 'Conduct the real estate shoot',\n            assigned_role: 'pilot',\n            priority: 'high',\n            order: 2,\n            isProjectTask: false\n        },\n        {\n            title: 'File Upload',\n            description: 'Upload captured files to storage',\n            assigned_role: 'pilot',\n            priority: 'medium',\n            order: 3,\n            dueDaysAfterShoot: 1,\n            isProjectTask: false\n        },\n        {\n            title: 'File Backup',\n            description: 'Create backup of all files',\n            assigned_role: 'pilot',\n            priority: 'medium',\n            order: 4,\n            dueDaysAfterShoot: 1,\n            isProjectTask: false\n        },\n        {\n            title: 'Edit',\n            description: 'Edit and enhance the property footage',\n            assigned_role: 'editor',\n            priority: 'high',\n            order: 5,\n            dueDaysAfterShoot: 7,\n            isProjectTask: false\n        },\n        // Project-level tasks (created once per project)\n        {\n            title: 'Deliver Files',\n            description: 'Deliver final files to client',\n            assigned_role: 'admin',\n            priority: 'medium',\n            order: 100,\n            dueDaysAfterShoot: 7,\n            isProjectTask: true\n        },\n        {\n            title: 'Payment Collect',\n            description: 'Collect payment from client',\n            assigned_role: 'admin',\n            priority: 'high',\n            order: 101,\n            dueDaysAfterTask: 'Deliver Files',\n            dueDaysAfterShoot: 7,\n            isProjectTask: true\n        }\n    ],\n    // Government, NGO (Shoot + File Upload + File Backup + Edit)\n    Govt: [\n        {\n            title: 'Shoot',\n            description: 'Conduct the government project shoot',\n            assigned_role: 'pilot',\n            priority: 'high',\n            order: 1,\n            isProjectTask: false\n        },\n        {\n            title: 'File Upload',\n            description: 'Upload captured files to storage',\n            assigned_role: 'pilot',\n            priority: 'medium',\n            order: 2,\n            dueDaysAfterShoot: 1,\n            isProjectTask: false\n        },\n        {\n            title: 'File Backup',\n            description: 'Create backup of all files',\n            assigned_role: 'pilot',\n            priority: 'medium',\n            order: 3,\n            dueDaysAfterShoot: 1,\n            isProjectTask: false\n        },\n        {\n            title: 'Edit',\n            description: 'Edit and process the footage',\n            assigned_role: 'editor',\n            priority: 'medium',\n            order: 4,\n            dueDaysAfterShoot: 7,\n            isProjectTask: false\n        },\n        // Project-level tasks (created once per project)\n        {\n            title: 'Deliver Files',\n            description: 'Deliver final files to client',\n            assigned_role: 'admin',\n            priority: 'medium',\n            order: 100,\n            dueDaysAfterShoot: 7,\n            isProjectTask: true\n        },\n        {\n            title: 'Payment Collect',\n            description: 'Collect payment from client',\n            assigned_role: 'admin',\n            priority: 'high',\n            order: 101,\n            dueDaysAfterTask: 'Deliver Files',\n            dueDaysAfterShoot: 7,\n            isProjectTask: true\n        }\n    ],\n    NGO: [\n        {\n            title: 'Shoot',\n            description: 'Conduct the NGO project shoot',\n            assigned_role: 'pilot',\n            priority: 'high',\n            order: 1,\n            isProjectTask: false\n        },\n        {\n            title: 'File Upload',\n            description: 'Upload captured files to storage',\n            assigned_role: 'pilot',\n            priority: 'medium',\n            order: 2,\n            dueDaysAfterShoot: 1,\n            isProjectTask: false\n        },\n        {\n            title: 'File Backup',\n            description: 'Create backup of all files',\n            assigned_role: 'pilot',\n            priority: 'medium',\n            order: 3,\n            dueDaysAfterShoot: 1,\n            isProjectTask: false\n        },\n        {\n            title: 'Edit',\n            description: 'Edit and process the footage',\n            assigned_role: 'editor',\n            priority: 'medium',\n            order: 4,\n            dueDaysAfterShoot: 7,\n            isProjectTask: false\n        },\n        // Project-level tasks (created once per project)\n        {\n            title: 'Deliver Files',\n            description: 'Deliver final files to client',\n            assigned_role: 'admin',\n            priority: 'medium',\n            order: 100,\n            dueDaysAfterShoot: 7,\n            isProjectTask: true\n        },\n        {\n            title: 'Payment Collect',\n            description: 'Collect payment from client',\n            assigned_role: 'admin',\n            priority: 'high',\n            order: 101,\n            dueDaysAfterTask: 'Deliver Files',\n            dueDaysAfterShoot: 7,\n            isProjectTask: true\n        }\n    ],\n    // Survey (Plan Flight + Mark GCPs + Shoot + File Upload + File Backup + Post-Processing)\n    Survey: [\n        {\n            title: 'Plan Flight',\n            description: 'Plan the survey flight path and parameters',\n            assigned_role: 'pilot',\n            priority: 'high',\n            order: 1,\n            isProjectTask: false\n        },\n        {\n            title: 'Mark GCPs',\n            description: 'Mark Ground Control Points for survey accuracy',\n            assigned_role: 'pilot',\n            priority: 'high',\n            order: 2,\n            isProjectTask: false\n        },\n        {\n            title: 'Shoot',\n            description: 'Conduct the survey shoot',\n            assigned_role: 'pilot',\n            priority: 'high',\n            order: 3,\n            isProjectTask: false\n        },\n        {\n            title: 'File Upload',\n            description: 'Upload captured survey data to storage',\n            assigned_role: 'pilot',\n            priority: 'medium',\n            order: 4,\n            dueDaysAfterShoot: 1,\n            isProjectTask: false\n        },\n        {\n            title: 'File Backup',\n            description: 'Create backup of all survey data',\n            assigned_role: 'pilot',\n            priority: 'medium',\n            order: 5,\n            dueDaysAfterShoot: 1,\n            isProjectTask: false\n        },\n        {\n            title: 'Post-Processing',\n            description: 'Process survey data and generate maps/models',\n            assigned_role: 'pilot',\n            priority: 'high',\n            order: 6,\n            dueDaysAfterShoot: 7,\n            isProjectTask: false\n        },\n        // Project-level tasks (created once per project)\n        {\n            title: 'Deliver Files',\n            description: 'Deliver processed survey results to client',\n            assigned_role: 'admin',\n            priority: 'medium',\n            order: 100,\n            dueDaysAfterShoot: 7,\n            isProjectTask: true\n        },\n        {\n            title: 'Payment Collect',\n            description: 'Collect payment from client',\n            assigned_role: 'admin',\n            priority: 'high',\n            order: 101,\n            dueDaysAfterTask: 'Deliver Files',\n            dueDaysAfterShoot: 7,\n            isProjectTask: true\n        }\n    ]\n};\n/**\n * Get default tasks for a specific client type (for project-level tasks)\n */ function getDefaultTasksForClientType(clientType) {\n    return DEFAULT_TASK_TEMPLATES[clientType] || [];\n}\n/**\n * Get default tasks for a specific schedule type (for schedule-based tasks)\n */ function getDefaultTasksForScheduleType(scheduleType) {\n    return DEFAULT_TASK_TEMPLATES[scheduleType] || [];\n}\n/**\n * Create task forms from templates for a specific project\n */ function createTasksFromTemplates(templates, projectId, usersByRole, shootDate, shootId// Optional shoot ID for shoot-based tasks\n) {\n    const sortedTemplates = templates.sort((a, b)=>a.order - b.order);\n    return sortedTemplates.map((template)=>{\n        let dueDate;\n        // Special case: Shoot task should have due date as the schedule start time\n        if (shootDate && template.title === 'Shoot') {\n            dueDate = shootDate; // Use the full schedule date/time\n        } else if (shootDate && template.dueDaysAfterShoot) {\n            const shootDateTime = new Date(shootDate);\n            const dueDateObj = new Date(shootDateTime);\n            dueDateObj.setDate(dueDateObj.getDate() + template.dueDaysAfterShoot);\n            dueDate = dueDateObj.toISOString().split('T')[0]; // Format as YYYY-MM-DD\n        }\n        // Handle tasks that depend on other tasks (like Payment Collect after Deliver Files)\n        if (shootDate && template.dueDaysAfterTask && template.dueDaysAfterShoot) {\n            const shootDateTime = new Date(shootDate);\n            const dueDateObj = new Date(shootDateTime);\n            dueDateObj.setDate(dueDateObj.getDate() + template.dueDaysAfterShoot);\n            dueDate = dueDateObj.toISOString().split('T')[0];\n        }\n        return {\n            title: template.title,\n            description: template.description,\n            status: 'pending',\n            priority: template.priority,\n            assigned_to: usersByRole[template.assigned_role] || '',\n            assigned_role: template.assigned_role,\n            project_id: projectId,\n            shoot_id: template.isProjectTask ? undefined : shootId,\n            due_date: dueDate,\n            order: template.order\n        };\n    });\n}\n/**\n * Get all available client types that have default tasks\n */ function getClientTypesWithDefaultTasks() {\n    return Object.keys(DEFAULT_TASK_TEMPLATES);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/default-tasks.ts\n");

/***/ })

};
;