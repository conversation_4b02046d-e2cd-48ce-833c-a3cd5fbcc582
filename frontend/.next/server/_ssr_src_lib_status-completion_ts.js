"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_src_lib_status-completion_ts";
exports.ids = ["_ssr_src_lib_status-completion_ts"];
exports.modules = {

/***/ "(ssr)/./src/lib/status-completion.ts":
/*!**************************************!*\
  !*** ./src/lib/status-completion.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkAndCompleteProject: () => (/* binding */ checkAndCompleteProject),\n/* harmony export */   checkAndCompleteShoot: () => (/* binding */ checkAndCompleteShoot),\n/* harmony export */   handleTaskStatusChange: () => (/* binding */ handleTaskStatusChange)\n/* harmony export */ });\n/* harmony import */ var _auth__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./auth */ \"(ssr)/./src/lib/auth.ts\");\n\nconst supabase = (0,_auth__WEBPACK_IMPORTED_MODULE_0__.createClientSupabaseClient)();\n/**\n * Check if all shoot-based tasks for a specific shoot are completed\n * and automatically complete the shoot if they are\n */ async function checkAndCompleteShoot(shootId) {\n    try {\n        console.log('Checking shoot completion for shoot:', shootId);\n        // Get the shoot details\n        const { data: shoot, error: shootError } = await supabase.from('shoots').select('id, status, project_id').eq('id', shootId).single();\n        if (shootError || !shoot) {\n            console.error('Error fetching shoot:', shootError);\n            return;\n        }\n        // Skip if shoot is already completed\n        if (shoot.status === 'completed') {\n            console.log('Shoot is already completed');\n            return;\n        }\n        // Get all shoot-based tasks for this shoot\n        const { data: shootTasks, error: tasksError } = await supabase.from('tasks').select('id, title, status').eq('shoot_id', shootId).neq('status', 'cancelled') // Exclude cancelled tasks\n        ;\n        if (tasksError) {\n            console.error('Error fetching shoot tasks:', tasksError);\n            return;\n        }\n        if (!shootTasks || shootTasks.length === 0) {\n            console.log('No shoot-based tasks found for shoot:', shootId);\n            return;\n        }\n        // Check if all shoot-based tasks are completed\n        const allCompleted = shootTasks.every((task)=>task.status === 'completed');\n        console.log('Shoot tasks status:', shootTasks.map((t)=>({\n                title: t.title,\n                status: t.status\n            })));\n        console.log('All shoot tasks completed:', allCompleted);\n        if (allCompleted) {\n            // Automatically complete the shoot\n            const { error: updateError } = await supabase.from('shoots').update({\n                status: 'completed',\n                actual_date: new Date().toISOString()\n            }).eq('id', shootId);\n            if (updateError) {\n                console.error('Error updating shoot status:', updateError);\n                return;\n            }\n            console.log('Shoot automatically completed:', shootId);\n            // Now check if the project should be completed\n            await checkAndCompleteProject(shoot.project_id);\n        }\n    } catch (error) {\n        console.error('Error in checkAndCompleteShoot:', error);\n    }\n}\n/**\n * Check if all shoots and project-level tasks for a project are completed\n * and automatically complete the project if they are\n */ async function checkAndCompleteProject(projectId) {\n    try {\n        console.log('Checking project completion for project:', projectId);\n        // Get the project details\n        const { data: project, error: projectError } = await supabase.from('projects').select('id, status').eq('id', projectId).single();\n        if (projectError || !project) {\n            console.error('Error fetching project:', projectError);\n            return;\n        }\n        // Skip if project is already completed\n        if (project.status === 'completed') {\n            console.log('Project is already completed');\n            return;\n        }\n        // Get all shoots for this project\n        const { data: shoots, error: shootsError } = await supabase.from('shoots').select('id, status').eq('project_id', projectId).neq('status', 'cancelled') // Exclude cancelled shoots\n        ;\n        if (shootsError) {\n            console.error('Error fetching project shoots:', shootsError);\n            return;\n        }\n        // Check if all shoots are completed\n        const allShootsCompleted = shoots?.every((shoot)=>shoot.status === 'completed') ?? true;\n        console.log('Project shoots status:', shoots?.map((s)=>({\n                id: s.id,\n                status: s.status\n            })));\n        console.log('All shoots completed:', allShootsCompleted);\n        if (!allShootsCompleted) {\n            console.log('Not all shoots are completed yet');\n            return;\n        }\n        // Get all project-level tasks (tasks without shoot_id)\n        const { data: projectTasks, error: projectTasksError } = await supabase.from('tasks').select('id, title, status').eq('project_id', projectId).is('shoot_id', null) // Project-level tasks have no shoot_id\n        .neq('status', 'cancelled') // Exclude cancelled tasks\n        ;\n        if (projectTasksError) {\n            console.error('Error fetching project tasks:', projectTasksError);\n            return;\n        }\n        // Check if all project-level tasks are completed\n        const allProjectTasksCompleted = projectTasks?.every((task)=>task.status === 'completed') ?? true;\n        console.log('Project tasks status:', projectTasks?.map((t)=>({\n                title: t.title,\n                status: t.status\n            })));\n        console.log('All project tasks completed:', allProjectTasksCompleted);\n        if (allShootsCompleted && allProjectTasksCompleted) {\n            // Automatically complete the project\n            const { error: updateError } = await supabase.from('projects').update({\n                status: 'completed'\n            }).eq('id', projectId);\n            if (updateError) {\n                console.error('Error updating project status:', updateError);\n                return;\n            }\n            console.log('Project automatically completed:', projectId);\n        }\n    } catch (error) {\n        console.error('Error in checkAndCompleteProject:', error);\n    }\n}\n/**\n * Handle task status change and trigger automatic completion checks\n */ async function handleTaskStatusChange(task, newStatus) {\n    try {\n        console.log('Handling task status change:', {\n            taskId: task.id,\n            title: task.title,\n            newStatus\n        });\n        // If task is completed, check for automatic completion\n        if (newStatus === 'completed') {\n            // If it's a shoot-based task, check shoot completion\n            if (task.shoot_id) {\n                await checkAndCompleteShoot(task.shoot_id);\n            } else if (task.project_id) {\n                await checkAndCompleteProject(task.project_id);\n            }\n        }\n    } catch (error) {\n        console.error('Error in handleTaskStatusChange:', error);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/status-completion.ts\n");

/***/ })

};
;