"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_rsc_src_lib_background-jobs_ts";
exports.ids = ["_rsc_src_lib_background-jobs_ts"];
exports.modules = {

/***/ "(rsc)/./src/lib/background-jobs.ts":
/*!************************************!*\
  !*** ./src/lib/background-jobs.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getAllJobs: () => (/* binding */ getAllJobs),\n/* harmony export */   getBackgroundJobQueue: () => (/* binding */ getBackgroundJobQueue),\n/* harmony export */   getJobStatus: () => (/* binding */ getJobStatus),\n/* harmony export */   queueBackgroundJob: () => (/* binding */ queueBackgroundJob),\n/* harmony export */   queueSharePointFolderCreation: () => (/* binding */ queueSharePointFolderCreation)\n/* harmony export */ });\n/**\n * Background job system for handling asynchronous tasks like SharePoint folder creation\n */ class BackgroundJobQueue {\n    constructor(){\n        this.jobs = new Map();\n        this.isProcessing = false;\n        this.processingInterval = null;\n        console.log('🏗️ BackgroundJobQueue constructor called');\n        // Start processing jobs every 2 seconds\n        this.startProcessing();\n        console.log('✅ BackgroundJobQueue initialized and processing started');\n    }\n    /**\n   * Add a new job to the queue\n   */ addJob(type, entityType, entityId, payload) {\n        const jobId = `${type}_${entityType}_${entityId}_${Date.now()}`;\n        const job = {\n            id: jobId,\n            type,\n            entityType,\n            entityId,\n            payload,\n            status: 'pending',\n            attempts: 0,\n            maxAttempts: 3,\n            createdAt: new Date(),\n            updatedAt: new Date()\n        };\n        this.jobs.set(jobId, job);\n        console.log(`📋 Background job added: ${jobId} (${type} for ${entityType}:${entityId})`);\n        console.log(`📊 Total jobs in queue: ${this.jobs.size}`);\n        return jobId;\n    }\n    /**\n   * Start processing jobs in the background\n   */ startProcessing() {\n        if (this.processingInterval) return;\n        console.log('🚀 Starting background job processing...');\n        // Re-enable background job processing with optimized interval\n        this.processingInterval = setInterval(async ()=>{\n            if (this.isProcessing) return;\n            await this.processNextJob();\n        }, 10000); // Process every 10 seconds to reduce CPU load\n    }\n    /**\n   * Stop processing jobs\n   */ stopProcessing() {\n        if (this.processingInterval) {\n            clearInterval(this.processingInterval);\n            this.processingInterval = null;\n        }\n    }\n    /**\n   * Process the next pending job\n   */ async processNextJob() {\n        const pendingJob = Array.from(this.jobs.values()).find((job)=>job.status === 'pending' && job.attempts < job.maxAttempts);\n        if (!pendingJob) return;\n        this.isProcessing = true;\n        pendingJob.status = 'processing';\n        pendingJob.attempts++;\n        pendingJob.updatedAt = new Date();\n        console.log(`🔄 Processing background job: ${pendingJob.id} (attempt ${pendingJob.attempts}/${pendingJob.maxAttempts})`);\n        try {\n            await this.executeJob(pendingJob);\n            pendingJob.status = 'completed';\n            pendingJob.updatedAt = new Date();\n            console.log(`✅ Background job completed: ${pendingJob.id}`);\n        } catch (error) {\n            console.error(`❌ Background job failed: ${pendingJob.id}`, error);\n            pendingJob.error = error instanceof Error ? error.message : 'Unknown error';\n            pendingJob.updatedAt = new Date();\n            if (pendingJob.attempts >= pendingJob.maxAttempts) {\n                pendingJob.status = 'failed';\n                console.error(`💀 Background job permanently failed after ${pendingJob.maxAttempts} attempts: ${pendingJob.id}`);\n            } else {\n                pendingJob.status = 'pending'; // Retry\n                console.log(`🔄 Background job will retry: ${pendingJob.id}`);\n            }\n        } finally{\n            this.isProcessing = false;\n        }\n    }\n    /**\n   * Execute a specific job based on its type\n   */ async executeJob(job) {\n        switch(job.type){\n            case 'sharepoint_folder_creation':\n                await this.executeSharePointFolderCreation(job);\n                break;\n            case 'heatmap_automation':\n                await this.executeHeatmapAutomation(job);\n                break;\n            case 'notification_triggers':\n                await this.executeNotificationTriggers(job);\n                break;\n            default:\n                throw new Error(`Unknown job type: ${job.type}`);\n        }\n    }\n    /**\n   * Execute SharePoint folder creation job\n   */ async executeSharePointFolderCreation(job) {\n        const { entityType, entityId, payload } = job;\n        switch(entityType){\n            case 'client':\n                await this.createClientFolder(entityId, payload);\n                break;\n            case 'project':\n                await this.createProjectFolder(entityId, payload);\n                break;\n            case 'schedule':\n                await this.createScheduleFolder(entityId, payload);\n                break;\n            default:\n                throw new Error(`Unknown entity type: ${entityType}`);\n        }\n    }\n    /**\n   * Create SharePoint folder for client\n   */ async createClientFolder(clientId, payload) {\n        const { createClientFolder } = await __webpack_require__.e(/*! import() */ \"_rsc_src_lib_microsoft-graph_ts\").then(__webpack_require__.bind(__webpack_require__, /*! @/lib/microsoft-graph */ \"(rsc)/./src/lib/microsoft-graph.ts\"));\n        await createClientFolder(clientId, payload.customId, payload.name);\n    }\n    /**\n   * Create SharePoint folder for project\n   */ async createProjectFolder(projectId, payload) {\n        const { createProjectFolder } = await __webpack_require__.e(/*! import() */ \"_rsc_src_lib_microsoft-graph_ts\").then(__webpack_require__.bind(__webpack_require__, /*! @/lib/microsoft-graph */ \"(rsc)/./src/lib/microsoft-graph.ts\"));\n        await createProjectFolder(projectId, payload.customId, payload.name, payload.clientId);\n    }\n    /**\n   * Create SharePoint folder for schedule\n   */ async createScheduleFolder(scheduleId, payload) {\n        console.log(`⚡️ Dynamically importing SharePointService for schedule: ${scheduleId}`);\n        const { SharePointService } = await __webpack_require__.e(/*! import() */ \"_rsc_src_lib_sharepoint-service_ts\").then(__webpack_require__.bind(__webpack_require__, /*! @/lib/sharepoint-service */ \"(rsc)/./src/lib/sharepoint-service.ts\"));\n        console.log(`📞 Calling SharePointService.ensureScheduleFolder for schedule: ${scheduleId}`);\n        const result = await SharePointService.ensureScheduleFolder(scheduleId);\n        console.log(`✔️ SharePointService.ensureScheduleFolder result for schedule ${scheduleId}:`, result);\n    }\n    /**\n   * Execute heatmap automation job\n   */ async executeHeatmapAutomation(job) {\n        const { entityId } = job;\n        console.log(`🗺️ Executing heatmap automation for schedule: ${entityId}`);\n        const { processHeatmapAutomation } = await __webpack_require__.e(/*! import() */ \"_rsc_src_lib_heatmap-automation_ts\").then(__webpack_require__.bind(__webpack_require__, /*! @/lib/heatmap-automation */ \"(rsc)/./src/lib/heatmap-automation.ts\"));\n        const result = await processHeatmapAutomation(entityId);\n        if (!result.success) {\n            throw new Error(result.message);\n        }\n        console.log(`✅ Heatmap automation completed for schedule: ${entityId}`);\n    }\n    /**\n   * Execute notification triggers job\n   */ async executeNotificationTriggers(job) {\n        const { payload } = job;\n        console.log(`🔔 Executing notification triggers: ${payload.triggerType}`);\n        const { processNotificationTriggers } = await __webpack_require__.e(/*! import() */ \"_rsc_src_lib_notification-triggers_ts\").then(__webpack_require__.bind(__webpack_require__, /*! @/lib/notification-triggers */ \"(rsc)/./src/lib/notification-triggers.ts\"));\n        const result = await processNotificationTriggers(payload.triggerType, payload.options);\n        if (!result.success) {\n            throw new Error(result.message);\n        }\n        console.log(`✅ Notification triggers completed: ${payload.triggerType}, created ${result.count} notifications`);\n    }\n    /**\n   * Get job status\n   */ getJobStatus(jobId) {\n        return this.jobs.get(jobId);\n    }\n    /**\n   * Get all jobs for debugging\n   */ getAllJobs() {\n        return Array.from(this.jobs.values());\n    }\n    /**\n   * Clean up completed and failed jobs older than 1 hour\n   */ cleanup() {\n        const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);\n        for (const [jobId, job] of this.jobs.entries()){\n            if ((job.status === 'completed' || job.status === 'failed') && job.updatedAt < oneHourAgo) {\n                this.jobs.delete(jobId);\n                console.log(`🧹 Cleaned up old job: ${jobId}`);\n            }\n        }\n    }\n}\n// Global instance\nlet backgroundJobQueue = null;\n/**\n * Get the global background job queue instance\n */ function getBackgroundJobQueue() {\n    if (!backgroundJobQueue) {\n        console.log('🚀 Creating new BackgroundJobQueue instance');\n        backgroundJobQueue = new BackgroundJobQueue();\n        // Clean up old jobs every 30 minutes\n        setInterval(()=>{\n            backgroundJobQueue?.cleanup();\n        }, 30 * 60 * 1000);\n        console.log('🎯 BackgroundJobQueue instance created and cleanup scheduled');\n    }\n    return backgroundJobQueue;\n}\n/**\n * Add a SharePoint folder creation job to the background queue\n * @param entityType - Type of entity (client, project, schedule)\n * @param entityId - ID of the entity\n * @param payload - Job payload\n * @param options - Additional options\n * @returns Job ID or 'sync' if executed synchronously\n */ async function queueSharePointFolderCreation(entityType, entityId, payload, options = {}) {\n    const { executeSync = false, fallbackToSync = true } = options;\n    // If sync execution is requested, execute immediately\n    if (executeSync) {\n        console.log(`🔄 Executing SharePoint folder creation synchronously for ${entityType}:${entityId}`);\n        try {\n            await executeSharePointFolderCreationDirect(entityType, entityId, payload);\n            console.log(`✅ Synchronous SharePoint folder creation completed for ${entityType}:${entityId}`);\n            return 'sync';\n        } catch (error) {\n            console.error(`❌ Synchronous SharePoint folder creation failed for ${entityType}:${entityId}:`, error);\n            throw error;\n        }\n    }\n    // Try to queue the job\n    try {\n        const queue = getBackgroundJobQueue();\n        const jobId = queue.addJob('sharepoint_folder_creation', entityType, entityId, payload);\n        console.log(`📋 SharePoint folder creation queued for ${entityType}:${entityId} with job ID: ${jobId}`);\n        return jobId;\n    } catch (queueError) {\n        console.error(`❌ Failed to queue SharePoint folder creation for ${entityType}:${entityId}:`, queueError);\n        // Fall back to synchronous execution if queuing fails and fallback is enabled\n        if (fallbackToSync) {\n            console.log(`🔄 Falling back to synchronous execution for ${entityType}:${entityId}`);\n            try {\n                await executeSharePointFolderCreationDirect(entityType, entityId, payload);\n                console.log(`✅ Fallback synchronous SharePoint folder creation completed for ${entityType}:${entityId}`);\n                return 'sync-fallback';\n            } catch (syncError) {\n                console.error(`❌ Fallback synchronous SharePoint folder creation failed for ${entityType}:${entityId}:`, syncError);\n                throw syncError;\n            }\n        }\n        throw queueError;\n    }\n}\n/**\n * Execute SharePoint folder creation directly (synchronously)\n */ async function executeSharePointFolderCreationDirect(entityType, entityId, payload) {\n    switch(entityType){\n        case 'client':\n            const { createClientFolder } = await __webpack_require__.e(/*! import() */ \"_rsc_src_lib_microsoft-graph_ts\").then(__webpack_require__.bind(__webpack_require__, /*! @/lib/microsoft-graph */ \"(rsc)/./src/lib/microsoft-graph.ts\"));\n            await createClientFolder(entityId, payload.customId, payload.name);\n            break;\n        case 'project':\n            const { createProjectFolder } = await __webpack_require__.e(/*! import() */ \"_rsc_src_lib_microsoft-graph_ts\").then(__webpack_require__.bind(__webpack_require__, /*! @/lib/microsoft-graph */ \"(rsc)/./src/lib/microsoft-graph.ts\"));\n            await createProjectFolder(entityId, payload.customId, payload.name, payload.clientId);\n            break;\n        case 'schedule':\n            const { SharePointService } = await __webpack_require__.e(/*! import() */ \"_rsc_src_lib_sharepoint-service_ts\").then(__webpack_require__.bind(__webpack_require__, /*! @/lib/sharepoint-service */ \"(rsc)/./src/lib/sharepoint-service.ts\"));\n            await SharePointService.ensureScheduleFolder(entityId);\n            break;\n        default:\n            throw new Error(`Unknown entity type: ${entityType}`);\n    }\n}\n/**\n * Get the status of a background job\n */ function getJobStatus(jobId) {\n    const queue = getBackgroundJobQueue();\n    return queue.getJobStatus(jobId);\n}\n/**\n * Get all background jobs (for debugging)\n */ function getAllJobs() {\n    const queue = getBackgroundJobQueue();\n    return queue.getAllJobs();\n}\n/**\n * Queue a heatmap automation job for a schedule\n * @param scheduleId - ID of the schedule to process\n * @returns Job ID\n */ function queueBackgroundJob(type, entityType, entityId, payload) {\n    const queue = getBackgroundJobQueue();\n    const jobId = queue.addJob(type, entityType, entityId, payload);\n    console.log(`📋 Heatmap automation queued for schedule:${entityId} with job ID: ${jobId}`);\n    return jobId;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/background-jobs.ts\n");

/***/ })

};
;