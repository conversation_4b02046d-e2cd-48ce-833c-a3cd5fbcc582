"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_src_components_ui_notification-bell_tsx";
exports.ids = ["_ssr_src_components_ui_notification-bell_tsx"];
exports.modules = {

/***/ "(ssr)/./src/components/ui/notification-bell.tsx":
/*!*************************************************!*\
  !*** ./src/components/ui/notification-bell.tsx ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NotificationBell: () => (/* binding */ NotificationBell)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _barrel_optimize_names_Bell_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Bell!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _hooks_useNotifications__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/hooks/useNotifications */ \"(ssr)/./src/hooks/useNotifications.ts\");\n/* __next_internal_client_entry_do_not_use__ NotificationBell auto */ \n\n\nfunction NotificationBell() {\n    const { stats, loading, error } = (0,_hooks_useNotifications__WEBPACK_IMPORTED_MODULE_1__.useNotifications)({\n        read: false,\n        limit: 5\n    });\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"relative p-2 rounded-full hover:bg-muted transition-colors\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                className: \"w-5 h-5 text-muted-foreground\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/notification-bell.tsx\",\n                lineNumber: 19,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/notification-bell.tsx\",\n            lineNumber: 18,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"relative p-2 rounded-full hover:bg-muted transition-colors\",\n            title: error,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                className: \"w-5 h-5 text-red-500\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/notification-bell.tsx\",\n                lineNumber: 27,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/notification-bell.tsx\",\n            lineNumber: 26,\n            columnNumber: 7\n        }, this);\n    }\n    const unreadCount = stats?.unread || 0;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative p-2 rounded-full hover:bg-muted transition-colors cursor-pointer\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                className: \"w-5 h-5 text-muted-foreground\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/notification-bell.tsx\",\n                lineNumber: 36,\n                columnNumber: 7\n            }, this),\n            unreadCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center\",\n                children: unreadCount > 99 ? '99+' : unreadCount\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/notification-bell.tsx\",\n                lineNumber: 38,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/notification-bell.tsx\",\n        lineNumber: 35,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/notification-bell.tsx\n");

/***/ }),

/***/ "(ssr)/./src/hooks/useNotifications.ts":
/*!***************************************!*\
  !*** ./src/hooks/useNotifications.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useNotifications: () => (/* binding */ useNotifications)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\nfunction useNotifications(filters) {\n    const [notifications, setNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    console.log('🔔 useNotifications hook called with filters:', filters);\n    // Fetch notifications and stats\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useNotifications.useEffect\": ()=>{\n            console.log('🔔 useNotifications effect running');\n            const fetchNotifications = {\n                \"useNotifications.useEffect.fetchNotifications\": async ()=>{\n                    try {\n                        setLoading(true);\n                        setError(null);\n                        // Provide fallback data instead of making API calls\n                        console.warn('🔔 Notifications API disabled, using empty data');\n                        setNotifications([]);\n                        setStats({\n                            total: 0,\n                            unread: 0,\n                            by_type: {\n                                payment: 0,\n                                schedule: 0,\n                                task: 0,\n                                info: 0,\n                                success: 0,\n                                warning: 0,\n                                error: 0,\n                                project: 0,\n                                system: 0\n                            },\n                            by_category: {\n                                task_assigned: 0,\n                                task_due: 0,\n                                task_overdue: 0,\n                                task_completed: 0,\n                                project_created: 0,\n                                project_updated: 0,\n                                schedule_upcoming: 0,\n                                schedule_changed: 0,\n                                payment_received: 0,\n                                payment_overdue: 0,\n                                system_update: 0,\n                                user_mention: 0,\n                                deadline_reminder: 0,\n                                general: 0\n                            },\n                            by_priority: {\n                                low: 0,\n                                medium: 0,\n                                high: 0,\n                                urgent: 0\n                            }\n                        });\n                    } catch (err) {\n                        console.error('Error in useNotifications:', err);\n                        setError(err instanceof Error ? err.message : 'Failed to fetch notifications');\n                        // Ensure we still set empty data even if there's an error\n                        setNotifications([]);\n                        setStats({\n                            total: 0,\n                            unread: 0,\n                            by_type: {\n                                payment: 0,\n                                schedule: 0,\n                                task: 0,\n                                info: 0,\n                                success: 0,\n                                warning: 0,\n                                error: 0,\n                                project: 0,\n                                system: 0\n                            },\n                            by_category: {\n                                task_assigned: 0,\n                                task_due: 0,\n                                task_overdue: 0,\n                                task_completed: 0,\n                                project_created: 0,\n                                project_updated: 0,\n                                schedule_upcoming: 0,\n                                schedule_changed: 0,\n                                payment_received: 0,\n                                payment_overdue: 0,\n                                system_update: 0,\n                                user_mention: 0,\n                                deadline_reminder: 0,\n                                general: 0\n                            },\n                            by_priority: {\n                                low: 0,\n                                medium: 0,\n                                high: 0,\n                                urgent: 0\n                            }\n                        });\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"useNotifications.useEffect.fetchNotifications\"];\n            fetchNotifications();\n        }\n    }[\"useNotifications.useEffect\"], [\n        filters?.read,\n        filters?.type,\n        filters?.category,\n        filters?.priority,\n        filters?.limit,\n        filters?.offset\n    ]);\n    // Return minimal interface\n    return {\n        notifications,\n        stats,\n        loading,\n        error,\n        refetch: ()=>Promise.resolve(),\n        markAsRead: ()=>Promise.resolve(),\n        markAsUnread: ()=>Promise.resolve(),\n        markAllAsRead: ()=>Promise.resolve(),\n        deleteNotification: ()=>Promise.resolve(),\n        createNotification: ()=>Promise.resolve({})\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvaG9va3MvdXNlTm90aWZpY2F0aW9ucy50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBMkM7QUFRcEMsU0FBU0UsaUJBQWlCQyxPQUE2QjtJQUM1RCxNQUFNLENBQUNDLGVBQWVDLGlCQUFpQixHQUFHTCwrQ0FBUUEsQ0FBaUIsRUFBRTtJQUNyRSxNQUFNLENBQUNNLE9BQU9DLFNBQVMsR0FBR1AsK0NBQVFBLENBQTJCO0lBQzdELE1BQU0sQ0FBQ1EsU0FBU0MsV0FBVyxHQUFHVCwrQ0FBUUEsQ0FBQztJQUN2QyxNQUFNLENBQUNVLE9BQU9DLFNBQVMsR0FBR1gsK0NBQVFBLENBQWdCO0lBRWxEWSxRQUFRQyxHQUFHLENBQUMsaURBQWlEVjtJQUU3RCxnQ0FBZ0M7SUFDaENGLGdEQUFTQTtzQ0FBQztZQUNSVyxRQUFRQyxHQUFHLENBQUM7WUFFWixNQUFNQztpRUFBcUI7b0JBQ3pCLElBQUk7d0JBQ0ZMLFdBQVc7d0JBQ1hFLFNBQVM7d0JBRVQsb0RBQW9EO3dCQUNwREMsUUFBUUcsSUFBSSxDQUFDO3dCQUNiVixpQkFBaUIsRUFBRTt3QkFDbkJFLFNBQVM7NEJBQ1BTLE9BQU87NEJBQ1BDLFFBQVE7NEJBQ1JDLFNBQVM7Z0NBQ1BDLFNBQVM7Z0NBQ1RDLFVBQVU7Z0NBQ1ZDLE1BQU07Z0NBQ05DLE1BQU07Z0NBQ05DLFNBQVM7Z0NBQ1RDLFNBQVM7Z0NBQ1RkLE9BQU87Z0NBQ1BlLFNBQVM7Z0NBQ1RDLFFBQVE7NEJBQ1Y7NEJBQ0FDLGFBQWE7Z0NBQ1hDLGVBQWU7Z0NBQ2ZDLFVBQVU7Z0NBQ1ZDLGNBQWM7Z0NBQ2RDLGdCQUFnQjtnQ0FDaEJDLGlCQUFpQjtnQ0FDakJDLGlCQUFpQjtnQ0FDakJDLG1CQUFtQjtnQ0FDbkJDLGtCQUFrQjtnQ0FDbEJDLGtCQUFrQjtnQ0FDbEJDLGlCQUFpQjtnQ0FDakJDLGVBQWU7Z0NBQ2ZDLGNBQWM7Z0NBQ2RDLG1CQUFtQjtnQ0FDbkJDLFNBQVM7NEJBQ1g7NEJBQ0FDLGFBQWE7Z0NBQ1hDLEtBQUs7Z0NBQ0xDLFFBQVE7Z0NBQ1JDLE1BQU07Z0NBQ05DLFFBQVE7NEJBQ1Y7d0JBQ0Y7b0JBQ0YsRUFBRSxPQUFPQyxLQUFLO3dCQUNabkMsUUFBUUYsS0FBSyxDQUFDLDhCQUE4QnFDO3dCQUM1Q3BDLFNBQVNvQyxlQUFlQyxRQUFRRCxJQUFJRSxPQUFPLEdBQUc7d0JBQzlDLDBEQUEwRDt3QkFDMUQ1QyxpQkFBaUIsRUFBRTt3QkFDbkJFLFNBQVM7NEJBQ1BTLE9BQU87NEJBQ1BDLFFBQVE7NEJBQ1JDLFNBQVM7Z0NBQ1BDLFNBQVM7Z0NBQ1RDLFVBQVU7Z0NBQ1ZDLE1BQU07Z0NBQ05DLE1BQU07Z0NBQ05DLFNBQVM7Z0NBQ1RDLFNBQVM7Z0NBQ1RkLE9BQU87Z0NBQ1BlLFNBQVM7Z0NBQ1RDLFFBQVE7NEJBQ1Y7NEJBQ0FDLGFBQWE7Z0NBQ1hDLGVBQWU7Z0NBQ2ZDLFVBQVU7Z0NBQ1ZDLGNBQWM7Z0NBQ2RDLGdCQUFnQjtnQ0FDaEJDLGlCQUFpQjtnQ0FDakJDLGlCQUFpQjtnQ0FDakJDLG1CQUFtQjtnQ0FDbkJDLGtCQUFrQjtnQ0FDbEJDLGtCQUFrQjtnQ0FDbEJDLGlCQUFpQjtnQ0FDakJDLGVBQWU7Z0NBQ2ZDLGNBQWM7Z0NBQ2RDLG1CQUFtQjtnQ0FDbkJDLFNBQVM7NEJBQ1g7NEJBQ0FDLGFBQWE7Z0NBQ1hDLEtBQUs7Z0NBQ0xDLFFBQVE7Z0NBQ1JDLE1BQU07Z0NBQ05DLFFBQVE7NEJBQ1Y7d0JBQ0Y7b0JBQ0YsU0FBVTt3QkFDUnJDLFdBQVc7b0JBQ2I7Z0JBQ0Y7O1lBRUFLO1FBQ0Y7cUNBQUc7UUFBQ1gsU0FBUytDO1FBQU0vQyxTQUFTZ0Q7UUFBTWhELFNBQVNpRDtRQUFVakQsU0FBU2tEO1FBQVVsRCxTQUFTbUQ7UUFBT25ELFNBQVNvRDtLQUFPO0lBRXhHLDJCQUEyQjtJQUMzQixPQUFPO1FBQ0xuRDtRQUNBRTtRQUNBRTtRQUNBRTtRQUNBOEMsU0FBUyxJQUFNQyxRQUFRQyxPQUFPO1FBQzlCQyxZQUFZLElBQU1GLFFBQVFDLE9BQU87UUFDakNFLGNBQWMsSUFBTUgsUUFBUUMsT0FBTztRQUNuQ0csZUFBZSxJQUFNSixRQUFRQyxPQUFPO1FBQ3BDSSxvQkFBb0IsSUFBTUwsUUFBUUMsT0FBTztRQUN6Q0ssb0JBQW9CLElBQU1OLFFBQVFDLE9BQU8sQ0FBQyxDQUFDO0lBQzdDO0FBQ0YiLCJzb3VyY2VzIjpbIi9Vc2Vycy92aWpheXlhc28vRG9jdW1lbnRzL0RldmVsb3BtZW50L2N5bWF0aWNzL2Zyb250ZW5kL3NyYy9ob29rcy91c2VOb3RpZmljYXRpb25zLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHVzZVN0YXRlLCB1c2VFZmZlY3QgfSBmcm9tICdyZWFjdCdcbmltcG9ydCB0eXBlIHtcbiAgTm90aWZpY2F0aW9uLFxuICBOb3RpZmljYXRpb25GaWx0ZXJzLFxuICBOb3RpZmljYXRpb25TdGF0cyxcbiAgQ3JlYXRlTm90aWZpY2F0aW9uUmVxdWVzdFxufSBmcm9tICdAL3R5cGVzL25vdGlmaWNhdGlvbnMnXG5cbmV4cG9ydCBmdW5jdGlvbiB1c2VOb3RpZmljYXRpb25zKGZpbHRlcnM/OiBOb3RpZmljYXRpb25GaWx0ZXJzKSB7XG4gIGNvbnN0IFtub3RpZmljYXRpb25zLCBzZXROb3RpZmljYXRpb25zXSA9IHVzZVN0YXRlPE5vdGlmaWNhdGlvbltdPihbXSlcbiAgY29uc3QgW3N0YXRzLCBzZXRTdGF0c10gPSB1c2VTdGF0ZTxOb3RpZmljYXRpb25TdGF0cyB8IG51bGw+KG51bGwpXG4gIGNvbnN0IFtsb2FkaW5nLCBzZXRMb2FkaW5nXSA9IHVzZVN0YXRlKGZhbHNlKVxuICBjb25zdCBbZXJyb3IsIHNldEVycm9yXSA9IHVzZVN0YXRlPHN0cmluZyB8IG51bGw+KG51bGwpXG5cbiAgY29uc29sZS5sb2coJ/CflJQgdXNlTm90aWZpY2F0aW9ucyBob29rIGNhbGxlZCB3aXRoIGZpbHRlcnM6JywgZmlsdGVycylcblxuICAvLyBGZXRjaCBub3RpZmljYXRpb25zIGFuZCBzdGF0c1xuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGNvbnNvbGUubG9nKCfwn5SUIHVzZU5vdGlmaWNhdGlvbnMgZWZmZWN0IHJ1bm5pbmcnKVxuICAgIFxuICAgIGNvbnN0IGZldGNoTm90aWZpY2F0aW9ucyA9IGFzeW5jICgpID0+IHtcbiAgICAgIHRyeSB7XG4gICAgICAgIHNldExvYWRpbmcodHJ1ZSlcbiAgICAgICAgc2V0RXJyb3IobnVsbClcblxuICAgICAgICAvLyBQcm92aWRlIGZhbGxiYWNrIGRhdGEgaW5zdGVhZCBvZiBtYWtpbmcgQVBJIGNhbGxzXG4gICAgICAgIGNvbnNvbGUud2Fybign8J+UlCBOb3RpZmljYXRpb25zIEFQSSBkaXNhYmxlZCwgdXNpbmcgZW1wdHkgZGF0YScpXG4gICAgICAgIHNldE5vdGlmaWNhdGlvbnMoW10pXG4gICAgICAgIHNldFN0YXRzKHtcbiAgICAgICAgICB0b3RhbDogMCxcbiAgICAgICAgICB1bnJlYWQ6IDAsXG4gICAgICAgICAgYnlfdHlwZToge1xuICAgICAgICAgICAgcGF5bWVudDogMCxcbiAgICAgICAgICAgIHNjaGVkdWxlOiAwLFxuICAgICAgICAgICAgdGFzazogMCxcbiAgICAgICAgICAgIGluZm86IDAsXG4gICAgICAgICAgICBzdWNjZXNzOiAwLFxuICAgICAgICAgICAgd2FybmluZzogMCxcbiAgICAgICAgICAgIGVycm9yOiAwLFxuICAgICAgICAgICAgcHJvamVjdDogMCxcbiAgICAgICAgICAgIHN5c3RlbTogMFxuICAgICAgICAgIH0sXG4gICAgICAgICAgYnlfY2F0ZWdvcnk6IHtcbiAgICAgICAgICAgIHRhc2tfYXNzaWduZWQ6IDAsXG4gICAgICAgICAgICB0YXNrX2R1ZTogMCxcbiAgICAgICAgICAgIHRhc2tfb3ZlcmR1ZTogMCxcbiAgICAgICAgICAgIHRhc2tfY29tcGxldGVkOiAwLFxuICAgICAgICAgICAgcHJvamVjdF9jcmVhdGVkOiAwLFxuICAgICAgICAgICAgcHJvamVjdF91cGRhdGVkOiAwLFxuICAgICAgICAgICAgc2NoZWR1bGVfdXBjb21pbmc6IDAsXG4gICAgICAgICAgICBzY2hlZHVsZV9jaGFuZ2VkOiAwLFxuICAgICAgICAgICAgcGF5bWVudF9yZWNlaXZlZDogMCxcbiAgICAgICAgICAgIHBheW1lbnRfb3ZlcmR1ZTogMCxcbiAgICAgICAgICAgIHN5c3RlbV91cGRhdGU6IDAsXG4gICAgICAgICAgICB1c2VyX21lbnRpb246IDAsXG4gICAgICAgICAgICBkZWFkbGluZV9yZW1pbmRlcjogMCxcbiAgICAgICAgICAgIGdlbmVyYWw6IDBcbiAgICAgICAgICB9LFxuICAgICAgICAgIGJ5X3ByaW9yaXR5OiB7XG4gICAgICAgICAgICBsb3c6IDAsXG4gICAgICAgICAgICBtZWRpdW06IDAsXG4gICAgICAgICAgICBoaWdoOiAwLFxuICAgICAgICAgICAgdXJnZW50OiAwXG4gICAgICAgICAgfVxuICAgICAgICB9KVxuICAgICAgfSBjYXRjaCAoZXJyKSB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGluIHVzZU5vdGlmaWNhdGlvbnM6JywgZXJyKVxuICAgICAgICBzZXRFcnJvcihlcnIgaW5zdGFuY2VvZiBFcnJvciA/IGVyci5tZXNzYWdlIDogJ0ZhaWxlZCB0byBmZXRjaCBub3RpZmljYXRpb25zJylcbiAgICAgICAgLy8gRW5zdXJlIHdlIHN0aWxsIHNldCBlbXB0eSBkYXRhIGV2ZW4gaWYgdGhlcmUncyBhbiBlcnJvclxuICAgICAgICBzZXROb3RpZmljYXRpb25zKFtdKVxuICAgICAgICBzZXRTdGF0cyh7XG4gICAgICAgICAgdG90YWw6IDAsXG4gICAgICAgICAgdW5yZWFkOiAwLFxuICAgICAgICAgIGJ5X3R5cGU6IHtcbiAgICAgICAgICAgIHBheW1lbnQ6IDAsXG4gICAgICAgICAgICBzY2hlZHVsZTogMCxcbiAgICAgICAgICAgIHRhc2s6IDAsXG4gICAgICAgICAgICBpbmZvOiAwLFxuICAgICAgICAgICAgc3VjY2VzczogMCxcbiAgICAgICAgICAgIHdhcm5pbmc6IDAsXG4gICAgICAgICAgICBlcnJvcjogMCxcbiAgICAgICAgICAgIHByb2plY3Q6IDAsXG4gICAgICAgICAgICBzeXN0ZW06IDBcbiAgICAgICAgICB9LFxuICAgICAgICAgIGJ5X2NhdGVnb3J5OiB7XG4gICAgICAgICAgICB0YXNrX2Fzc2lnbmVkOiAwLFxuICAgICAgICAgICAgdGFza19kdWU6IDAsXG4gICAgICAgICAgICB0YXNrX292ZXJkdWU6IDAsXG4gICAgICAgICAgICB0YXNrX2NvbXBsZXRlZDogMCxcbiAgICAgICAgICAgIHByb2plY3RfY3JlYXRlZDogMCxcbiAgICAgICAgICAgIHByb2plY3RfdXBkYXRlZDogMCxcbiAgICAgICAgICAgIHNjaGVkdWxlX3VwY29taW5nOiAwLFxuICAgICAgICAgICAgc2NoZWR1bGVfY2hhbmdlZDogMCxcbiAgICAgICAgICAgIHBheW1lbnRfcmVjZWl2ZWQ6IDAsXG4gICAgICAgICAgICBwYXltZW50X292ZXJkdWU6IDAsXG4gICAgICAgICAgICBzeXN0ZW1fdXBkYXRlOiAwLFxuICAgICAgICAgICAgdXNlcl9tZW50aW9uOiAwLFxuICAgICAgICAgICAgZGVhZGxpbmVfcmVtaW5kZXI6IDAsXG4gICAgICAgICAgICBnZW5lcmFsOiAwXG4gICAgICAgICAgfSxcbiAgICAgICAgICBieV9wcmlvcml0eToge1xuICAgICAgICAgICAgbG93OiAwLFxuICAgICAgICAgICAgbWVkaXVtOiAwLFxuICAgICAgICAgICAgaGlnaDogMCxcbiAgICAgICAgICAgIHVyZ2VudDogMFxuICAgICAgICAgIH1cbiAgICAgICAgfSlcbiAgICAgIH0gZmluYWxseSB7XG4gICAgICAgIHNldExvYWRpbmcoZmFsc2UpXG4gICAgICB9XG4gICAgfVxuICAgIFxuICAgIGZldGNoTm90aWZpY2F0aW9ucygpXG4gIH0sIFtmaWx0ZXJzPy5yZWFkLCBmaWx0ZXJzPy50eXBlLCBmaWx0ZXJzPy5jYXRlZ29yeSwgZmlsdGVycz8ucHJpb3JpdHksIGZpbHRlcnM/LmxpbWl0LCBmaWx0ZXJzPy5vZmZzZXRdKVxuXG4gIC8vIFJldHVybiBtaW5pbWFsIGludGVyZmFjZVxuICByZXR1cm4ge1xuICAgIG5vdGlmaWNhdGlvbnMsXG4gICAgc3RhdHMsXG4gICAgbG9hZGluZyxcbiAgICBlcnJvcixcbiAgICByZWZldGNoOiAoKSA9PiBQcm9taXNlLnJlc29sdmUoKSxcbiAgICBtYXJrQXNSZWFkOiAoKSA9PiBQcm9taXNlLnJlc29sdmUoKSxcbiAgICBtYXJrQXNVbnJlYWQ6ICgpID0+IFByb21pc2UucmVzb2x2ZSgpLFxuICAgIG1hcmtBbGxBc1JlYWQ6ICgpID0+IFByb21pc2UucmVzb2x2ZSgpLFxuICAgIGRlbGV0ZU5vdGlmaWNhdGlvbjogKCkgPT4gUHJvbWlzZS5yZXNvbHZlKCksXG4gICAgY3JlYXRlTm90aWZpY2F0aW9uOiAoKSA9PiBQcm9taXNlLnJlc29sdmUoe30gYXMgYW55KVxuICB9XG59Il0sIm5hbWVzIjpbInVzZVN0YXRlIiwidXNlRWZmZWN0IiwidXNlTm90aWZpY2F0aW9ucyIsImZpbHRlcnMiLCJub3RpZmljYXRpb25zIiwic2V0Tm90aWZpY2F0aW9ucyIsInN0YXRzIiwic2V0U3RhdHMiLCJsb2FkaW5nIiwic2V0TG9hZGluZyIsImVycm9yIiwic2V0RXJyb3IiLCJjb25zb2xlIiwibG9nIiwiZmV0Y2hOb3RpZmljYXRpb25zIiwid2FybiIsInRvdGFsIiwidW5yZWFkIiwiYnlfdHlwZSIsInBheW1lbnQiLCJzY2hlZHVsZSIsInRhc2siLCJpbmZvIiwic3VjY2VzcyIsIndhcm5pbmciLCJwcm9qZWN0Iiwic3lzdGVtIiwiYnlfY2F0ZWdvcnkiLCJ0YXNrX2Fzc2lnbmVkIiwidGFza19kdWUiLCJ0YXNrX292ZXJkdWUiLCJ0YXNrX2NvbXBsZXRlZCIsInByb2plY3RfY3JlYXRlZCIsInByb2plY3RfdXBkYXRlZCIsInNjaGVkdWxlX3VwY29taW5nIiwic2NoZWR1bGVfY2hhbmdlZCIsInBheW1lbnRfcmVjZWl2ZWQiLCJwYXltZW50X292ZXJkdWUiLCJzeXN0ZW1fdXBkYXRlIiwidXNlcl9tZW50aW9uIiwiZGVhZGxpbmVfcmVtaW5kZXIiLCJnZW5lcmFsIiwiYnlfcHJpb3JpdHkiLCJsb3ciLCJtZWRpdW0iLCJoaWdoIiwidXJnZW50IiwiZXJyIiwiRXJyb3IiLCJtZXNzYWdlIiwicmVhZCIsInR5cGUiLCJjYXRlZ29yeSIsInByaW9yaXR5IiwibGltaXQiLCJvZmZzZXQiLCJyZWZldGNoIiwiUHJvbWlzZSIsInJlc29sdmUiLCJtYXJrQXNSZWFkIiwibWFya0FzVW5yZWFkIiwibWFya0FsbEFzUmVhZCIsImRlbGV0ZU5vdGlmaWNhdGlvbiIsImNyZWF0ZU5vdGlmaWNhdGlvbiJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/hooks/useNotifications.ts\n");

/***/ })

};
;