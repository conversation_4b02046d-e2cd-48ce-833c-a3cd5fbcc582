"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_src_lib_notification-triggers_ts";
exports.ids = ["_ssr_src_lib_notification-triggers_ts"];
exports.modules = {

/***/ "(ssr)/./src/lib/notification-triggers.ts":
/*!******************************************!*\
  !*** ./src/lib/notification-triggers.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createTaskUpdateNotification: () => (/* binding */ createTaskUpdateNotification),\n/* harmony export */   processNotificationTriggers: () => (/* binding */ processNotificationTriggers),\n/* harmony export */   queueNotificationTriggers: () => (/* binding */ queueNotificationTriggers),\n/* harmony export */   scheduleNotificationTriggers: () => (/* binding */ scheduleNotificationTriggers)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"(ssr)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n/**\n * Notification Triggers System\n * Handles automated notification generation for payments, schedules, and tasks\n */ \nconst supabaseUrl = \"https://qxgfulribhhohmggyroq.supabase.co\";\nconst supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;\n// Use service role key for server-side operations\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseServiceKey);\n/**\n * Main function to process notification triggers\n */ async function processNotificationTriggers(triggerType, options = {}) {\n    console.log(`🔔 Processing notification triggers: ${triggerType}`);\n    try {\n        let totalCount = 0;\n        const errors = [];\n        switch(triggerType){\n            case 'payment_overdue':\n                const paymentResult = await processPaymentOverdueNotifications(options);\n                totalCount += paymentResult.count;\n                if (paymentResult.errors) errors.push(...paymentResult.errors);\n                break;\n            case 'schedule_reminders':\n                const scheduleResult = await processScheduleReminderNotifications(options);\n                totalCount += scheduleResult.count;\n                if (scheduleResult.errors) errors.push(...scheduleResult.errors);\n                break;\n            case 'task_updates':\n                // Task updates are handled in real-time via triggers, not batch processing\n                console.log('ℹ️ Task updates are handled in real-time, skipping batch processing');\n                break;\n            case 'all':\n                const allResults = await Promise.allSettled([\n                    processPaymentOverdueNotifications(options),\n                    processScheduleReminderNotifications(options)\n                ]);\n                allResults.forEach((result, index)=>{\n                    if (result.status === 'fulfilled') {\n                        totalCount += result.value.count;\n                        if (result.value.errors) errors.push(...result.value.errors);\n                    } else {\n                        errors.push(`Trigger ${index} failed: ${result.reason}`);\n                    }\n                });\n                break;\n            default:\n                throw new Error(`Unknown trigger type: ${triggerType}`);\n        }\n        return {\n            success: errors.length === 0,\n            message: `Processed ${triggerType} triggers, created ${totalCount} notifications`,\n            count: totalCount,\n            errors: errors.length > 0 ? errors : undefined\n        };\n    } catch (error) {\n        console.error('❌ Error processing notification triggers:', error);\n        return {\n            success: false,\n            message: error instanceof Error ? error.message : 'Unknown error',\n            count: 0,\n            errors: [\n                error instanceof Error ? error.message : 'Unknown error'\n            ]\n        };\n    }\n}\n/**\n * Process payment overdue notifications\n */ async function processPaymentOverdueNotifications(options = {}) {\n    try {\n        console.log('💰 Processing payment overdue notifications...');\n        if (options.dryRun) {\n            console.log('🔍 Dry run mode - no notifications will be created');\n        }\n        // Call the database function to create payment overdue notifications\n        const { data, error } = await supabase.rpc('create_payment_overdue_notifications');\n        if (error) {\n            throw error;\n        }\n        const count = data || 0;\n        console.log(`✅ Created ${count} payment overdue notifications`);\n        return {\n            success: true,\n            message: `Created ${count} payment overdue notifications`,\n            count\n        };\n    } catch (error) {\n        console.error('❌ Error processing payment overdue notifications:', error);\n        return {\n            success: false,\n            message: error instanceof Error ? error.message : 'Unknown error',\n            count: 0,\n            errors: [\n                error instanceof Error ? error.message : 'Unknown error'\n            ]\n        };\n    }\n}\n/**\n * Process schedule reminder notifications\n */ async function processScheduleReminderNotifications(options = {}) {\n    try {\n        console.log('📅 Processing schedule reminder notifications...');\n        if (options.dryRun) {\n            console.log('🔍 Dry run mode - no notifications will be created');\n        }\n        // Call the database function to create schedule reminder notifications\n        const { data, error } = await supabase.rpc('create_schedule_reminder_notifications');\n        if (error) {\n            throw error;\n        }\n        const count = data || 0;\n        console.log(`✅ Created ${count} schedule reminder notifications`);\n        return {\n            success: true,\n            message: `Created ${count} schedule reminder notifications`,\n            count\n        };\n    } catch (error) {\n        console.error('❌ Error processing schedule reminder notifications:', error);\n        return {\n            success: false,\n            message: error instanceof Error ? error.message : 'Unknown error',\n            count: 0,\n            errors: [\n                error instanceof Error ? error.message : 'Unknown error'\n            ]\n        };\n    }\n}\n/**\n * Create task update notification (called from task update triggers)\n */ async function createTaskUpdateNotification(taskId, oldStatus, newStatus, updatedByUserId) {\n    try {\n        console.log(`📋 Creating task update notification for task: ${taskId}`);\n        // Call the database function to create task update notification\n        const { error } = await supabase.rpc('create_task_update_notification', {\n            task_id_param: taskId,\n            old_status: oldStatus,\n            new_status: newStatus,\n            updated_by_user_id: updatedByUserId\n        });\n        if (error) {\n            throw error;\n        }\n        console.log(`✅ Created task update notification for task: ${taskId}`);\n        return {\n            success: true,\n            message: `Created task update notification`,\n            count: 1\n        };\n    } catch (error) {\n        console.error('❌ Error creating task update notification:', error);\n        return {\n            success: false,\n            message: error instanceof Error ? error.message : 'Unknown error',\n            count: 0,\n            errors: [\n                error instanceof Error ? error.message : 'Unknown error'\n            ]\n        };\n    }\n}\n/**\n * Queue notification triggers as background job\n */ function queueNotificationTriggers(triggerType, options = {}) {\n    const { getBackgroundJobQueue } = __webpack_require__(/*! ./background-jobs */ \"(ssr)/./src/lib/background-jobs.ts\");\n    const queue = getBackgroundJobQueue();\n    const jobId = queue.addJob('notification_triggers', 'notification', 'system', {\n        triggerType,\n        options\n    });\n    console.log(`📋 Notification triggers queued: ${triggerType} with job ID: ${jobId}`);\n    return jobId;\n}\n/**\n * Schedule periodic notification triggers\n * This should be called from a cron job or scheduled task\n */ function scheduleNotificationTriggers() {\n    console.log('⏰ Scheduling periodic notification triggers...');\n    // Schedule payment overdue checks (daily at 9 AM)\n    const paymentJobId = queueNotificationTriggers('payment_overdue');\n    console.log(`📅 Scheduled payment overdue check: ${paymentJobId}`);\n    // Schedule schedule reminder checks (every 30 minutes)\n    const scheduleJobId = queueNotificationTriggers('schedule_reminders');\n    console.log(`📅 Scheduled schedule reminder check: ${scheduleJobId}`);\n    return {\n        paymentJobId,\n        scheduleJobId\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/notification-triggers.ts\n");

/***/ })

};
;