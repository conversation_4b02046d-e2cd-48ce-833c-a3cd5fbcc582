{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "Veic9U5KVSHwsbVdwWmZWThF0H2eTQj0ePNQOZsDSiU=", "__NEXT_PREVIEW_MODE_ID": "3e514d7c7c9ae2a6aa380cde49ea478b", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "d6d45da1582b8fbb996c11ef4a0170d670be6f72425c9d63437735a37f81bc46", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "eb52fe4fe39383528d7c8c950b851498fa4b7596323b54fbaf4c10b7fb3c0f2d"}}}, "functions": {}, "sortedMiddleware": ["/"]}