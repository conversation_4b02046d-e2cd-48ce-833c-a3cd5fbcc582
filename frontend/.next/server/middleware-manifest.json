{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "Veic9U5KVSHwsbVdwWmZWThF0H2eTQj0ePNQOZsDSiU=", "__NEXT_PREVIEW_MODE_ID": "1d23c2481f0ea0a15f5fcbc1e764979f", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "dfc13c49d0db8ba34a982151e031c11b1292354dbf19bbfae148374cfb17a442", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "06e44e66f285d0a9b5f7efab907dcbb6673e12c532cfe918c567cbe6c3c38c13"}}}, "functions": {}, "sortedMiddleware": ["/"]}