"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/exifr";
exports.ids = ["vendor-chunks/exifr"];
exports.modules = {

/***/ "(rsc)/./node_modules/exifr/dist/full.esm.mjs":
/*!**********************************************!*\
  !*** ./node_modules/exifr/dist/full.esm.mjs ***!
  \**********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Exifr: () => (/* binding */ te),\n/* harmony export */   Options: () => (/* binding */ q),\n/* harmony export */   allFormatters: () => (/* binding */ X),\n/* harmony export */   chunkedProps: () => (/* binding */ G),\n/* harmony export */   createDictionary: () => (/* binding */ U),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   extendDictionary: () => (/* binding */ F),\n/* harmony export */   fetchUrlAsArrayBuffer: () => (/* binding */ M),\n/* harmony export */   fileParsers: () => (/* binding */ w),\n/* harmony export */   fileReaders: () => (/* binding */ A),\n/* harmony export */   gps: () => (/* binding */ Se),\n/* harmony export */   gpsOnlyOptions: () => (/* binding */ me),\n/* harmony export */   inheritables: () => (/* binding */ K),\n/* harmony export */   orientation: () => (/* binding */ Pe),\n/* harmony export */   orientationOnlyOptions: () => (/* binding */ Ie),\n/* harmony export */   otherSegments: () => (/* binding */ V),\n/* harmony export */   parse: () => (/* binding */ ie),\n/* harmony export */   readBlobAsArrayBuffer: () => (/* binding */ R),\n/* harmony export */   rotateCanvas: () => (/* binding */ we),\n/* harmony export */   rotateCss: () => (/* binding */ Te),\n/* harmony export */   rotation: () => (/* binding */ Ae),\n/* harmony export */   rotations: () => (/* binding */ ke),\n/* harmony export */   segmentParsers: () => (/* binding */ T),\n/* harmony export */   segments: () => (/* binding */ z),\n/* harmony export */   segmentsAndBlocks: () => (/* binding */ j),\n/* harmony export */   sidecar: () => (/* binding */ st),\n/* harmony export */   tagKeys: () => (/* binding */ E),\n/* harmony export */   tagRevivers: () => (/* binding */ N),\n/* harmony export */   tagValues: () => (/* binding */ B),\n/* harmony export */   thumbnail: () => (/* binding */ ye),\n/* harmony export */   thumbnailOnlyOptions: () => (/* binding */ Ce),\n/* harmony export */   thumbnailUrl: () => (/* binding */ be),\n/* harmony export */   tiffBlocks: () => (/* binding */ H),\n/* harmony export */   tiffExtractables: () => (/* binding */ W)\n/* harmony export */ });\nvar e=\"undefined\"!=typeof self?self:global;const t=\"undefined\"!=typeof navigator,i=t&&\"undefined\"==typeof HTMLImageElement,n=!(\"undefined\"==typeof global||\"undefined\"==typeof process||!process.versions||!process.versions.node),s=e.Buffer,r=e.BigInt,a=!!s,o=e=>e;function l(e,t=o){if(n)try{return\"function\"==typeof require?Promise.resolve(t(require(e))):import(/* webpackIgnore: true */ e).then(t)}catch(t){console.warn(`Couldn't load ${e}`)}}let h=e.fetch;const u=e=>h=e;if(!e.fetch){const e=l(\"http\",(e=>e)),t=l(\"https\",(e=>e)),i=(n,{headers:s}={})=>new Promise((async(r,a)=>{let{port:o,hostname:l,pathname:h,protocol:u,search:c}=new URL(n);const f={method:\"GET\",hostname:l,path:encodeURI(h)+c,headers:s};\"\"!==o&&(f.port=Number(o));const d=(\"https:\"===u?await t:await e).request(f,(e=>{if(301===e.statusCode||302===e.statusCode){let t=new URL(e.headers.location,n).toString();return i(t,{headers:s}).then(r).catch(a)}r({status:e.statusCode,arrayBuffer:()=>new Promise((t=>{let i=[];e.on(\"data\",(e=>i.push(e))),e.on(\"end\",(()=>t(Buffer.concat(i))))}))})}));d.on(\"error\",a),d.end()}));u(i)}function c(e,t,i){return t in e?Object.defineProperty(e,t,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[t]=i,e}const f=e=>p(e)?void 0:e,d=e=>void 0!==e;function p(e){return void 0===e||(e instanceof Map?0===e.size:0===Object.values(e).filter(d).length)}function g(e){let t=new Error(e);throw delete t.stack,t}function m(e){return\"\"===(e=function(e){for(;e.endsWith(\"\\0\");)e=e.slice(0,-1);return e}(e).trim())?void 0:e}function S(e){let t=function(e){let t=0;return e.ifd0.enabled&&(t+=1024),e.exif.enabled&&(t+=2048),e.makerNote&&(t+=2048),e.userComment&&(t+=1024),e.gps.enabled&&(t+=512),e.interop.enabled&&(t+=100),e.ifd1.enabled&&(t+=1024),t+2048}(e);return e.jfif.enabled&&(t+=50),e.xmp.enabled&&(t+=2e4),e.iptc.enabled&&(t+=14e3),e.icc.enabled&&(t+=6e3),t}const C=e=>String.fromCharCode.apply(null,e),y=\"undefined\"!=typeof TextDecoder?new TextDecoder(\"utf-8\"):void 0;function b(e){return y?y.decode(e):a?Buffer.from(e).toString(\"utf8\"):decodeURIComponent(escape(C(e)))}class I{static from(e,t){return e instanceof this&&e.le===t?e:new I(e,void 0,void 0,t)}constructor(e,t=0,i,n){if(\"boolean\"==typeof n&&(this.le=n),Array.isArray(e)&&(e=new Uint8Array(e)),0===e)this.byteOffset=0,this.byteLength=0;else if(e instanceof ArrayBuffer){void 0===i&&(i=e.byteLength-t);let n=new DataView(e,t,i);this._swapDataView(n)}else if(e instanceof Uint8Array||e instanceof DataView||e instanceof I){void 0===i&&(i=e.byteLength-t),(t+=e.byteOffset)+i>e.byteOffset+e.byteLength&&g(\"Creating view outside of available memory in ArrayBuffer\");let n=new DataView(e.buffer,t,i);this._swapDataView(n)}else if(\"number\"==typeof e){let t=new DataView(new ArrayBuffer(e));this._swapDataView(t)}else g(\"Invalid input argument for BufferView: \"+e)}_swapArrayBuffer(e){this._swapDataView(new DataView(e))}_swapBuffer(e){this._swapDataView(new DataView(e.buffer,e.byteOffset,e.byteLength))}_swapDataView(e){this.dataView=e,this.buffer=e.buffer,this.byteOffset=e.byteOffset,this.byteLength=e.byteLength}_lengthToEnd(e){return this.byteLength-e}set(e,t,i=I){return e instanceof DataView||e instanceof I?e=new Uint8Array(e.buffer,e.byteOffset,e.byteLength):e instanceof ArrayBuffer&&(e=new Uint8Array(e)),e instanceof Uint8Array||g(\"BufferView.set(): Invalid data argument.\"),this.toUint8().set(e,t),new i(this,t,e.byteLength)}subarray(e,t){return t=t||this._lengthToEnd(e),new I(this,e,t)}toUint8(){return new Uint8Array(this.buffer,this.byteOffset,this.byteLength)}getUint8Array(e,t){return new Uint8Array(this.buffer,this.byteOffset+e,t)}getString(e=0,t=this.byteLength){return b(this.getUint8Array(e,t))}getLatin1String(e=0,t=this.byteLength){let i=this.getUint8Array(e,t);return C(i)}getUnicodeString(e=0,t=this.byteLength){const i=[];for(let n=0;n<t&&e+n<this.byteLength;n+=2)i.push(this.getUint16(e+n));return C(i)}getInt8(e){return this.dataView.getInt8(e)}getUint8(e){return this.dataView.getUint8(e)}getInt16(e,t=this.le){return this.dataView.getInt16(e,t)}getInt32(e,t=this.le){return this.dataView.getInt32(e,t)}getUint16(e,t=this.le){return this.dataView.getUint16(e,t)}getUint32(e,t=this.le){return this.dataView.getUint32(e,t)}getFloat32(e,t=this.le){return this.dataView.getFloat32(e,t)}getFloat64(e,t=this.le){return this.dataView.getFloat64(e,t)}getFloat(e,t=this.le){return this.dataView.getFloat32(e,t)}getDouble(e,t=this.le){return this.dataView.getFloat64(e,t)}getUintBytes(e,t,i){switch(t){case 1:return this.getUint8(e,i);case 2:return this.getUint16(e,i);case 4:return this.getUint32(e,i);case 8:return this.getUint64&&this.getUint64(e,i)}}getUint(e,t,i){switch(t){case 8:return this.getUint8(e,i);case 16:return this.getUint16(e,i);case 32:return this.getUint32(e,i);case 64:return this.getUint64&&this.getUint64(e,i)}}toString(e){return this.dataView.toString(e,this.constructor.name)}ensureChunk(){}}function P(e,t){g(`${e} '${t}' was not loaded, try using full build of exifr.`)}class k extends Map{constructor(e){super(),this.kind=e}get(e,t){return this.has(e)||P(this.kind,e),t&&(e in t||function(e,t){g(`Unknown ${e} '${t}'.`)}(this.kind,e),t[e].enabled||P(this.kind,e)),super.get(e)}keyList(){return Array.from(this.keys())}}var w=new k(\"file parser\"),T=new k(\"segment parser\"),A=new k(\"file reader\");function D(e,n){return\"string\"==typeof e?O(e,n):t&&!i&&e instanceof HTMLImageElement?O(e.src,n):e instanceof Uint8Array||e instanceof ArrayBuffer||e instanceof DataView?new I(e):t&&e instanceof Blob?x(e,n,\"blob\",R):void g(\"Invalid input argument\")}function O(e,i){return(s=e).startsWith(\"data:\")||s.length>1e4?v(e,i,\"base64\"):n&&e.includes(\"://\")?x(e,i,\"url\",M):n?v(e,i,\"fs\"):t?x(e,i,\"url\",M):void g(\"Invalid input argument\");var s}async function x(e,t,i,n){return A.has(i)?v(e,t,i):n?async function(e,t){let i=await t(e);return new I(i)}(e,n):void g(`Parser ${i} is not loaded`)}async function v(e,t,i){let n=new(A.get(i))(e,t);return await n.read(),n}const M=e=>h(e).then((e=>e.arrayBuffer())),R=e=>new Promise(((t,i)=>{let n=new FileReader;n.onloadend=()=>t(n.result||new ArrayBuffer),n.onerror=i,n.readAsArrayBuffer(e)}));class L extends Map{get tagKeys(){return this.allKeys||(this.allKeys=Array.from(this.keys())),this.allKeys}get tagValues(){return this.allValues||(this.allValues=Array.from(this.values())),this.allValues}}function U(e,t,i){let n=new L;for(let[e,t]of i)n.set(e,t);if(Array.isArray(t))for(let i of t)e.set(i,n);else e.set(t,n);return n}function F(e,t,i){let n,s=e.get(t);for(n of i)s.set(n[0],n[1])}const E=new Map,B=new Map,N=new Map,G=[\"chunked\",\"firstChunkSize\",\"firstChunkSizeNode\",\"firstChunkSizeBrowser\",\"chunkSize\",\"chunkLimit\"],V=[\"jfif\",\"xmp\",\"icc\",\"iptc\",\"ihdr\"],z=[\"tiff\",...V],H=[\"ifd0\",\"ifd1\",\"exif\",\"gps\",\"interop\"],j=[...z,...H],W=[\"makerNote\",\"userComment\"],K=[\"translateKeys\",\"translateValues\",\"reviveValues\",\"multiSegment\"],X=[...K,\"sanitize\",\"mergeOutput\",\"silentErrors\"];class _{get translate(){return this.translateKeys||this.translateValues||this.reviveValues}}class Y extends _{get needed(){return this.enabled||this.deps.size>0}constructor(e,t,i,n){if(super(),c(this,\"enabled\",!1),c(this,\"skip\",new Set),c(this,\"pick\",new Set),c(this,\"deps\",new Set),c(this,\"translateKeys\",!1),c(this,\"translateValues\",!1),c(this,\"reviveValues\",!1),this.key=e,this.enabled=t,this.parse=this.enabled,this.applyInheritables(n),this.canBeFiltered=H.includes(e),this.canBeFiltered&&(this.dict=E.get(e)),void 0!==i)if(Array.isArray(i))this.parse=this.enabled=!0,this.canBeFiltered&&i.length>0&&this.translateTagSet(i,this.pick);else if(\"object\"==typeof i){if(this.enabled=!0,this.parse=!1!==i.parse,this.canBeFiltered){let{pick:e,skip:t}=i;e&&e.length>0&&this.translateTagSet(e,this.pick),t&&t.length>0&&this.translateTagSet(t,this.skip)}this.applyInheritables(i)}else!0===i||!1===i?this.parse=this.enabled=i:g(`Invalid options argument: ${i}`)}applyInheritables(e){let t,i;for(t of K)i=e[t],void 0!==i&&(this[t]=i)}translateTagSet(e,t){if(this.dict){let i,n,{tagKeys:s,tagValues:r}=this.dict;for(i of e)\"string\"==typeof i?(n=r.indexOf(i),-1===n&&(n=s.indexOf(Number(i))),-1!==n&&t.add(Number(s[n]))):t.add(i)}else for(let i of e)t.add(i)}finalizeFilters(){!this.enabled&&this.deps.size>0?(this.enabled=!0,ee(this.pick,this.deps)):this.enabled&&this.pick.size>0&&ee(this.pick,this.deps)}}var $={jfif:!1,tiff:!0,xmp:!1,icc:!1,iptc:!1,ifd0:!0,ifd1:!1,exif:!0,gps:!0,interop:!1,ihdr:void 0,makerNote:!1,userComment:!1,multiSegment:!1,skip:[],pick:[],translateKeys:!0,translateValues:!0,reviveValues:!0,sanitize:!0,mergeOutput:!0,silentErrors:!0,chunked:!0,firstChunkSize:void 0,firstChunkSizeNode:512,firstChunkSizeBrowser:65536,chunkSize:65536,chunkLimit:5},J=new Map;class q extends _{static useCached(e){let t=J.get(e);return void 0!==t||(t=new this(e),J.set(e,t)),t}constructor(e){super(),!0===e?this.setupFromTrue():void 0===e?this.setupFromUndefined():Array.isArray(e)?this.setupFromArray(e):\"object\"==typeof e?this.setupFromObject(e):g(`Invalid options argument ${e}`),void 0===this.firstChunkSize&&(this.firstChunkSize=t?this.firstChunkSizeBrowser:this.firstChunkSizeNode),this.mergeOutput&&(this.ifd1.enabled=!1),this.filterNestedSegmentTags(),this.traverseTiffDependencyTree(),this.checkLoadedPlugins()}setupFromUndefined(){let e;for(e of G)this[e]=$[e];for(e of X)this[e]=$[e];for(e of W)this[e]=$[e];for(e of j)this[e]=new Y(e,$[e],void 0,this)}setupFromTrue(){let e;for(e of G)this[e]=$[e];for(e of X)this[e]=$[e];for(e of W)this[e]=!0;for(e of j)this[e]=new Y(e,!0,void 0,this)}setupFromArray(e){let t;for(t of G)this[t]=$[t];for(t of X)this[t]=$[t];for(t of W)this[t]=$[t];for(t of j)this[t]=new Y(t,!1,void 0,this);this.setupGlobalFilters(e,void 0,H)}setupFromObject(e){let t;for(t of(H.ifd0=H.ifd0||H.image,H.ifd1=H.ifd1||H.thumbnail,Object.assign(this,e),G))this[t]=Z(e[t],$[t]);for(t of X)this[t]=Z(e[t],$[t]);for(t of W)this[t]=Z(e[t],$[t]);for(t of z)this[t]=new Y(t,$[t],e[t],this);for(t of H)this[t]=new Y(t,$[t],e[t],this.tiff);this.setupGlobalFilters(e.pick,e.skip,H,j),!0===e.tiff?this.batchEnableWithBool(H,!0):!1===e.tiff?this.batchEnableWithUserValue(H,e):Array.isArray(e.tiff)?this.setupGlobalFilters(e.tiff,void 0,H):\"object\"==typeof e.tiff&&this.setupGlobalFilters(e.tiff.pick,e.tiff.skip,H)}batchEnableWithBool(e,t){for(let i of e)this[i].enabled=t}batchEnableWithUserValue(e,t){for(let i of e){let e=t[i];this[i].enabled=!1!==e&&void 0!==e}}setupGlobalFilters(e,t,i,n=i){if(e&&e.length){for(let e of n)this[e].enabled=!1;let t=Q(e,i);for(let[e,i]of t)ee(this[e].pick,i),this[e].enabled=!0}else if(t&&t.length){let e=Q(t,i);for(let[t,i]of e)ee(this[t].skip,i)}}filterNestedSegmentTags(){let{ifd0:e,exif:t,xmp:i,iptc:n,icc:s}=this;this.makerNote?t.deps.add(37500):t.skip.add(37500),this.userComment?t.deps.add(37510):t.skip.add(37510),i.enabled||e.skip.add(700),n.enabled||e.skip.add(33723),s.enabled||e.skip.add(34675)}traverseTiffDependencyTree(){let{ifd0:e,exif:t,gps:i,interop:n}=this;n.needed&&(t.deps.add(40965),e.deps.add(40965)),t.needed&&e.deps.add(34665),i.needed&&e.deps.add(34853),this.tiff.enabled=H.some((e=>!0===this[e].enabled))||this.makerNote||this.userComment;for(let e of H)this[e].finalizeFilters()}get onlyTiff(){return!V.map((e=>this[e].enabled)).some((e=>!0===e))&&this.tiff.enabled}checkLoadedPlugins(){for(let e of z)this[e].enabled&&!T.has(e)&&P(\"segment parser\",e)}}function Q(e,t){let i,n,s,r,a=[];for(s of t){for(r of(i=E.get(s),n=[],i))(e.includes(r[0])||e.includes(r[1]))&&n.push(r[0]);n.length&&a.push([s,n])}return a}function Z(e,t){return void 0!==e?e:void 0!==t?t:void 0}function ee(e,t){for(let i of t)e.add(i)}c(q,\"default\",$);class te{constructor(e){c(this,\"parsers\",{}),c(this,\"output\",{}),c(this,\"errors\",[]),c(this,\"pushToErrors\",(e=>this.errors.push(e))),this.options=q.useCached(e)}async read(e){this.file=await D(e,this.options)}setup(){if(this.fileParser)return;let{file:e}=this,t=e.getUint16(0);for(let[i,n]of w)if(n.canHandle(e,t))return this.fileParser=new n(this.options,this.file,this.parsers),e[i]=!0;this.file.close&&this.file.close(),g(\"Unknown file format\")}async parse(){let{output:e,errors:t}=this;return this.setup(),this.options.silentErrors?(await this.executeParsers().catch(this.pushToErrors),t.push(...this.fileParser.errors)):await this.executeParsers(),this.file.close&&this.file.close(),this.options.silentErrors&&t.length>0&&(e.errors=t),f(e)}async executeParsers(){let{output:e}=this;await this.fileParser.parse();let t=Object.values(this.parsers).map((async t=>{let i=await t.parse();t.assignToOutput(e,i)}));this.options.silentErrors&&(t=t.map((e=>e.catch(this.pushToErrors)))),await Promise.all(t)}async extractThumbnail(){this.setup();let{options:e,file:t}=this,i=T.get(\"tiff\",e);var n;if(t.tiff?n={start:0,type:\"tiff\"}:t.jpeg&&(n=await this.fileParser.getOrFindSegment(\"tiff\")),void 0===n)return;let s=await this.fileParser.ensureSegmentChunk(n),r=this.parsers.tiff=new i(s,e,t),a=await r.extractThumbnail();return t.close&&t.close(),a}}async function ie(e,t){let i=new te(t);return await i.read(e),i.parse()}var ne=Object.freeze({__proto__:null,parse:ie,Exifr:te,fileParsers:w,segmentParsers:T,fileReaders:A,tagKeys:E,tagValues:B,tagRevivers:N,createDictionary:U,extendDictionary:F,fetchUrlAsArrayBuffer:M,readBlobAsArrayBuffer:R,chunkedProps:G,otherSegments:V,segments:z,tiffBlocks:H,segmentsAndBlocks:j,tiffExtractables:W,inheritables:K,allFormatters:X,Options:q});class se{constructor(e,t,i){c(this,\"errors\",[]),c(this,\"ensureSegmentChunk\",(async e=>{let t=e.start,i=e.size||65536;if(this.file.chunked)if(this.file.available(t,i))e.chunk=this.file.subarray(t,i);else try{e.chunk=await this.file.readChunk(t,i)}catch(t){g(`Couldn't read segment: ${JSON.stringify(e)}. ${t.message}`)}else this.file.byteLength>t+i?e.chunk=this.file.subarray(t,i):void 0===e.size?e.chunk=this.file.subarray(t):g(\"Segment unreachable: \"+JSON.stringify(e));return e.chunk})),this.extendOptions&&this.extendOptions(e),this.options=e,this.file=t,this.parsers=i}injectSegment(e,t){this.options[e].enabled&&this.createParser(e,t)}createParser(e,t){let i=new(T.get(e))(t,this.options,this.file);return this.parsers[e]=i}createParsers(e){for(let t of e){let{type:e,chunk:i}=t,n=this.options[e];if(n&&n.enabled){let t=this.parsers[e];t&&t.append||t||this.createParser(e,i)}}}async readSegments(e){let t=e.map(this.ensureSegmentChunk);await Promise.all(t)}}class re{static findPosition(e,t){let i=e.getUint16(t+2)+2,n=\"function\"==typeof this.headerLength?this.headerLength(e,t,i):this.headerLength,s=t+n,r=i-n;return{offset:t,length:i,headerLength:n,start:s,size:r,end:s+r}}static parse(e,t={}){return new this(e,new q({[this.type]:t}),e).parse()}normalizeInput(e){return e instanceof I?e:new I(e)}constructor(e,t={},i){c(this,\"errors\",[]),c(this,\"raw\",new Map),c(this,\"handleError\",(e=>{if(!this.options.silentErrors)throw e;this.errors.push(e.message)})),this.chunk=this.normalizeInput(e),this.file=i,this.type=this.constructor.type,this.globalOptions=this.options=t,this.localOptions=t[this.type],this.canTranslate=this.localOptions&&this.localOptions.translate}translate(){this.canTranslate&&(this.translated=this.translateBlock(this.raw,this.type))}get output(){return this.translated?this.translated:this.raw?Object.fromEntries(this.raw):void 0}translateBlock(e,t){let i=N.get(t),n=B.get(t),s=E.get(t),r=this.options[t],a=r.reviveValues&&!!i,o=r.translateValues&&!!n,l=r.translateKeys&&!!s,h={};for(let[t,r]of e)a&&i.has(t)?r=i.get(t)(r):o&&n.has(t)&&(r=this.translateValue(r,n.get(t))),l&&s.has(t)&&(t=s.get(t)||t),h[t]=r;return h}translateValue(e,t){return t[e]||t.DEFAULT||e}assignToOutput(e,t){this.assignObjectToOutput(e,this.constructor.type,t)}assignObjectToOutput(e,t,i){if(this.globalOptions.mergeOutput)return Object.assign(e,i);e[t]?Object.assign(e[t],i):e[t]=i}}c(re,\"headerLength\",4),c(re,\"type\",void 0),c(re,\"multiSegment\",!1),c(re,\"canHandle\",(()=>!1));function ae(e){return 192===e||194===e||196===e||219===e||221===e||218===e||254===e}function oe(e){return e>=224&&e<=239}function le(e,t,i){for(let[n,s]of T)if(s.canHandle(e,t,i))return n}class he extends se{constructor(...e){super(...e),c(this,\"appSegments\",[]),c(this,\"jpegSegments\",[]),c(this,\"unknownSegments\",[])}static canHandle(e,t){return 65496===t}async parse(){await this.findAppSegments(),await this.readSegments(this.appSegments),this.mergeMultiSegments(),this.createParsers(this.mergedAppSegments||this.appSegments)}setupSegmentFinderArgs(e){!0===e?(this.findAll=!0,this.wanted=new Set(T.keyList())):(e=void 0===e?T.keyList().filter((e=>this.options[e].enabled)):e.filter((e=>this.options[e].enabled&&T.has(e))),this.findAll=!1,this.remaining=new Set(e),this.wanted=new Set(e)),this.unfinishedMultiSegment=!1}async findAppSegments(e=0,t){this.setupSegmentFinderArgs(t);let{file:i,findAll:n,wanted:s,remaining:r}=this;if(!n&&this.file.chunked&&(n=Array.from(s).some((e=>{let t=T.get(e),i=this.options[e];return t.multiSegment&&i.multiSegment})),n&&await this.file.readWhole()),e=this.findAppSegmentsInRange(e,i.byteLength),!this.options.onlyTiff&&i.chunked){let t=!1;for(;r.size>0&&!t&&(i.canReadNextChunk||this.unfinishedMultiSegment);){let{nextChunkOffset:n}=i,s=this.appSegments.some((e=>!this.file.available(e.offset||e.start,e.length||e.size)));if(t=e>n&&!s?!await i.readNextChunk(e):!await i.readNextChunk(n),void 0===(e=this.findAppSegmentsInRange(e,i.byteLength)))return}}}findAppSegmentsInRange(e,t){t-=2;let i,n,s,r,a,o,{file:l,findAll:h,wanted:u,remaining:c,options:f}=this;for(;e<t;e++)if(255===l.getUint8(e))if(i=l.getUint8(e+1),oe(i)){if(n=l.getUint16(e+2),s=le(l,e,n),s&&u.has(s)&&(r=T.get(s),a=r.findPosition(l,e),o=f[s],a.type=s,this.appSegments.push(a),!h&&(r.multiSegment&&o.multiSegment?(this.unfinishedMultiSegment=a.chunkNumber<a.chunkCount,this.unfinishedMultiSegment||c.delete(s)):c.delete(s),0===c.size)))break;f.recordUnknownSegments&&(a=re.findPosition(l,e),a.marker=i,this.unknownSegments.push(a)),e+=n+1}else if(ae(i)){if(n=l.getUint16(e+2),218===i&&!1!==f.stopAfterSos)return;f.recordJpegSegments&&this.jpegSegments.push({offset:e,length:n,marker:i}),e+=n+1}return e}mergeMultiSegments(){if(!this.appSegments.some((e=>e.multiSegment)))return;let e=function(e,t){let i,n,s,r=new Map;for(let a=0;a<e.length;a++)i=e[a],n=i[t],r.has(n)?s=r.get(n):r.set(n,s=[]),s.push(i);return Array.from(r)}(this.appSegments,\"type\");this.mergedAppSegments=e.map((([e,t])=>{let i=T.get(e,this.options);if(i.handleMultiSegments){return{type:e,chunk:i.handleMultiSegments(t)}}return t[0]}))}getSegment(e){return this.appSegments.find((t=>t.type===e))}async getOrFindSegment(e){let t=this.getSegment(e);return void 0===t&&(await this.findAppSegments(0,[e]),t=this.getSegment(e)),t}}c(he,\"type\",\"jpeg\"),w.set(\"jpeg\",he);const ue=[void 0,1,1,2,4,8,1,1,2,4,8,4,8,4];class ce extends re{parseHeader(){var e=this.chunk.getUint16();18761===e?this.le=!0:19789===e&&(this.le=!1),this.chunk.le=this.le,this.headerParsed=!0}parseTags(e,t,i=new Map){let{pick:n,skip:s}=this.options[t];n=new Set(n);let r=n.size>0,a=0===s.size,o=this.chunk.getUint16(e);e+=2;for(let l=0;l<o;l++){let o=this.chunk.getUint16(e);if(r){if(n.has(o)&&(i.set(o,this.parseTag(e,o,t)),n.delete(o),0===n.size))break}else!a&&s.has(o)||i.set(o,this.parseTag(e,o,t));e+=12}return i}parseTag(e,t,i){let{chunk:n}=this,s=n.getUint16(e+2),r=n.getUint32(e+4),a=ue[s];if(a*r<=4?e+=8:e=n.getUint32(e+8),(s<1||s>13)&&g(`Invalid TIFF value type. block: ${i.toUpperCase()}, tag: ${t.toString(16)}, type: ${s}, offset ${e}`),e>n.byteLength&&g(`Invalid TIFF value offset. block: ${i.toUpperCase()}, tag: ${t.toString(16)}, type: ${s}, offset ${e} is outside of chunk size ${n.byteLength}`),1===s)return n.getUint8Array(e,r);if(2===s)return m(n.getString(e,r));if(7===s)return n.getUint8Array(e,r);if(1===r)return this.parseTagValue(s,e);{let t=new(function(e){switch(e){case 1:return Uint8Array;case 3:return Uint16Array;case 4:return Uint32Array;case 5:return Array;case 6:return Int8Array;case 8:return Int16Array;case 9:return Int32Array;case 10:return Array;case 11:return Float32Array;case 12:return Float64Array;default:return Array}}(s))(r),i=a;for(let n=0;n<r;n++)t[n]=this.parseTagValue(s,e),e+=i;return t}}parseTagValue(e,t){let{chunk:i}=this;switch(e){case 1:return i.getUint8(t);case 3:return i.getUint16(t);case 4:return i.getUint32(t);case 5:return i.getUint32(t)/i.getUint32(t+4);case 6:return i.getInt8(t);case 8:return i.getInt16(t);case 9:return i.getInt32(t);case 10:return i.getInt32(t)/i.getInt32(t+4);case 11:return i.getFloat(t);case 12:return i.getDouble(t);case 13:return i.getUint32(t);default:g(`Invalid tiff type ${e}`)}}}class fe extends ce{static canHandle(e,t){return 225===e.getUint8(t+1)&&1165519206===e.getUint32(t+4)&&0===e.getUint16(t+8)}async parse(){this.parseHeader();let{options:e}=this;return e.ifd0.enabled&&await this.parseIfd0Block(),e.exif.enabled&&await this.safeParse(\"parseExifBlock\"),e.gps.enabled&&await this.safeParse(\"parseGpsBlock\"),e.interop.enabled&&await this.safeParse(\"parseInteropBlock\"),e.ifd1.enabled&&await this.safeParse(\"parseThumbnailBlock\"),this.createOutput()}safeParse(e){let t=this[e]();return void 0!==t.catch&&(t=t.catch(this.handleError)),t}findIfd0Offset(){void 0===this.ifd0Offset&&(this.ifd0Offset=this.chunk.getUint32(4))}findIfd1Offset(){if(void 0===this.ifd1Offset){this.findIfd0Offset();let e=this.chunk.getUint16(this.ifd0Offset),t=this.ifd0Offset+2+12*e;this.ifd1Offset=this.chunk.getUint32(t)}}parseBlock(e,t){let i=new Map;return this[t]=i,this.parseTags(e,t,i),i}async parseIfd0Block(){if(this.ifd0)return;let{file:e}=this;this.findIfd0Offset(),this.ifd0Offset<8&&g(\"Malformed EXIF data\"),!e.chunked&&this.ifd0Offset>e.byteLength&&g(`IFD0 offset points to outside of file.\\nthis.ifd0Offset: ${this.ifd0Offset}, file.byteLength: ${e.byteLength}`),e.tiff&&await e.ensureChunk(this.ifd0Offset,S(this.options));let t=this.parseBlock(this.ifd0Offset,\"ifd0\");return 0!==t.size?(this.exifOffset=t.get(34665),this.interopOffset=t.get(40965),this.gpsOffset=t.get(34853),this.xmp=t.get(700),this.iptc=t.get(33723),this.icc=t.get(34675),this.options.sanitize&&(t.delete(34665),t.delete(40965),t.delete(34853),t.delete(700),t.delete(33723),t.delete(34675)),t):void 0}async parseExifBlock(){if(this.exif)return;if(this.ifd0||await this.parseIfd0Block(),void 0===this.exifOffset)return;this.file.tiff&&await this.file.ensureChunk(this.exifOffset,S(this.options));let e=this.parseBlock(this.exifOffset,\"exif\");return this.interopOffset||(this.interopOffset=e.get(40965)),this.makerNote=e.get(37500),this.userComment=e.get(37510),this.options.sanitize&&(e.delete(40965),e.delete(37500),e.delete(37510)),this.unpack(e,41728),this.unpack(e,41729),e}unpack(e,t){let i=e.get(t);i&&1===i.length&&e.set(t,i[0])}async parseGpsBlock(){if(this.gps)return;if(this.ifd0||await this.parseIfd0Block(),void 0===this.gpsOffset)return;let e=this.parseBlock(this.gpsOffset,\"gps\");return e&&e.has(2)&&e.has(4)&&(e.set(\"latitude\",de(...e.get(2),e.get(1))),e.set(\"longitude\",de(...e.get(4),e.get(3)))),e}async parseInteropBlock(){if(!this.interop&&(this.ifd0||await this.parseIfd0Block(),void 0!==this.interopOffset||this.exif||await this.parseExifBlock(),void 0!==this.interopOffset))return this.parseBlock(this.interopOffset,\"interop\")}async parseThumbnailBlock(e=!1){if(!this.ifd1&&!this.ifd1Parsed&&(!this.options.mergeOutput||e))return this.findIfd1Offset(),this.ifd1Offset>0&&(this.parseBlock(this.ifd1Offset,\"ifd1\"),this.ifd1Parsed=!0),this.ifd1}async extractThumbnail(){if(this.headerParsed||this.parseHeader(),this.ifd1Parsed||await this.parseThumbnailBlock(!0),void 0===this.ifd1)return;let e=this.ifd1.get(513),t=this.ifd1.get(514);return this.chunk.getUint8Array(e,t)}get image(){return this.ifd0}get thumbnail(){return this.ifd1}createOutput(){let e,t,i,n={};for(t of H)if(e=this[t],!p(e))if(i=this.canTranslate?this.translateBlock(e,t):Object.fromEntries(e),this.options.mergeOutput){if(\"ifd1\"===t)continue;Object.assign(n,i)}else n[t]=i;return this.makerNote&&(n.makerNote=this.makerNote),this.userComment&&(n.userComment=this.userComment),n}assignToOutput(e,t){if(this.globalOptions.mergeOutput)Object.assign(e,t);else for(let[i,n]of Object.entries(t))this.assignObjectToOutput(e,i,n)}}function de(e,t,i,n){var s=e+t/60+i/3600;return\"S\"!==n&&\"W\"!==n||(s*=-1),s}c(fe,\"type\",\"tiff\"),c(fe,\"headerLength\",10),T.set(\"tiff\",fe);var pe=Object.freeze({__proto__:null,default:ne,Exifr:te,fileParsers:w,segmentParsers:T,fileReaders:A,tagKeys:E,tagValues:B,tagRevivers:N,createDictionary:U,extendDictionary:F,fetchUrlAsArrayBuffer:M,readBlobAsArrayBuffer:R,chunkedProps:G,otherSegments:V,segments:z,tiffBlocks:H,segmentsAndBlocks:j,tiffExtractables:W,inheritables:K,allFormatters:X,Options:q,parse:ie});const ge={ifd0:!1,ifd1:!1,exif:!1,gps:!1,interop:!1,sanitize:!1,reviveValues:!0,translateKeys:!1,translateValues:!1,mergeOutput:!1},me=Object.assign({},ge,{firstChunkSize:4e4,gps:[1,2,3,4]});async function Se(e){let t=new te(me);await t.read(e);let i=await t.parse();if(i&&i.gps){let{latitude:e,longitude:t}=i.gps;return{latitude:e,longitude:t}}}const Ce=Object.assign({},ge,{tiff:!1,ifd1:!0,mergeOutput:!1});async function ye(e){let t=new te(Ce);await t.read(e);let i=await t.extractThumbnail();return i&&a?s.from(i):i}async function be(e){let t=await this.thumbnail(e);if(void 0!==t){let e=new Blob([t]);return URL.createObjectURL(e)}}const Ie=Object.assign({},ge,{firstChunkSize:4e4,ifd0:[274]});async function Pe(e){let t=new te(Ie);await t.read(e);let i=await t.parse();if(i&&i.ifd0)return i.ifd0[274]}const ke=Object.freeze({1:{dimensionSwapped:!1,scaleX:1,scaleY:1,deg:0,rad:0},2:{dimensionSwapped:!1,scaleX:-1,scaleY:1,deg:0,rad:0},3:{dimensionSwapped:!1,scaleX:1,scaleY:1,deg:180,rad:180*Math.PI/180},4:{dimensionSwapped:!1,scaleX:-1,scaleY:1,deg:180,rad:180*Math.PI/180},5:{dimensionSwapped:!0,scaleX:1,scaleY:-1,deg:90,rad:90*Math.PI/180},6:{dimensionSwapped:!0,scaleX:1,scaleY:1,deg:90,rad:90*Math.PI/180},7:{dimensionSwapped:!0,scaleX:1,scaleY:-1,deg:270,rad:270*Math.PI/180},8:{dimensionSwapped:!0,scaleX:1,scaleY:1,deg:270,rad:270*Math.PI/180}});let we=!0,Te=!0;if(\"object\"==typeof navigator){let e=navigator.userAgent;if(e.includes(\"iPad\")||e.includes(\"iPhone\")){let t=e.match(/OS (\\d+)_(\\d+)/);if(t){let[,e,i]=t,n=Number(e)+.1*Number(i);we=n<13.4,Te=!1}}else if(e.includes(\"OS X 10\")){let[,t]=e.match(/OS X 10[_.](\\d+)/);we=Te=Number(t)<15}if(e.includes(\"Chrome/\")){let[,t]=e.match(/Chrome\\/(\\d+)/);we=Te=Number(t)<81}else if(e.includes(\"Firefox/\")){let[,t]=e.match(/Firefox\\/(\\d+)/);we=Te=Number(t)<77}}async function Ae(e){let t=await Pe(e);return Object.assign({canvas:we,css:Te},ke[t])}class De extends I{constructor(...e){super(...e),c(this,\"ranges\",new Oe),0!==this.byteLength&&this.ranges.add(0,this.byteLength)}_tryExtend(e,t,i){if(0===e&&0===this.byteLength&&i){let e=new DataView(i.buffer||i,i.byteOffset,i.byteLength);this._swapDataView(e)}else{let i=e+t;if(i>this.byteLength){let{dataView:e}=this._extend(i);this._swapDataView(e)}}}_extend(e){let t;t=a?s.allocUnsafe(e):new Uint8Array(e);let i=new DataView(t.buffer,t.byteOffset,t.byteLength);return t.set(new Uint8Array(this.buffer,this.byteOffset,this.byteLength),0),{uintView:t,dataView:i}}subarray(e,t,i=!1){return t=t||this._lengthToEnd(e),i&&this._tryExtend(e,t),this.ranges.add(e,t),super.subarray(e,t)}set(e,t,i=!1){i&&this._tryExtend(t,e.byteLength,e);let n=super.set(e,t);return this.ranges.add(t,n.byteLength),n}async ensureChunk(e,t){this.chunked&&(this.ranges.available(e,t)||await this.readChunk(e,t))}available(e,t){return this.ranges.available(e,t)}}class Oe{constructor(){c(this,\"list\",[])}get length(){return this.list.length}add(e,t,i=0){let n=e+t,s=this.list.filter((t=>xe(e,t.offset,n)||xe(e,t.end,n)));if(s.length>0){e=Math.min(e,...s.map((e=>e.offset))),n=Math.max(n,...s.map((e=>e.end))),t=n-e;let i=s.shift();i.offset=e,i.length=t,i.end=n,this.list=this.list.filter((e=>!s.includes(e)))}else this.list.push({offset:e,length:t,end:n})}available(e,t){let i=e+t;return this.list.some((t=>t.offset<=e&&i<=t.end))}}function xe(e,t,i){return e<=t&&t<=i}class ve extends De{constructor(e,t){super(0),c(this,\"chunksRead\",0),this.input=e,this.options=t}async readWhole(){this.chunked=!1,await this.readChunk(this.nextChunkOffset)}async readChunked(){this.chunked=!0,await this.readChunk(0,this.options.firstChunkSize)}async readNextChunk(e=this.nextChunkOffset){if(this.fullyRead)return this.chunksRead++,!1;let t=this.options.chunkSize,i=await this.readChunk(e,t);return!!i&&i.byteLength===t}async readChunk(e,t){if(this.chunksRead++,0!==(t=this.safeWrapAddress(e,t)))return this._readChunk(e,t)}safeWrapAddress(e,t){return void 0!==this.size&&e+t>this.size?Math.max(0,this.size-e):t}get nextChunkOffset(){if(0!==this.ranges.list.length)return this.ranges.list[0].length}get canReadNextChunk(){return this.chunksRead<this.options.chunkLimit}get fullyRead(){return void 0!==this.size&&this.nextChunkOffset===this.size}read(){return this.options.chunked?this.readChunked():this.readWhole()}close(){}}A.set(\"blob\",class extends ve{async readWhole(){this.chunked=!1;let e=await R(this.input);this._swapArrayBuffer(e)}readChunked(){return this.chunked=!0,this.size=this.input.size,super.readChunked()}async _readChunk(e,t){let i=t?e+t:void 0,n=this.input.slice(e,i),s=await R(n);return this.set(s,e,!0)}});var Me=Object.freeze({__proto__:null,default:pe,Exifr:te,fileParsers:w,segmentParsers:T,fileReaders:A,tagKeys:E,tagValues:B,tagRevivers:N,createDictionary:U,extendDictionary:F,fetchUrlAsArrayBuffer:M,readBlobAsArrayBuffer:R,chunkedProps:G,otherSegments:V,segments:z,tiffBlocks:H,segmentsAndBlocks:j,tiffExtractables:W,inheritables:K,allFormatters:X,Options:q,parse:ie,gpsOnlyOptions:me,gps:Se,thumbnailOnlyOptions:Ce,thumbnail:ye,thumbnailUrl:be,orientationOnlyOptions:Ie,orientation:Pe,rotations:ke,get rotateCanvas(){return we},get rotateCss(){return Te},rotation:Ae});A.set(\"url\",class extends ve{async readWhole(){this.chunked=!1;let e=await M(this.input);e instanceof ArrayBuffer?this._swapArrayBuffer(e):e instanceof Uint8Array&&this._swapBuffer(e)}async _readChunk(e,t){let i=t?e+t-1:void 0,n=this.options.httpHeaders||{};(e||i)&&(n.range=`bytes=${[e,i].join(\"-\")}`);let s=await h(this.input,{headers:n}),r=await s.arrayBuffer(),a=r.byteLength;if(416!==s.status)return a!==t&&(this.size=e+a),this.set(r,e,!0)}});I.prototype.getUint64=function(e){let t=this.getUint32(e),i=this.getUint32(e+4);return t<1048575?t<<32|i:void 0!==typeof r?(console.warn(\"Using BigInt because of type 64uint but JS can only handle 53b numbers.\"),r(t)<<r(32)|r(i)):void g(\"Trying to read 64b value but JS can only handle 53b numbers.\")};class Re extends se{parseBoxes(e=0){let t=[];for(;e<this.file.byteLength-4;){let i=this.parseBoxHead(e);if(t.push(i),0===i.length)break;e+=i.length}return t}parseSubBoxes(e){e.boxes=this.parseBoxes(e.start)}findBox(e,t){return void 0===e.boxes&&this.parseSubBoxes(e),e.boxes.find((e=>e.kind===t))}parseBoxHead(e){let t=this.file.getUint32(e),i=this.file.getString(e+4,4),n=e+8;return 1===t&&(t=this.file.getUint64(e+8),n+=8),{offset:e,length:t,kind:i,start:n}}parseBoxFullHead(e){if(void 0!==e.version)return;let t=this.file.getUint32(e.start);e.version=t>>24,e.start+=4}}class Le extends Re{static canHandle(e,t){if(0!==t)return!1;let i=e.getUint16(2);if(i>50)return!1;let n=16,s=[];for(;n<i;)s.push(e.getString(n,4)),n+=4;return s.includes(this.type)}async parse(){let e=this.file.getUint32(0),t=this.parseBoxHead(e);for(;\"meta\"!==t.kind;)e+=t.length,await this.file.ensureChunk(e,16),t=this.parseBoxHead(e);await this.file.ensureChunk(t.offset,t.length),this.parseBoxFullHead(t),this.parseSubBoxes(t),this.options.icc.enabled&&await this.findIcc(t),this.options.tiff.enabled&&await this.findExif(t)}async registerSegment(e,t,i){await this.file.ensureChunk(t,i);let n=this.file.subarray(t,i);this.createParser(e,n)}async findIcc(e){let t=this.findBox(e,\"iprp\");if(void 0===t)return;let i=this.findBox(t,\"ipco\");if(void 0===i)return;let n=this.findBox(i,\"colr\");void 0!==n&&await this.registerSegment(\"icc\",n.offset+12,n.length)}async findExif(e){let t=this.findBox(e,\"iinf\");if(void 0===t)return;let i=this.findBox(e,\"iloc\");if(void 0===i)return;let n=this.findExifLocIdInIinf(t),s=this.findExtentInIloc(i,n);if(void 0===s)return;let[r,a]=s;await this.file.ensureChunk(r,a);let o=4+this.file.getUint32(r);r+=o,a-=o,await this.registerSegment(\"tiff\",r,a)}findExifLocIdInIinf(e){this.parseBoxFullHead(e);let t,i,n,s,r=e.start,a=this.file.getUint16(r);for(r+=2;a--;){if(t=this.parseBoxHead(r),this.parseBoxFullHead(t),i=t.start,t.version>=2&&(n=3===t.version?4:2,s=this.file.getString(i+n+2,4),\"Exif\"===s))return this.file.getUintBytes(i,n);r+=t.length}}get8bits(e){let t=this.file.getUint8(e);return[t>>4,15&t]}findExtentInIloc(e,t){this.parseBoxFullHead(e);let i=e.start,[n,s]=this.get8bits(i++),[r,a]=this.get8bits(i++),o=2===e.version?4:2,l=1===e.version||2===e.version?2:0,h=a+n+s,u=2===e.version?4:2,c=this.file.getUintBytes(i,u);for(i+=u;c--;){let e=this.file.getUintBytes(i,o);i+=o+l+2+r;let u=this.file.getUint16(i);if(i+=2,e===t)return u>1&&console.warn(\"ILOC box has more than one extent but we're only processing one\\nPlease create an issue at https://github.com/MikeKovarik/exifr with this file\"),[this.file.getUintBytes(i+a,n),this.file.getUintBytes(i+a+n,s)];i+=u*h}}}class Ue extends Le{}c(Ue,\"type\",\"heic\");class Fe extends Le{}c(Fe,\"type\",\"avif\"),w.set(\"heic\",Ue),w.set(\"avif\",Fe),U(E,[\"ifd0\",\"ifd1\"],[[256,\"ImageWidth\"],[257,\"ImageHeight\"],[258,\"BitsPerSample\"],[259,\"Compression\"],[262,\"PhotometricInterpretation\"],[270,\"ImageDescription\"],[271,\"Make\"],[272,\"Model\"],[273,\"StripOffsets\"],[274,\"Orientation\"],[277,\"SamplesPerPixel\"],[278,\"RowsPerStrip\"],[279,\"StripByteCounts\"],[282,\"XResolution\"],[283,\"YResolution\"],[284,\"PlanarConfiguration\"],[296,\"ResolutionUnit\"],[301,\"TransferFunction\"],[305,\"Software\"],[306,\"ModifyDate\"],[315,\"Artist\"],[316,\"HostComputer\"],[317,\"Predictor\"],[318,\"WhitePoint\"],[319,\"PrimaryChromaticities\"],[513,\"ThumbnailOffset\"],[514,\"ThumbnailLength\"],[529,\"YCbCrCoefficients\"],[530,\"YCbCrSubSampling\"],[531,\"YCbCrPositioning\"],[532,\"ReferenceBlackWhite\"],[700,\"ApplicationNotes\"],[33432,\"Copyright\"],[33723,\"IPTC\"],[34665,\"ExifIFD\"],[34675,\"ICC\"],[34853,\"GpsIFD\"],[330,\"SubIFD\"],[40965,\"InteropIFD\"],[40091,\"XPTitle\"],[40092,\"XPComment\"],[40093,\"XPAuthor\"],[40094,\"XPKeywords\"],[40095,\"XPSubject\"]]),U(E,\"exif\",[[33434,\"ExposureTime\"],[33437,\"FNumber\"],[34850,\"ExposureProgram\"],[34852,\"SpectralSensitivity\"],[34855,\"ISO\"],[34858,\"TimeZoneOffset\"],[34859,\"SelfTimerMode\"],[34864,\"SensitivityType\"],[34865,\"StandardOutputSensitivity\"],[34866,\"RecommendedExposureIndex\"],[34867,\"ISOSpeed\"],[34868,\"ISOSpeedLatitudeyyy\"],[34869,\"ISOSpeedLatitudezzz\"],[36864,\"ExifVersion\"],[36867,\"DateTimeOriginal\"],[36868,\"CreateDate\"],[36873,\"GooglePlusUploadCode\"],[36880,\"OffsetTime\"],[36881,\"OffsetTimeOriginal\"],[36882,\"OffsetTimeDigitized\"],[37121,\"ComponentsConfiguration\"],[37122,\"CompressedBitsPerPixel\"],[37377,\"ShutterSpeedValue\"],[37378,\"ApertureValue\"],[37379,\"BrightnessValue\"],[37380,\"ExposureCompensation\"],[37381,\"MaxApertureValue\"],[37382,\"SubjectDistance\"],[37383,\"MeteringMode\"],[37384,\"LightSource\"],[37385,\"Flash\"],[37386,\"FocalLength\"],[37393,\"ImageNumber\"],[37394,\"SecurityClassification\"],[37395,\"ImageHistory\"],[37396,\"SubjectArea\"],[37500,\"MakerNote\"],[37510,\"UserComment\"],[37520,\"SubSecTime\"],[37521,\"SubSecTimeOriginal\"],[37522,\"SubSecTimeDigitized\"],[37888,\"AmbientTemperature\"],[37889,\"Humidity\"],[37890,\"Pressure\"],[37891,\"WaterDepth\"],[37892,\"Acceleration\"],[37893,\"CameraElevationAngle\"],[40960,\"FlashpixVersion\"],[40961,\"ColorSpace\"],[40962,\"ExifImageWidth\"],[40963,\"ExifImageHeight\"],[40964,\"RelatedSoundFile\"],[41483,\"FlashEnergy\"],[41486,\"FocalPlaneXResolution\"],[41487,\"FocalPlaneYResolution\"],[41488,\"FocalPlaneResolutionUnit\"],[41492,\"SubjectLocation\"],[41493,\"ExposureIndex\"],[41495,\"SensingMethod\"],[41728,\"FileSource\"],[41729,\"SceneType\"],[41730,\"CFAPattern\"],[41985,\"CustomRendered\"],[41986,\"ExposureMode\"],[41987,\"WhiteBalance\"],[41988,\"DigitalZoomRatio\"],[41989,\"FocalLengthIn35mmFormat\"],[41990,\"SceneCaptureType\"],[41991,\"GainControl\"],[41992,\"Contrast\"],[41993,\"Saturation\"],[41994,\"Sharpness\"],[41996,\"SubjectDistanceRange\"],[42016,\"ImageUniqueID\"],[42032,\"OwnerName\"],[42033,\"SerialNumber\"],[42034,\"LensInfo\"],[42035,\"LensMake\"],[42036,\"LensModel\"],[42037,\"LensSerialNumber\"],[42080,\"CompositeImage\"],[42081,\"CompositeImageCount\"],[42082,\"CompositeImageExposureTimes\"],[42240,\"Gamma\"],[59932,\"Padding\"],[59933,\"OffsetSchema\"],[65e3,\"OwnerName\"],[65001,\"SerialNumber\"],[65002,\"Lens\"],[65100,\"RawFile\"],[65101,\"Converter\"],[65102,\"WhiteBalance\"],[65105,\"Exposure\"],[65106,\"Shadows\"],[65107,\"Brightness\"],[65108,\"Contrast\"],[65109,\"Saturation\"],[65110,\"Sharpness\"],[65111,\"Smoothness\"],[65112,\"MoireFilter\"],[40965,\"InteropIFD\"]]),U(E,\"gps\",[[0,\"GPSVersionID\"],[1,\"GPSLatitudeRef\"],[2,\"GPSLatitude\"],[3,\"GPSLongitudeRef\"],[4,\"GPSLongitude\"],[5,\"GPSAltitudeRef\"],[6,\"GPSAltitude\"],[7,\"GPSTimeStamp\"],[8,\"GPSSatellites\"],[9,\"GPSStatus\"],[10,\"GPSMeasureMode\"],[11,\"GPSDOP\"],[12,\"GPSSpeedRef\"],[13,\"GPSSpeed\"],[14,\"GPSTrackRef\"],[15,\"GPSTrack\"],[16,\"GPSImgDirectionRef\"],[17,\"GPSImgDirection\"],[18,\"GPSMapDatum\"],[19,\"GPSDestLatitudeRef\"],[20,\"GPSDestLatitude\"],[21,\"GPSDestLongitudeRef\"],[22,\"GPSDestLongitude\"],[23,\"GPSDestBearingRef\"],[24,\"GPSDestBearing\"],[25,\"GPSDestDistanceRef\"],[26,\"GPSDestDistance\"],[27,\"GPSProcessingMethod\"],[28,\"GPSAreaInformation\"],[29,\"GPSDateStamp\"],[30,\"GPSDifferential\"],[31,\"GPSHPositioningError\"]]),U(B,[\"ifd0\",\"ifd1\"],[[274,{1:\"Horizontal (normal)\",2:\"Mirror horizontal\",3:\"Rotate 180\",4:\"Mirror vertical\",5:\"Mirror horizontal and rotate 270 CW\",6:\"Rotate 90 CW\",7:\"Mirror horizontal and rotate 90 CW\",8:\"Rotate 270 CW\"}],[296,{1:\"None\",2:\"inches\",3:\"cm\"}]]);let Ee=U(B,\"exif\",[[34850,{0:\"Not defined\",1:\"Manual\",2:\"Normal program\",3:\"Aperture priority\",4:\"Shutter priority\",5:\"Creative program\",6:\"Action program\",7:\"Portrait mode\",8:\"Landscape mode\"}],[37121,{0:\"-\",1:\"Y\",2:\"Cb\",3:\"Cr\",4:\"R\",5:\"G\",6:\"B\"}],[37383,{0:\"Unknown\",1:\"Average\",2:\"CenterWeightedAverage\",3:\"Spot\",4:\"MultiSpot\",5:\"Pattern\",6:\"Partial\",255:\"Other\"}],[37384,{0:\"Unknown\",1:\"Daylight\",2:\"Fluorescent\",3:\"Tungsten (incandescent light)\",4:\"Flash\",9:\"Fine weather\",10:\"Cloudy weather\",11:\"Shade\",12:\"Daylight fluorescent (D 5700 - 7100K)\",13:\"Day white fluorescent (N 4600 - 5400K)\",14:\"Cool white fluorescent (W 3900 - 4500K)\",15:\"White fluorescent (WW 3200 - 3700K)\",17:\"Standard light A\",18:\"Standard light B\",19:\"Standard light C\",20:\"D55\",21:\"D65\",22:\"D75\",23:\"D50\",24:\"ISO studio tungsten\",255:\"Other\"}],[37385,{0:\"Flash did not fire\",1:\"Flash fired\",5:\"Strobe return light not detected\",7:\"Strobe return light detected\",9:\"Flash fired, compulsory flash mode\",13:\"Flash fired, compulsory flash mode, return light not detected\",15:\"Flash fired, compulsory flash mode, return light detected\",16:\"Flash did not fire, compulsory flash mode\",24:\"Flash did not fire, auto mode\",25:\"Flash fired, auto mode\",29:\"Flash fired, auto mode, return light not detected\",31:\"Flash fired, auto mode, return light detected\",32:\"No flash function\",65:\"Flash fired, red-eye reduction mode\",69:\"Flash fired, red-eye reduction mode, return light not detected\",71:\"Flash fired, red-eye reduction mode, return light detected\",73:\"Flash fired, compulsory flash mode, red-eye reduction mode\",77:\"Flash fired, compulsory flash mode, red-eye reduction mode, return light not detected\",79:\"Flash fired, compulsory flash mode, red-eye reduction mode, return light detected\",89:\"Flash fired, auto mode, red-eye reduction mode\",93:\"Flash fired, auto mode, return light not detected, red-eye reduction mode\",95:\"Flash fired, auto mode, return light detected, red-eye reduction mode\"}],[41495,{1:\"Not defined\",2:\"One-chip color area sensor\",3:\"Two-chip color area sensor\",4:\"Three-chip color area sensor\",5:\"Color sequential area sensor\",7:\"Trilinear sensor\",8:\"Color sequential linear sensor\"}],[41728,{1:\"Film Scanner\",2:\"Reflection Print Scanner\",3:\"Digital Camera\"}],[41729,{1:\"Directly photographed\"}],[41985,{0:\"Normal\",1:\"Custom\",2:\"HDR (no original saved)\",3:\"HDR (original saved)\",4:\"Original (for HDR)\",6:\"Panorama\",7:\"Portrait HDR\",8:\"Portrait\"}],[41986,{0:\"Auto\",1:\"Manual\",2:\"Auto bracket\"}],[41987,{0:\"Auto\",1:\"Manual\"}],[41990,{0:\"Standard\",1:\"Landscape\",2:\"Portrait\",3:\"Night\",4:\"Other\"}],[41991,{0:\"None\",1:\"Low gain up\",2:\"High gain up\",3:\"Low gain down\",4:\"High gain down\"}],[41996,{0:\"Unknown\",1:\"Macro\",2:\"Close\",3:\"Distant\"}],[42080,{0:\"Unknown\",1:\"Not a Composite Image\",2:\"General Composite Image\",3:\"Composite Image Captured While Shooting\"}]]);const Be={1:\"No absolute unit of measurement\",2:\"Inch\",3:\"Centimeter\"};Ee.set(37392,Be),Ee.set(41488,Be);const Ne={0:\"Normal\",1:\"Low\",2:\"High\"};function Ge(e){return\"object\"==typeof e&&void 0!==e.length?e[0]:e}function Ve(e){let t=Array.from(e).slice(1);return t[1]>15&&(t=t.map((e=>String.fromCharCode(e)))),\"0\"!==t[2]&&0!==t[2]||t.pop(),t.join(\".\")}function ze(e){if(\"string\"==typeof e){var[t,i,n,s,r,a]=e.trim().split(/[-: ]/g).map(Number),o=new Date(t,i-1,n);return Number.isNaN(s)||Number.isNaN(r)||Number.isNaN(a)||(o.setHours(s),o.setMinutes(r),o.setSeconds(a)),Number.isNaN(+o)?e:o}}function He(e){if(\"string\"==typeof e)return e;let t=[];if(0===e[1]&&0===e[e.length-1])for(let i=0;i<e.length;i+=2)t.push(je(e[i+1],e[i]));else for(let i=0;i<e.length;i+=2)t.push(je(e[i],e[i+1]));return m(String.fromCodePoint(...t))}function je(e,t){return e<<8|t}Ee.set(41992,Ne),Ee.set(41993,Ne),Ee.set(41994,Ne),U(N,[\"ifd0\",\"ifd1\"],[[50827,function(e){return\"string\"!=typeof e?b(e):e}],[306,ze],[40091,He],[40092,He],[40093,He],[40094,He],[40095,He]]),U(N,\"exif\",[[40960,Ve],[36864,Ve],[36867,ze],[36868,ze],[40962,Ge],[40963,Ge]]),U(N,\"gps\",[[0,e=>Array.from(e).join(\".\")],[7,e=>Array.from(e).join(\":\")]]);class We extends re{static canHandle(e,t){return 225===e.getUint8(t+1)&&1752462448===e.getUint32(t+4)&&\"http://ns.adobe.com/\"===e.getString(t+4,\"http://ns.adobe.com/\".length)}static headerLength(e,t){return\"http://ns.adobe.com/xmp/extension/\"===e.getString(t+4,\"http://ns.adobe.com/xmp/extension/\".length)?79:4+\"http://ns.adobe.com/xap/1.0/\".length+1}static findPosition(e,t){let i=super.findPosition(e,t);return i.multiSegment=i.extended=79===i.headerLength,i.multiSegment?(i.chunkCount=e.getUint8(t+72),i.chunkNumber=e.getUint8(t+76),0!==e.getUint8(t+77)&&i.chunkNumber++):(i.chunkCount=1/0,i.chunkNumber=-1),i}static handleMultiSegments(e){return e.map((e=>e.chunk.getString())).join(\"\")}normalizeInput(e){return\"string\"==typeof e?e:I.from(e).getString()}parse(e=this.chunk){if(!this.localOptions.parse)return e;e=function(e){let t={},i={};for(let e of Ze)t[e]=[],i[e]=0;return e.replace(et,((e,n,s)=>{if(\"<\"===n){let n=++i[s];return t[s].push(n),`${e}#${n}`}return`${e}#${t[s].pop()}`}))}(e);let t=Xe.findAll(e,\"rdf\",\"Description\");0===t.length&&t.push(new Xe(\"rdf\",\"Description\",void 0,e));let i,n={};for(let e of t)for(let t of e.properties)i=Je(t.ns,n),_e(t,i);return function(e){let t;for(let i in e)t=e[i]=f(e[i]),void 0===t&&delete e[i];return f(e)}(n)}assignToOutput(e,t){if(this.localOptions.parse)for(let[i,n]of Object.entries(t))switch(i){case\"tiff\":this.assignObjectToOutput(e,\"ifd0\",n);break;case\"exif\":this.assignObjectToOutput(e,\"exif\",n);break;case\"xmlns\":break;default:this.assignObjectToOutput(e,i,n)}else e.xmp=t}}c(We,\"type\",\"xmp\"),c(We,\"multiSegment\",!0),T.set(\"xmp\",We);class Ke{static findAll(e){return qe(e,/([a-zA-Z0-9-]+):([a-zA-Z0-9-]+)=(\"[^\"]*\"|'[^']*')/gm).map(Ke.unpackMatch)}static unpackMatch(e){let t=e[1],i=e[2],n=e[3].slice(1,-1);return n=Qe(n),new Ke(t,i,n)}constructor(e,t,i){this.ns=e,this.name=t,this.value=i}serialize(){return this.value}}class Xe{static findAll(e,t,i){if(void 0!==t||void 0!==i){t=t||\"[\\\\w\\\\d-]+\",i=i||\"[\\\\w\\\\d-]+\";var n=new RegExp(`<(${t}):(${i})(#\\\\d+)?((\\\\s+?[\\\\w\\\\d-:]+=(\"[^\"]*\"|'[^']*'))*\\\\s*)(\\\\/>|>([\\\\s\\\\S]*?)<\\\\/\\\\1:\\\\2\\\\3>)`,\"gm\")}else n=/<([\\w\\d-]+):([\\w\\d-]+)(#\\d+)?((\\s+?[\\w\\d-:]+=(\"[^\"]*\"|'[^']*'))*\\s*)(\\/>|>([\\s\\S]*?)<\\/\\1:\\2\\3>)/gm;return qe(e,n).map(Xe.unpackMatch)}static unpackMatch(e){let t=e[1],i=e[2],n=e[4],s=e[8];return new Xe(t,i,n,s)}constructor(e,t,i,n){this.ns=e,this.name=t,this.attrString=i,this.innerXml=n,this.attrs=Ke.findAll(i),this.children=Xe.findAll(n),this.value=0===this.children.length?Qe(n):void 0,this.properties=[...this.attrs,...this.children]}get isPrimitive(){return void 0!==this.value&&0===this.attrs.length&&0===this.children.length}get isListContainer(){return 1===this.children.length&&this.children[0].isList}get isList(){let{ns:e,name:t}=this;return\"rdf\"===e&&(\"Seq\"===t||\"Bag\"===t||\"Alt\"===t)}get isListItem(){return\"rdf\"===this.ns&&\"li\"===this.name}serialize(){if(0===this.properties.length&&void 0===this.value)return;if(this.isPrimitive)return this.value;if(this.isListContainer)return this.children[0].serialize();if(this.isList)return $e(this.children.map(Ye));if(this.isListItem&&1===this.children.length&&0===this.attrs.length)return this.children[0].serialize();let e={};for(let t of this.properties)_e(t,e);return void 0!==this.value&&(e.value=this.value),f(e)}}function _e(e,t){let i=e.serialize();void 0!==i&&(t[e.name]=i)}var Ye=e=>e.serialize(),$e=e=>1===e.length?e[0]:e,Je=(e,t)=>t[e]?t[e]:t[e]={};function qe(e,t){let i,n=[];if(!e)return n;for(;null!==(i=t.exec(e));)n.push(i);return n}function Qe(e){if(function(e){return null==e||\"null\"===e||\"undefined\"===e||\"\"===e||\"\"===e.trim()}(e))return;let t=Number(e);if(!Number.isNaN(t))return t;let i=e.toLowerCase();return\"true\"===i||\"false\"!==i&&e.trim()}const Ze=[\"rdf:li\",\"rdf:Seq\",\"rdf:Bag\",\"rdf:Alt\",\"rdf:Description\"],et=new RegExp(`(<|\\\\/)(${Ze.join(\"|\")})`,\"g\");var tt=Object.freeze({__proto__:null,default:Me,Exifr:te,fileParsers:w,segmentParsers:T,fileReaders:A,tagKeys:E,tagValues:B,tagRevivers:N,createDictionary:U,extendDictionary:F,fetchUrlAsArrayBuffer:M,readBlobAsArrayBuffer:R,chunkedProps:G,otherSegments:V,segments:z,tiffBlocks:H,segmentsAndBlocks:j,tiffExtractables:W,inheritables:K,allFormatters:X,Options:q,parse:ie,gpsOnlyOptions:me,gps:Se,thumbnailOnlyOptions:Ce,thumbnail:ye,thumbnailUrl:be,orientationOnlyOptions:Ie,orientation:Pe,rotations:ke,get rotateCanvas(){return we},get rotateCss(){return Te},rotation:Ae});const it=[\"xmp\",\"icc\",\"iptc\",\"tiff\"],nt=()=>{};async function st(e,t,i){let n=new q(t);n.chunked=!1,void 0===i&&\"string\"==typeof e&&(i=function(e){let t=e.toLowerCase().split(\".\").pop();if(function(e){return\"exif\"===e||\"tiff\"===e||\"tif\"===e}(t))return\"tiff\";if(it.includes(t))return t}(e));let s=await D(e,n);if(i){if(it.includes(i))return rt(i,s,n);g(\"Invalid segment type\")}else{if(function(e){let t=e.getString(0,50).trim();return t.includes(\"<?xpacket\")||t.includes(\"<x:\")}(s))return rt(\"xmp\",s,n);for(let[e]of T){if(!it.includes(e))continue;let t=await rt(e,s,n).catch(nt);if(t)return t}g(\"Unknown file format\")}}async function rt(e,t,i){let n=i[e];return n.enabled=!0,n.parse=!0,T.get(e).parse(t,n)}let at=l(\"fs\",(e=>e.promises));A.set(\"fs\",class extends ve{async readWhole(){this.chunked=!1,this.fs=await at;let e=await this.fs.readFile(this.input);this._swapBuffer(e)}async readChunked(){this.chunked=!0,this.fs=await at,await this.open(),await this.readChunk(0,this.options.firstChunkSize)}async open(){void 0===this.fh&&(this.fh=await this.fs.open(this.input,\"r\"),this.size=(await this.fh.stat(this.input)).size)}async _readChunk(e,t){void 0===this.fh&&await this.open(),e+t>this.size&&(t=this.size-e);var i=this.subarray(e,t,!0);return await this.fh.read(i.dataView,0,t,e),i}async close(){if(this.fh){let e=this.fh;this.fh=void 0,await e.close()}}});A.set(\"base64\",class extends ve{constructor(...e){super(...e),this.input=this.input.replace(/^data:([^;]+);base64,/gim,\"\"),this.size=this.input.length/4*3,this.input.endsWith(\"==\")?this.size-=2:this.input.endsWith(\"=\")&&(this.size-=1)}async _readChunk(e,t){let i,n,r=this.input;void 0===e?(e=0,i=0,n=0):(i=4*Math.floor(e/3),n=e-i/4*3),void 0===t&&(t=this.size);let o=e+t,l=i+4*Math.ceil(o/3);r=r.slice(i,l);let h=Math.min(t,this.size-e);if(a){let t=s.from(r,\"base64\").slice(n,n+h);return this.set(t,e,!0)}{let t=this.subarray(e,h,!0),i=atob(r),s=t.toUint8();for(let e=0;e<h;e++)s[e]=i.charCodeAt(n+e);return t}}});class ot extends se{static canHandle(e,t){return 18761===t||19789===t}extendOptions(e){let{ifd0:t,xmp:i,iptc:n,icc:s}=e;i.enabled&&t.deps.add(700),n.enabled&&t.deps.add(33723),s.enabled&&t.deps.add(34675),t.finalizeFilters()}async parse(){let{tiff:e,xmp:t,iptc:i,icc:n}=this.options;if(e.enabled||t.enabled||i.enabled||n.enabled){let e=Math.max(S(this.options),this.options.chunkSize);await this.file.ensureChunk(0,e),this.createParser(\"tiff\",this.file),this.parsers.tiff.parseHeader(),await this.parsers.tiff.parseIfd0Block(),this.adaptTiffPropAsSegment(\"xmp\"),this.adaptTiffPropAsSegment(\"iptc\"),this.adaptTiffPropAsSegment(\"icc\")}}adaptTiffPropAsSegment(e){if(this.parsers.tiff[e]){let t=this.parsers.tiff[e];this.injectSegment(e,t)}}}c(ot,\"type\",\"tiff\"),w.set(\"tiff\",ot);let lt=l(\"zlib\");const ht=[\"ihdr\",\"iccp\",\"text\",\"itxt\",\"exif\"];class ut extends se{constructor(...e){super(...e),c(this,\"catchError\",(e=>this.errors.push(e))),c(this,\"metaChunks\",[]),c(this,\"unknownChunks\",[])}static canHandle(e,t){return 35152===t&&2303741511===e.getUint32(0)&&218765834===e.getUint32(4)}async parse(){let{file:e}=this;await this.findPngChunksInRange(\"PNG\\r\\n\u001a\\n\".length,e.byteLength),await this.readSegments(this.metaChunks),this.findIhdr(),this.parseTextChunks(),await this.findExif().catch(this.catchError),await this.findXmp().catch(this.catchError),await this.findIcc().catch(this.catchError)}async findPngChunksInRange(e,t){let{file:i}=this;for(;e<t;){let t=i.getUint32(e),n=i.getUint32(e+4),s=i.getString(e+4,4).toLowerCase(),r=t+4+4+4,a={type:s,offset:e,length:r,start:e+4+4,size:t,marker:n};ht.includes(s)?this.metaChunks.push(a):this.unknownChunks.push(a),e+=r}}parseTextChunks(){let e=this.metaChunks.filter((e=>\"text\"===e.type));for(let t of e){let[e,i]=this.file.getString(t.start,t.size).split(\"\\0\");this.injectKeyValToIhdr(e,i)}}injectKeyValToIhdr(e,t){let i=this.parsers.ihdr;i&&i.raw.set(e,t)}findIhdr(){let e=this.metaChunks.find((e=>\"ihdr\"===e.type));e&&!1!==this.options.ihdr.enabled&&this.createParser(\"ihdr\",e.chunk)}async findExif(){let e=this.metaChunks.find((e=>\"exif\"===e.type));e&&this.injectSegment(\"tiff\",e.chunk)}async findXmp(){let e=this.metaChunks.filter((e=>\"itxt\"===e.type));for(let t of e){\"XML:com.adobe.xmp\"===t.chunk.getString(0,\"XML:com.adobe.xmp\".length)&&this.injectSegment(\"xmp\",t.chunk)}}async findIcc(){let e=this.metaChunks.find((e=>\"iccp\"===e.type));if(!e)return;let{chunk:t}=e,i=t.getUint8Array(0,81),s=0;for(;s<80&&0!==i[s];)s++;let r=s+2,a=t.getString(0,s);if(this.injectKeyValToIhdr(\"ProfileName\",a),n){let e=await lt,i=t.getUint8Array(r);i=e.inflateSync(i),this.injectSegment(\"icc\",i)}}}c(ut,\"type\",\"png\"),w.set(\"png\",ut),U(E,\"interop\",[[1,\"InteropIndex\"],[2,\"InteropVersion\"],[4096,\"RelatedImageFileFormat\"],[4097,\"RelatedImageWidth\"],[4098,\"RelatedImageHeight\"]]),F(E,\"ifd0\",[[11,\"ProcessingSoftware\"],[254,\"SubfileType\"],[255,\"OldSubfileType\"],[263,\"Thresholding\"],[264,\"CellWidth\"],[265,\"CellLength\"],[266,\"FillOrder\"],[269,\"DocumentName\"],[280,\"MinSampleValue\"],[281,\"MaxSampleValue\"],[285,\"PageName\"],[286,\"XPosition\"],[287,\"YPosition\"],[290,\"GrayResponseUnit\"],[297,\"PageNumber\"],[321,\"HalftoneHints\"],[322,\"TileWidth\"],[323,\"TileLength\"],[332,\"InkSet\"],[337,\"TargetPrinter\"],[18246,\"Rating\"],[18249,\"RatingPercent\"],[33550,\"PixelScale\"],[34264,\"ModelTransform\"],[34377,\"PhotoshopSettings\"],[50706,\"DNGVersion\"],[50707,\"DNGBackwardVersion\"],[50708,\"UniqueCameraModel\"],[50709,\"LocalizedCameraModel\"],[50736,\"DNGLensInfo\"],[50739,\"ShadowScale\"],[50740,\"DNGPrivateData\"],[33920,\"IntergraphMatrix\"],[33922,\"ModelTiePoint\"],[34118,\"SEMInfo\"],[34735,\"GeoTiffDirectory\"],[34736,\"GeoTiffDoubleParams\"],[34737,\"GeoTiffAsciiParams\"],[50341,\"PrintIM\"],[50721,\"ColorMatrix1\"],[50722,\"ColorMatrix2\"],[50723,\"CameraCalibration1\"],[50724,\"CameraCalibration2\"],[50725,\"ReductionMatrix1\"],[50726,\"ReductionMatrix2\"],[50727,\"AnalogBalance\"],[50728,\"AsShotNeutral\"],[50729,\"AsShotWhiteXY\"],[50730,\"BaselineExposure\"],[50731,\"BaselineNoise\"],[50732,\"BaselineSharpness\"],[50734,\"LinearResponseLimit\"],[50735,\"CameraSerialNumber\"],[50741,\"MakerNoteSafety\"],[50778,\"CalibrationIlluminant1\"],[50779,\"CalibrationIlluminant2\"],[50781,\"RawDataUniqueID\"],[50827,\"OriginalRawFileName\"],[50828,\"OriginalRawFileData\"],[50831,\"AsShotICCProfile\"],[50832,\"AsShotPreProfileMatrix\"],[50833,\"CurrentICCProfile\"],[50834,\"CurrentPreProfileMatrix\"],[50879,\"ColorimetricReference\"],[50885,\"SRawType\"],[50898,\"PanasonicTitle\"],[50899,\"PanasonicTitle2\"],[50931,\"CameraCalibrationSig\"],[50932,\"ProfileCalibrationSig\"],[50933,\"ProfileIFD\"],[50934,\"AsShotProfileName\"],[50936,\"ProfileName\"],[50937,\"ProfileHueSatMapDims\"],[50938,\"ProfileHueSatMapData1\"],[50939,\"ProfileHueSatMapData2\"],[50940,\"ProfileToneCurve\"],[50941,\"ProfileEmbedPolicy\"],[50942,\"ProfileCopyright\"],[50964,\"ForwardMatrix1\"],[50965,\"ForwardMatrix2\"],[50966,\"PreviewApplicationName\"],[50967,\"PreviewApplicationVersion\"],[50968,\"PreviewSettingsName\"],[50969,\"PreviewSettingsDigest\"],[50970,\"PreviewColorSpace\"],[50971,\"PreviewDateTime\"],[50972,\"RawImageDigest\"],[50973,\"OriginalRawFileDigest\"],[50981,\"ProfileLookTableDims\"],[50982,\"ProfileLookTableData\"],[51043,\"TimeCodes\"],[51044,\"FrameRate\"],[51058,\"TStop\"],[51081,\"ReelName\"],[51089,\"OriginalDefaultFinalSize\"],[51090,\"OriginalBestQualitySize\"],[51091,\"OriginalDefaultCropSize\"],[51105,\"CameraLabel\"],[51107,\"ProfileHueSatMapEncoding\"],[51108,\"ProfileLookTableEncoding\"],[51109,\"BaselineExposureOffset\"],[51110,\"DefaultBlackRender\"],[51111,\"NewRawImageDigest\"],[51112,\"RawToPreviewGain\"]]);let ct=[[273,\"StripOffsets\"],[279,\"StripByteCounts\"],[288,\"FreeOffsets\"],[289,\"FreeByteCounts\"],[291,\"GrayResponseCurve\"],[292,\"T4Options\"],[293,\"T6Options\"],[300,\"ColorResponseUnit\"],[320,\"ColorMap\"],[324,\"TileOffsets\"],[325,\"TileByteCounts\"],[326,\"BadFaxLines\"],[327,\"CleanFaxData\"],[328,\"ConsecutiveBadFaxLines\"],[330,\"SubIFD\"],[333,\"InkNames\"],[334,\"NumberofInks\"],[336,\"DotRange\"],[338,\"ExtraSamples\"],[339,\"SampleFormat\"],[340,\"SMinSampleValue\"],[341,\"SMaxSampleValue\"],[342,\"TransferRange\"],[343,\"ClipPath\"],[344,\"XClipPathUnits\"],[345,\"YClipPathUnits\"],[346,\"Indexed\"],[347,\"JPEGTables\"],[351,\"OPIProxy\"],[400,\"GlobalParametersIFD\"],[401,\"ProfileType\"],[402,\"FaxProfile\"],[403,\"CodingMethods\"],[404,\"VersionYear\"],[405,\"ModeNumber\"],[433,\"Decode\"],[434,\"DefaultImageColor\"],[435,\"T82Options\"],[437,\"JPEGTables\"],[512,\"JPEGProc\"],[515,\"JPEGRestartInterval\"],[517,\"JPEGLosslessPredictors\"],[518,\"JPEGPointTransforms\"],[519,\"JPEGQTables\"],[520,\"JPEGDCTables\"],[521,\"JPEGACTables\"],[559,\"StripRowCounts\"],[999,\"USPTOMiscellaneous\"],[18247,\"XP_DIP_XML\"],[18248,\"StitchInfo\"],[28672,\"SonyRawFileType\"],[28688,\"SonyToneCurve\"],[28721,\"VignettingCorrection\"],[28722,\"VignettingCorrParams\"],[28724,\"ChromaticAberrationCorrection\"],[28725,\"ChromaticAberrationCorrParams\"],[28726,\"DistortionCorrection\"],[28727,\"DistortionCorrParams\"],[29895,\"SonyCropTopLeft\"],[29896,\"SonyCropSize\"],[32781,\"ImageID\"],[32931,\"WangTag1\"],[32932,\"WangAnnotation\"],[32933,\"WangTag3\"],[32934,\"WangTag4\"],[32953,\"ImageReferencePoints\"],[32954,\"RegionXformTackPoint\"],[32955,\"WarpQuadrilateral\"],[32956,\"AffineTransformMat\"],[32995,\"Matteing\"],[32996,\"DataType\"],[32997,\"ImageDepth\"],[32998,\"TileDepth\"],[33300,\"ImageFullWidth\"],[33301,\"ImageFullHeight\"],[33302,\"TextureFormat\"],[33303,\"WrapModes\"],[33304,\"FovCot\"],[33305,\"MatrixWorldToScreen\"],[33306,\"MatrixWorldToCamera\"],[33405,\"Model2\"],[33421,\"CFARepeatPatternDim\"],[33422,\"CFAPattern2\"],[33423,\"BatteryLevel\"],[33424,\"KodakIFD\"],[33445,\"MDFileTag\"],[33446,\"MDScalePixel\"],[33447,\"MDColorTable\"],[33448,\"MDLabName\"],[33449,\"MDSampleInfo\"],[33450,\"MDPrepDate\"],[33451,\"MDPrepTime\"],[33452,\"MDFileUnits\"],[33589,\"AdventScale\"],[33590,\"AdventRevision\"],[33628,\"UIC1Tag\"],[33629,\"UIC2Tag\"],[33630,\"UIC3Tag\"],[33631,\"UIC4Tag\"],[33918,\"IntergraphPacketData\"],[33919,\"IntergraphFlagRegisters\"],[33921,\"INGRReserved\"],[34016,\"Site\"],[34017,\"ColorSequence\"],[34018,\"IT8Header\"],[34019,\"RasterPadding\"],[34020,\"BitsPerRunLength\"],[34021,\"BitsPerExtendedRunLength\"],[34022,\"ColorTable\"],[34023,\"ImageColorIndicator\"],[34024,\"BackgroundColorIndicator\"],[34025,\"ImageColorValue\"],[34026,\"BackgroundColorValue\"],[34027,\"PixelIntensityRange\"],[34028,\"TransparencyIndicator\"],[34029,\"ColorCharacterization\"],[34030,\"HCUsage\"],[34031,\"TrapIndicator\"],[34032,\"CMYKEquivalent\"],[34152,\"AFCP_IPTC\"],[34232,\"PixelMagicJBIGOptions\"],[34263,\"JPLCartoIFD\"],[34306,\"WB_GRGBLevels\"],[34310,\"LeafData\"],[34687,\"TIFF_FXExtensions\"],[34688,\"MultiProfiles\"],[34689,\"SharedData\"],[34690,\"T88Options\"],[34732,\"ImageLayer\"],[34750,\"JBIGOptions\"],[34856,\"Opto-ElectricConvFactor\"],[34857,\"Interlace\"],[34908,\"FaxRecvParams\"],[34909,\"FaxSubAddress\"],[34910,\"FaxRecvTime\"],[34929,\"FedexEDR\"],[34954,\"LeafSubIFD\"],[37387,\"FlashEnergy\"],[37388,\"SpatialFrequencyResponse\"],[37389,\"Noise\"],[37390,\"FocalPlaneXResolution\"],[37391,\"FocalPlaneYResolution\"],[37392,\"FocalPlaneResolutionUnit\"],[37397,\"ExposureIndex\"],[37398,\"TIFF-EPStandardID\"],[37399,\"SensingMethod\"],[37434,\"CIP3DataFile\"],[37435,\"CIP3Sheet\"],[37436,\"CIP3Side\"],[37439,\"StoNits\"],[37679,\"MSDocumentText\"],[37680,\"MSPropertySetStorage\"],[37681,\"MSDocumentTextPosition\"],[37724,\"ImageSourceData\"],[40965,\"InteropIFD\"],[40976,\"SamsungRawPointersOffset\"],[40977,\"SamsungRawPointersLength\"],[41217,\"SamsungRawByteOrder\"],[41218,\"SamsungRawUnknown\"],[41484,\"SpatialFrequencyResponse\"],[41485,\"Noise\"],[41489,\"ImageNumber\"],[41490,\"SecurityClassification\"],[41491,\"ImageHistory\"],[41494,\"TIFF-EPStandardID\"],[41995,\"DeviceSettingDescription\"],[42112,\"GDALMetadata\"],[42113,\"GDALNoData\"],[44992,\"ExpandSoftware\"],[44993,\"ExpandLens\"],[44994,\"ExpandFilm\"],[44995,\"ExpandFilterLens\"],[44996,\"ExpandScanner\"],[44997,\"ExpandFlashLamp\"],[46275,\"HasselbladRawImage\"],[48129,\"PixelFormat\"],[48130,\"Transformation\"],[48131,\"Uncompressed\"],[48132,\"ImageType\"],[48256,\"ImageWidth\"],[48257,\"ImageHeight\"],[48258,\"WidthResolution\"],[48259,\"HeightResolution\"],[48320,\"ImageOffset\"],[48321,\"ImageByteCount\"],[48322,\"AlphaOffset\"],[48323,\"AlphaByteCount\"],[48324,\"ImageDataDiscard\"],[48325,\"AlphaDataDiscard\"],[50215,\"OceScanjobDesc\"],[50216,\"OceApplicationSelector\"],[50217,\"OceIDNumber\"],[50218,\"OceImageLogic\"],[50255,\"Annotations\"],[50459,\"HasselbladExif\"],[50547,\"OriginalFileName\"],[50560,\"USPTOOriginalContentType\"],[50656,\"CR2CFAPattern\"],[50710,\"CFAPlaneColor\"],[50711,\"CFALayout\"],[50712,\"LinearizationTable\"],[50713,\"BlackLevelRepeatDim\"],[50714,\"BlackLevel\"],[50715,\"BlackLevelDeltaH\"],[50716,\"BlackLevelDeltaV\"],[50717,\"WhiteLevel\"],[50718,\"DefaultScale\"],[50719,\"DefaultCropOrigin\"],[50720,\"DefaultCropSize\"],[50733,\"BayerGreenSplit\"],[50737,\"ChromaBlurRadius\"],[50738,\"AntiAliasStrength\"],[50752,\"RawImageSegmentation\"],[50780,\"BestQualityScale\"],[50784,\"AliasLayerMetadata\"],[50829,\"ActiveArea\"],[50830,\"MaskedAreas\"],[50935,\"NoiseReductionApplied\"],[50974,\"SubTileBlockSize\"],[50975,\"RowInterleaveFactor\"],[51008,\"OpcodeList1\"],[51009,\"OpcodeList2\"],[51022,\"OpcodeList3\"],[51041,\"NoiseProfile\"],[51114,\"CacheVersion\"],[51125,\"DefaultUserCrop\"],[51157,\"NikonNEFInfo\"],[65024,\"KdcIFD\"]];F(E,\"ifd0\",ct),F(E,\"exif\",ct),U(B,\"gps\",[[23,{M:\"Magnetic North\",T:\"True North\"}],[25,{K:\"Kilometers\",M:\"Miles\",N:\"Nautical Miles\"}]]);class ft extends re{static canHandle(e,t){return 224===e.getUint8(t+1)&&1246120262===e.getUint32(t+4)&&0===e.getUint8(t+8)}parse(){return this.parseTags(),this.translate(),this.output}parseTags(){this.raw=new Map([[0,this.chunk.getUint16(0)],[2,this.chunk.getUint8(2)],[3,this.chunk.getUint16(3)],[5,this.chunk.getUint16(5)],[7,this.chunk.getUint8(7)],[8,this.chunk.getUint8(8)]])}}c(ft,\"type\",\"jfif\"),c(ft,\"headerLength\",9),T.set(\"jfif\",ft),U(E,\"jfif\",[[0,\"JFIFVersion\"],[2,\"ResolutionUnit\"],[3,\"XResolution\"],[5,\"YResolution\"],[7,\"ThumbnailWidth\"],[8,\"ThumbnailHeight\"]]);class dt extends re{parse(){return this.parseTags(),this.translate(),this.output}parseTags(){this.raw=new Map([[0,this.chunk.getUint32(0)],[4,this.chunk.getUint32(4)],[8,this.chunk.getUint8(8)],[9,this.chunk.getUint8(9)],[10,this.chunk.getUint8(10)],[11,this.chunk.getUint8(11)],[12,this.chunk.getUint8(12)],...Array.from(this.raw)])}}c(dt,\"type\",\"ihdr\"),T.set(\"ihdr\",dt),U(E,\"ihdr\",[[0,\"ImageWidth\"],[4,\"ImageHeight\"],[8,\"BitDepth\"],[9,\"ColorType\"],[10,\"Compression\"],[11,\"Filter\"],[12,\"Interlace\"]]),U(B,\"ihdr\",[[9,{0:\"Grayscale\",2:\"RGB\",3:\"Palette\",4:\"Grayscale with Alpha\",6:\"RGB with Alpha\",DEFAULT:\"Unknown\"}],[10,{0:\"Deflate/Inflate\",DEFAULT:\"Unknown\"}],[11,{0:\"Adaptive\",DEFAULT:\"Unknown\"}],[12,{0:\"Noninterlaced\",1:\"Adam7 Interlace\",DEFAULT:\"Unknown\"}]]);class pt extends re{static canHandle(e,t){return 226===e.getUint8(t+1)&&1229144927===e.getUint32(t+4)}static findPosition(e,t){let i=super.findPosition(e,t);return i.chunkNumber=e.getUint8(t+16),i.chunkCount=e.getUint8(t+17),i.multiSegment=i.chunkCount>1,i}static handleMultiSegments(e){return function(e){let t=function(e){let t=e[0].constructor,i=0;for(let t of e)i+=t.length;let n=new t(i),s=0;for(let t of e)n.set(t,s),s+=t.length;return n}(e.map((e=>e.chunk.toUint8())));return new I(t)}(e)}parse(){return this.raw=new Map,this.parseHeader(),this.parseTags(),this.translate(),this.output}parseHeader(){let{raw:e}=this;this.chunk.byteLength<84&&g(\"ICC header is too short\");for(let[t,i]of Object.entries(gt)){t=parseInt(t,10);let n=i(this.chunk,t);\"\\0\\0\\0\\0\"!==n&&e.set(t,n)}}parseTags(){let e,t,i,n,s,{raw:r}=this,a=this.chunk.getUint32(128),o=132,l=this.chunk.byteLength;for(;a--;){if(e=this.chunk.getString(o,4),t=this.chunk.getUint32(o+4),i=this.chunk.getUint32(o+8),n=this.chunk.getString(t,4),t+i>l)return void console.warn(\"reached the end of the first ICC chunk. Enable options.tiff.multiSegment to read all ICC segments.\");s=this.parseTag(n,t,i),void 0!==s&&\"\\0\\0\\0\\0\"!==s&&r.set(e,s),o+=12}}parseTag(e,t,i){switch(e){case\"desc\":return this.parseDesc(t);case\"mluc\":return this.parseMluc(t);case\"text\":return this.parseText(t,i);case\"sig \":return this.parseSig(t)}if(!(t+i>this.chunk.byteLength))return this.chunk.getUint8Array(t,i)}parseDesc(e){let t=this.chunk.getUint32(e+8)-1;return m(this.chunk.getString(e+12,t))}parseText(e,t){return m(this.chunk.getString(e+8,t-8))}parseSig(e){return m(this.chunk.getString(e+8,4))}parseMluc(e){let{chunk:t}=this,i=t.getUint32(e+8),n=t.getUint32(e+12),s=e+16,r=[];for(let a=0;a<i;a++){let i=t.getString(s+0,2),a=t.getString(s+2,2),o=t.getUint32(s+4),l=t.getUint32(s+8)+e,h=m(t.getUnicodeString(l,o));r.push({lang:i,country:a,text:h}),s+=n}return 1===i?r[0].text:r}translateValue(e,t){return\"string\"==typeof e?t[e]||t[e.toLowerCase()]||e:t[e]||e}}c(pt,\"type\",\"icc\"),c(pt,\"multiSegment\",!0),c(pt,\"headerLength\",18);const gt={4:mt,8:function(e,t){return[e.getUint8(t),e.getUint8(t+1)>>4,e.getUint8(t+1)%16].map((e=>e.toString(10))).join(\".\")},12:mt,16:mt,20:mt,24:function(e,t){const i=e.getUint16(t),n=e.getUint16(t+2)-1,s=e.getUint16(t+4),r=e.getUint16(t+6),a=e.getUint16(t+8),o=e.getUint16(t+10);return new Date(Date.UTC(i,n,s,r,a,o))},36:mt,40:mt,48:mt,52:mt,64:(e,t)=>e.getUint32(t),80:mt};function mt(e,t){return m(e.getString(t,4))}T.set(\"icc\",pt),U(E,\"icc\",[[4,\"ProfileCMMType\"],[8,\"ProfileVersion\"],[12,\"ProfileClass\"],[16,\"ColorSpaceData\"],[20,\"ProfileConnectionSpace\"],[24,\"ProfileDateTime\"],[36,\"ProfileFileSignature\"],[40,\"PrimaryPlatform\"],[44,\"CMMFlags\"],[48,\"DeviceManufacturer\"],[52,\"DeviceModel\"],[56,\"DeviceAttributes\"],[64,\"RenderingIntent\"],[68,\"ConnectionSpaceIlluminant\"],[80,\"ProfileCreator\"],[84,\"ProfileID\"],[\"Header\",\"ProfileHeader\"],[\"MS00\",\"WCSProfiles\"],[\"bTRC\",\"BlueTRC\"],[\"bXYZ\",\"BlueMatrixColumn\"],[\"bfd\",\"UCRBG\"],[\"bkpt\",\"MediaBlackPoint\"],[\"calt\",\"CalibrationDateTime\"],[\"chad\",\"ChromaticAdaptation\"],[\"chrm\",\"Chromaticity\"],[\"ciis\",\"ColorimetricIntentImageState\"],[\"clot\",\"ColorantTableOut\"],[\"clro\",\"ColorantOrder\"],[\"clrt\",\"ColorantTable\"],[\"cprt\",\"ProfileCopyright\"],[\"crdi\",\"CRDInfo\"],[\"desc\",\"ProfileDescription\"],[\"devs\",\"DeviceSettings\"],[\"dmdd\",\"DeviceModelDesc\"],[\"dmnd\",\"DeviceMfgDesc\"],[\"dscm\",\"ProfileDescriptionML\"],[\"fpce\",\"FocalPlaneColorimetryEstimates\"],[\"gTRC\",\"GreenTRC\"],[\"gXYZ\",\"GreenMatrixColumn\"],[\"gamt\",\"Gamut\"],[\"kTRC\",\"GrayTRC\"],[\"lumi\",\"Luminance\"],[\"meas\",\"Measurement\"],[\"meta\",\"Metadata\"],[\"mmod\",\"MakeAndModel\"],[\"ncl2\",\"NamedColor2\"],[\"ncol\",\"NamedColor\"],[\"ndin\",\"NativeDisplayInfo\"],[\"pre0\",\"Preview0\"],[\"pre1\",\"Preview1\"],[\"pre2\",\"Preview2\"],[\"ps2i\",\"PS2RenderingIntent\"],[\"ps2s\",\"PostScript2CSA\"],[\"psd0\",\"PostScript2CRD0\"],[\"psd1\",\"PostScript2CRD1\"],[\"psd2\",\"PostScript2CRD2\"],[\"psd3\",\"PostScript2CRD3\"],[\"pseq\",\"ProfileSequenceDesc\"],[\"psid\",\"ProfileSequenceIdentifier\"],[\"psvm\",\"PS2CRDVMSize\"],[\"rTRC\",\"RedTRC\"],[\"rXYZ\",\"RedMatrixColumn\"],[\"resp\",\"OutputResponse\"],[\"rhoc\",\"ReflectionHardcopyOrigColorimetry\"],[\"rig0\",\"PerceptualRenderingIntentGamut\"],[\"rig2\",\"SaturationRenderingIntentGamut\"],[\"rpoc\",\"ReflectionPrintOutputColorimetry\"],[\"sape\",\"SceneAppearanceEstimates\"],[\"scoe\",\"SceneColorimetryEstimates\"],[\"scrd\",\"ScreeningDesc\"],[\"scrn\",\"Screening\"],[\"targ\",\"CharTarget\"],[\"tech\",\"Technology\"],[\"vcgt\",\"VideoCardGamma\"],[\"view\",\"ViewingConditions\"],[\"vued\",\"ViewingCondDesc\"],[\"wtpt\",\"MediaWhitePoint\"]]);const St={\"4d2p\":\"Erdt Systems\",AAMA:\"Aamazing Technologies\",ACER:\"Acer\",ACLT:\"Acolyte Color Research\",ACTI:\"Actix Sytems\",ADAR:\"Adara Technology\",ADBE:\"Adobe\",ADI:\"ADI Systems\",AGFA:\"Agfa Graphics\",ALMD:\"Alps Electric\",ALPS:\"Alps Electric\",ALWN:\"Alwan Color Expertise\",AMTI:\"Amiable Technologies\",AOC:\"AOC International\",APAG:\"Apago\",APPL:\"Apple Computer\",AST:\"AST\",\"AT&T\":\"AT&T\",BAEL:\"BARBIERI electronic\",BRCO:\"Barco NV\",BRKP:\"Breakpoint\",BROT:\"Brother\",BULL:\"Bull\",BUS:\"Bus Computer Systems\",\"C-IT\":\"C-Itoh\",CAMR:\"Intel\",CANO:\"Canon\",CARR:\"Carroll Touch\",CASI:\"Casio\",CBUS:\"Colorbus PL\",CEL:\"Crossfield\",CELx:\"Crossfield\",CGS:\"CGS Publishing Technologies International\",CHM:\"Rochester Robotics\",CIGL:\"Colour Imaging Group, London\",CITI:\"Citizen\",CL00:\"Candela\",CLIQ:\"Color IQ\",CMCO:\"Chromaco\",CMiX:\"CHROMiX\",COLO:\"Colorgraphic Communications\",COMP:\"Compaq\",COMp:\"Compeq/Focus Technology\",CONR:\"Conrac Display Products\",CORD:\"Cordata Technologies\",CPQ:\"Compaq\",CPRO:\"ColorPro\",CRN:\"Cornerstone\",CTX:\"CTX International\",CVIS:\"ColorVision\",CWC:\"Fujitsu Laboratories\",DARI:\"Darius Technology\",DATA:\"Dataproducts\",DCP:\"Dry Creek Photo\",DCRC:\"Digital Contents Resource Center, Chung-Ang University\",DELL:\"Dell Computer\",DIC:\"Dainippon Ink and Chemicals\",DICO:\"Diconix\",DIGI:\"Digital\",\"DL&C\":\"Digital Light & Color\",DPLG:\"Doppelganger\",DS:\"Dainippon Screen\",DSOL:\"DOOSOL\",DUPN:\"DuPont\",EPSO:\"Epson\",ESKO:\"Esko-Graphics\",ETRI:\"Electronics and Telecommunications Research Institute\",EVER:\"Everex Systems\",EXAC:\"ExactCODE\",Eizo:\"Eizo\",FALC:\"Falco Data Products\",FF:\"Fuji Photo Film\",FFEI:\"FujiFilm Electronic Imaging\",FNRD:\"Fnord Software\",FORA:\"Fora\",FORE:\"Forefront Technology\",FP:\"Fujitsu\",FPA:\"WayTech Development\",FUJI:\"Fujitsu\",FX:\"Fuji Xerox\",GCC:\"GCC Technologies\",GGSL:\"Global Graphics Software\",GMB:\"Gretagmacbeth\",GMG:\"GMG\",GOLD:\"GoldStar Technology\",GOOG:\"Google\",GPRT:\"Giantprint\",GTMB:\"Gretagmacbeth\",GVC:\"WayTech Development\",GW2K:\"Sony\",HCI:\"HCI\",HDM:\"Heidelberger Druckmaschinen\",HERM:\"Hermes\",HITA:\"Hitachi America\",HP:\"Hewlett-Packard\",HTC:\"Hitachi\",HiTi:\"HiTi Digital\",IBM:\"IBM\",IDNT:\"Scitex\",IEC:\"Hewlett-Packard\",IIYA:\"Iiyama North America\",IKEG:\"Ikegami Electronics\",IMAG:\"Image Systems\",IMI:\"Ingram Micro\",INTC:\"Intel\",INTL:\"N/A (INTL)\",INTR:\"Intra Electronics\",IOCO:\"Iocomm International Technology\",IPS:\"InfoPrint Solutions Company\",IRIS:\"Scitex\",ISL:\"Ichikawa Soft Laboratory\",ITNL:\"N/A (ITNL)\",IVM:\"IVM\",IWAT:\"Iwatsu Electric\",Idnt:\"Scitex\",Inca:\"Inca Digital Printers\",Iris:\"Scitex\",JPEG:\"Joint Photographic Experts Group\",JSFT:\"Jetsoft Development\",JVC:\"JVC Information Products\",KART:\"Scitex\",KFC:\"KFC Computek Components\",KLH:\"KLH Computers\",KMHD:\"Konica Minolta\",KNCA:\"Konica\",KODA:\"Kodak\",KYOC:\"Kyocera\",Kart:\"Scitex\",LCAG:\"Leica\",LCCD:\"Leeds Colour\",LDAK:\"Left Dakota\",LEAD:\"Leading Technology\",LEXM:\"Lexmark International\",LINK:\"Link Computer\",LINO:\"Linotronic\",LITE:\"Lite-On\",Leaf:\"Leaf\",Lino:\"Linotronic\",MAGC:\"Mag Computronic\",MAGI:\"MAG Innovision\",MANN:\"Mannesmann\",MICN:\"Micron Technology\",MICR:\"Microtek\",MICV:\"Microvitec\",MINO:\"Minolta\",MITS:\"Mitsubishi Electronics America\",MITs:\"Mitsuba\",MNLT:\"Minolta\",MODG:\"Modgraph\",MONI:\"Monitronix\",MONS:\"Monaco Systems\",MORS:\"Morse Technology\",MOTI:\"Motive Systems\",MSFT:\"Microsoft\",MUTO:\"MUTOH INDUSTRIES\",Mits:\"Mitsubishi Electric\",NANA:\"NANAO\",NEC:\"NEC\",NEXP:\"NexPress Solutions\",NISS:\"Nissei Sangyo America\",NKON:\"Nikon\",NONE:\"none\",OCE:\"Oce Technologies\",OCEC:\"OceColor\",OKI:\"Oki\",OKID:\"Okidata\",OKIP:\"Okidata\",OLIV:\"Olivetti\",OLYM:\"Olympus\",ONYX:\"Onyx Graphics\",OPTI:\"Optiquest\",PACK:\"Packard Bell\",PANA:\"Matsushita Electric Industrial\",PANT:\"Pantone\",PBN:\"Packard Bell\",PFU:\"PFU\",PHIL:\"Philips Consumer Electronics\",PNTX:\"HOYA\",POne:\"Phase One A/S\",PREM:\"Premier Computer Innovations\",PRIN:\"Princeton Graphic Systems\",PRIP:\"Princeton Publishing Labs\",QLUX:\"Hong Kong\",QMS:\"QMS\",QPCD:\"QPcard AB\",QUAD:\"QuadLaser\",QUME:\"Qume\",RADI:\"Radius\",RDDx:\"Integrated Color Solutions\",RDG:\"Roland DG\",REDM:\"REDMS Group\",RELI:\"Relisys\",RGMS:\"Rolf Gierling Multitools\",RICO:\"Ricoh\",RNLD:\"Edmund Ronald\",ROYA:\"Royal\",RPC:\"Ricoh Printing Systems\",RTL:\"Royal Information Electronics\",SAMP:\"Sampo\",SAMS:\"Samsung\",SANT:\"Jaime Santana Pomares\",SCIT:\"Scitex\",SCRN:\"Dainippon Screen\",SDP:\"Scitex\",SEC:\"Samsung\",SEIK:\"Seiko Instruments\",SEIk:\"Seikosha\",SGUY:\"ScanGuy.com\",SHAR:\"Sharp Laboratories\",SICC:\"International Color Consortium\",SONY:\"Sony\",SPCL:\"SpectraCal\",STAR:\"Star\",STC:\"Sampo Technology\",Scit:\"Scitex\",Sdp:\"Scitex\",Sony:\"Sony\",TALO:\"Talon Technology\",TAND:\"Tandy\",TATU:\"Tatung\",TAXA:\"TAXAN America\",TDS:\"Tokyo Denshi Sekei\",TECO:\"TECO Information Systems\",TEGR:\"Tegra\",TEKT:\"Tektronix\",TI:\"Texas Instruments\",TMKR:\"TypeMaker\",TOSB:\"Toshiba\",TOSH:\"Toshiba\",TOTK:\"TOTOKU ELECTRIC\",TRIU:\"Triumph\",TSBT:\"Toshiba\",TTX:\"TTX Computer Products\",TVM:\"TVM Professional Monitor\",TW:\"TW Casper\",ULSX:\"Ulead Systems\",UNIS:\"Unisys\",UTZF:\"Utz Fehlau & Sohn\",VARI:\"Varityper\",VIEW:\"Viewsonic\",VISL:\"Visual communication\",VIVO:\"Vivo Mobile Communication\",WANG:\"Wang\",WLBR:\"Wilbur Imaging\",WTG2:\"Ware To Go\",WYSE:\"WYSE Technology\",XERX:\"Xerox\",XRIT:\"X-Rite\",ZRAN:\"Zoran\",Zebr:\"Zebra Technologies\",appl:\"Apple Computer\",bICC:\"basICColor\",berg:\"bergdesign\",ceyd:\"Integrated Color Solutions\",clsp:\"MacDermid ColorSpan\",ds:\"Dainippon Screen\",dupn:\"DuPont\",ffei:\"FujiFilm Electronic Imaging\",flux:\"FluxData\",iris:\"Scitex\",kart:\"Scitex\",lcms:\"Little CMS\",lino:\"Linotronic\",none:\"none\",ob4d:\"Erdt Systems\",obic:\"Medigraph\",quby:\"Qubyx Sarl\",scit:\"Scitex\",scrn:\"Dainippon Screen\",sdp:\"Scitex\",siwi:\"SIWI GRAFIKA\",yxym:\"YxyMaster\"},Ct={scnr:\"Scanner\",mntr:\"Monitor\",prtr:\"Printer\",link:\"Device Link\",abst:\"Abstract\",spac:\"Color Space Conversion Profile\",nmcl:\"Named Color\",cenc:\"ColorEncodingSpace profile\",mid:\"MultiplexIdentification profile\",mlnk:\"MultiplexLink profile\",mvis:\"MultiplexVisualization profile\",nkpf:\"Nikon Input Device Profile (NON-STANDARD!)\"};U(B,\"icc\",[[4,St],[12,Ct],[40,Object.assign({},St,Ct)],[48,St],[80,St],[64,{0:\"Perceptual\",1:\"Relative Colorimetric\",2:\"Saturation\",3:\"Absolute Colorimetric\"}],[\"tech\",{amd:\"Active Matrix Display\",crt:\"Cathode Ray Tube Display\",kpcd:\"Photo CD\",pmd:\"Passive Matrix Display\",dcam:\"Digital Camera\",dcpj:\"Digital Cinema Projector\",dmpc:\"Digital Motion Picture Camera\",dsub:\"Dye Sublimation Printer\",epho:\"Electrophotographic Printer\",esta:\"Electrostatic Printer\",flex:\"Flexography\",fprn:\"Film Writer\",fscn:\"Film Scanner\",grav:\"Gravure\",ijet:\"Ink Jet Printer\",imgs:\"Photo Image Setter\",mpfr:\"Motion Picture Film Recorder\",mpfs:\"Motion Picture Film Scanner\",offs:\"Offset Lithography\",pjtv:\"Projection Television\",rpho:\"Photographic Paper Printer\",rscn:\"Reflective Scanner\",silk:\"Silkscreen\",twax:\"Thermal Wax Printer\",vidc:\"Video Camera\",vidm:\"Video Monitor\"}]]);class yt extends re{static canHandle(e,t,i){return 237===e.getUint8(t+1)&&\"Photoshop\"===e.getString(t+4,9)&&void 0!==this.containsIptc8bim(e,t,i)}static headerLength(e,t,i){let n,s=this.containsIptc8bim(e,t,i);if(void 0!==s)return n=e.getUint8(t+s+7),n%2!=0&&(n+=1),0===n&&(n=4),s+8+n}static containsIptc8bim(e,t,i){for(let n=0;n<i;n++)if(this.isIptcSegmentHead(e,t+n))return n}static isIptcSegmentHead(e,t){return 56===e.getUint8(t)&&943868237===e.getUint32(t)&&1028===e.getUint16(t+4)}parse(){let{raw:e}=this,t=this.chunk.byteLength-1,i=!1;for(let n=0;n<t;n++)if(28===this.chunk.getUint8(n)&&2===this.chunk.getUint8(n+1)){i=!0;let t=this.chunk.getUint16(n+3),s=this.chunk.getUint8(n+2),r=this.chunk.getLatin1String(n+5,t);e.set(s,this.pluralizeValue(e.get(s),r)),n+=4+t}else if(i)break;return this.translate(),this.output}pluralizeValue(e,t){return void 0!==e?e instanceof Array?(e.push(t),e):[e,t]:t}}c(yt,\"type\",\"iptc\"),c(yt,\"translateValues\",!1),c(yt,\"reviveValues\",!1),T.set(\"iptc\",yt),U(E,\"iptc\",[[0,\"ApplicationRecordVersion\"],[3,\"ObjectTypeReference\"],[4,\"ObjectAttributeReference\"],[5,\"ObjectName\"],[7,\"EditStatus\"],[8,\"EditorialUpdate\"],[10,\"Urgency\"],[12,\"SubjectReference\"],[15,\"Category\"],[20,\"SupplementalCategories\"],[22,\"FixtureIdentifier\"],[25,\"Keywords\"],[26,\"ContentLocationCode\"],[27,\"ContentLocationName\"],[30,\"ReleaseDate\"],[35,\"ReleaseTime\"],[37,\"ExpirationDate\"],[38,\"ExpirationTime\"],[40,\"SpecialInstructions\"],[42,\"ActionAdvised\"],[45,\"ReferenceService\"],[47,\"ReferenceDate\"],[50,\"ReferenceNumber\"],[55,\"DateCreated\"],[60,\"TimeCreated\"],[62,\"DigitalCreationDate\"],[63,\"DigitalCreationTime\"],[65,\"OriginatingProgram\"],[70,\"ProgramVersion\"],[75,\"ObjectCycle\"],[80,\"Byline\"],[85,\"BylineTitle\"],[90,\"City\"],[92,\"Sublocation\"],[95,\"State\"],[100,\"CountryCode\"],[101,\"Country\"],[103,\"OriginalTransmissionReference\"],[105,\"Headline\"],[110,\"Credit\"],[115,\"Source\"],[116,\"CopyrightNotice\"],[118,\"Contact\"],[120,\"Caption\"],[121,\"LocalCaption\"],[122,\"Writer\"],[125,\"RasterizedCaption\"],[130,\"ImageType\"],[131,\"ImageOrientation\"],[135,\"LanguageIdentifier\"],[150,\"AudioType\"],[151,\"AudioSamplingRate\"],[152,\"AudioSamplingResolution\"],[153,\"AudioDuration\"],[154,\"AudioOutcue\"],[184,\"JobID\"],[185,\"MasterDocumentID\"],[186,\"ShortDocumentID\"],[187,\"UniqueDocumentID\"],[188,\"OwnerID\"],[200,\"ObjectPreviewFileFormat\"],[201,\"ObjectPreviewFileVersion\"],[202,\"ObjectPreviewData\"],[221,\"Prefs\"],[225,\"ClassifyState\"],[228,\"SimilarityIndex\"],[230,\"DocumentNotes\"],[231,\"DocumentHistory\"],[232,\"ExifCameraInfo\"],[255,\"CatalogSets\"]]),U(B,\"iptc\",[[10,{0:\"0 (reserved)\",1:\"1 (most urgent)\",2:\"2\",3:\"3\",4:\"4\",5:\"5 (normal urgency)\",6:\"6\",7:\"7\",8:\"8 (least urgent)\",9:\"9 (user-defined priority)\"}],[75,{a:\"Morning\",b:\"Both Morning and Evening\",p:\"Evening\"}],[131,{L:\"Landscape\",P:\"Portrait\",S:\"Square\"}]]);/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (tt);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/exifr/dist/full.esm.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/exifr/dist/full.esm.mjs":
/*!**********************************************!*\
  !*** ./node_modules/exifr/dist/full.esm.mjs ***!
  \**********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Exifr: () => (/* binding */ te),\n/* harmony export */   Options: () => (/* binding */ q),\n/* harmony export */   allFormatters: () => (/* binding */ X),\n/* harmony export */   chunkedProps: () => (/* binding */ G),\n/* harmony export */   createDictionary: () => (/* binding */ U),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   extendDictionary: () => (/* binding */ F),\n/* harmony export */   fetchUrlAsArrayBuffer: () => (/* binding */ M),\n/* harmony export */   fileParsers: () => (/* binding */ w),\n/* harmony export */   fileReaders: () => (/* binding */ A),\n/* harmony export */   gps: () => (/* binding */ Se),\n/* harmony export */   gpsOnlyOptions: () => (/* binding */ me),\n/* harmony export */   inheritables: () => (/* binding */ K),\n/* harmony export */   orientation: () => (/* binding */ Pe),\n/* harmony export */   orientationOnlyOptions: () => (/* binding */ Ie),\n/* harmony export */   otherSegments: () => (/* binding */ V),\n/* harmony export */   parse: () => (/* binding */ ie),\n/* harmony export */   readBlobAsArrayBuffer: () => (/* binding */ R),\n/* harmony export */   rotateCanvas: () => (/* binding */ we),\n/* harmony export */   rotateCss: () => (/* binding */ Te),\n/* harmony export */   rotation: () => (/* binding */ Ae),\n/* harmony export */   rotations: () => (/* binding */ ke),\n/* harmony export */   segmentParsers: () => (/* binding */ T),\n/* harmony export */   segments: () => (/* binding */ z),\n/* harmony export */   segmentsAndBlocks: () => (/* binding */ j),\n/* harmony export */   sidecar: () => (/* binding */ st),\n/* harmony export */   tagKeys: () => (/* binding */ E),\n/* harmony export */   tagRevivers: () => (/* binding */ N),\n/* harmony export */   tagValues: () => (/* binding */ B),\n/* harmony export */   thumbnail: () => (/* binding */ ye),\n/* harmony export */   thumbnailOnlyOptions: () => (/* binding */ Ce),\n/* harmony export */   thumbnailUrl: () => (/* binding */ be),\n/* harmony export */   tiffBlocks: () => (/* binding */ H),\n/* harmony export */   tiffExtractables: () => (/* binding */ W)\n/* harmony export */ });\nvar e=\"undefined\"!=typeof self?self:global;const t=\"undefined\"!=typeof navigator,i=t&&\"undefined\"==typeof HTMLImageElement,n=!(\"undefined\"==typeof global||\"undefined\"==typeof process||!process.versions||!process.versions.node),s=e.Buffer,r=e.BigInt,a=!!s,o=e=>e;function l(e,t=o){if(n)try{return\"function\"==typeof require?Promise.resolve(t(require(e))):import(/* webpackIgnore: true */ e).then(t)}catch(t){console.warn(`Couldn't load ${e}`)}}let h=e.fetch;const u=e=>h=e;if(!e.fetch){const e=l(\"http\",(e=>e)),t=l(\"https\",(e=>e)),i=(n,{headers:s}={})=>new Promise((async(r,a)=>{let{port:o,hostname:l,pathname:h,protocol:u,search:c}=new URL(n);const f={method:\"GET\",hostname:l,path:encodeURI(h)+c,headers:s};\"\"!==o&&(f.port=Number(o));const d=(\"https:\"===u?await t:await e).request(f,(e=>{if(301===e.statusCode||302===e.statusCode){let t=new URL(e.headers.location,n).toString();return i(t,{headers:s}).then(r).catch(a)}r({status:e.statusCode,arrayBuffer:()=>new Promise((t=>{let i=[];e.on(\"data\",(e=>i.push(e))),e.on(\"end\",(()=>t(Buffer.concat(i))))}))})}));d.on(\"error\",a),d.end()}));u(i)}function c(e,t,i){return t in e?Object.defineProperty(e,t,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[t]=i,e}const f=e=>p(e)?void 0:e,d=e=>void 0!==e;function p(e){return void 0===e||(e instanceof Map?0===e.size:0===Object.values(e).filter(d).length)}function g(e){let t=new Error(e);throw delete t.stack,t}function m(e){return\"\"===(e=function(e){for(;e.endsWith(\"\\0\");)e=e.slice(0,-1);return e}(e).trim())?void 0:e}function S(e){let t=function(e){let t=0;return e.ifd0.enabled&&(t+=1024),e.exif.enabled&&(t+=2048),e.makerNote&&(t+=2048),e.userComment&&(t+=1024),e.gps.enabled&&(t+=512),e.interop.enabled&&(t+=100),e.ifd1.enabled&&(t+=1024),t+2048}(e);return e.jfif.enabled&&(t+=50),e.xmp.enabled&&(t+=2e4),e.iptc.enabled&&(t+=14e3),e.icc.enabled&&(t+=6e3),t}const C=e=>String.fromCharCode.apply(null,e),y=\"undefined\"!=typeof TextDecoder?new TextDecoder(\"utf-8\"):void 0;function b(e){return y?y.decode(e):a?Buffer.from(e).toString(\"utf8\"):decodeURIComponent(escape(C(e)))}class I{static from(e,t){return e instanceof this&&e.le===t?e:new I(e,void 0,void 0,t)}constructor(e,t=0,i,n){if(\"boolean\"==typeof n&&(this.le=n),Array.isArray(e)&&(e=new Uint8Array(e)),0===e)this.byteOffset=0,this.byteLength=0;else if(e instanceof ArrayBuffer){void 0===i&&(i=e.byteLength-t);let n=new DataView(e,t,i);this._swapDataView(n)}else if(e instanceof Uint8Array||e instanceof DataView||e instanceof I){void 0===i&&(i=e.byteLength-t),(t+=e.byteOffset)+i>e.byteOffset+e.byteLength&&g(\"Creating view outside of available memory in ArrayBuffer\");let n=new DataView(e.buffer,t,i);this._swapDataView(n)}else if(\"number\"==typeof e){let t=new DataView(new ArrayBuffer(e));this._swapDataView(t)}else g(\"Invalid input argument for BufferView: \"+e)}_swapArrayBuffer(e){this._swapDataView(new DataView(e))}_swapBuffer(e){this._swapDataView(new DataView(e.buffer,e.byteOffset,e.byteLength))}_swapDataView(e){this.dataView=e,this.buffer=e.buffer,this.byteOffset=e.byteOffset,this.byteLength=e.byteLength}_lengthToEnd(e){return this.byteLength-e}set(e,t,i=I){return e instanceof DataView||e instanceof I?e=new Uint8Array(e.buffer,e.byteOffset,e.byteLength):e instanceof ArrayBuffer&&(e=new Uint8Array(e)),e instanceof Uint8Array||g(\"BufferView.set(): Invalid data argument.\"),this.toUint8().set(e,t),new i(this,t,e.byteLength)}subarray(e,t){return t=t||this._lengthToEnd(e),new I(this,e,t)}toUint8(){return new Uint8Array(this.buffer,this.byteOffset,this.byteLength)}getUint8Array(e,t){return new Uint8Array(this.buffer,this.byteOffset+e,t)}getString(e=0,t=this.byteLength){return b(this.getUint8Array(e,t))}getLatin1String(e=0,t=this.byteLength){let i=this.getUint8Array(e,t);return C(i)}getUnicodeString(e=0,t=this.byteLength){const i=[];for(let n=0;n<t&&e+n<this.byteLength;n+=2)i.push(this.getUint16(e+n));return C(i)}getInt8(e){return this.dataView.getInt8(e)}getUint8(e){return this.dataView.getUint8(e)}getInt16(e,t=this.le){return this.dataView.getInt16(e,t)}getInt32(e,t=this.le){return this.dataView.getInt32(e,t)}getUint16(e,t=this.le){return this.dataView.getUint16(e,t)}getUint32(e,t=this.le){return this.dataView.getUint32(e,t)}getFloat32(e,t=this.le){return this.dataView.getFloat32(e,t)}getFloat64(e,t=this.le){return this.dataView.getFloat64(e,t)}getFloat(e,t=this.le){return this.dataView.getFloat32(e,t)}getDouble(e,t=this.le){return this.dataView.getFloat64(e,t)}getUintBytes(e,t,i){switch(t){case 1:return this.getUint8(e,i);case 2:return this.getUint16(e,i);case 4:return this.getUint32(e,i);case 8:return this.getUint64&&this.getUint64(e,i)}}getUint(e,t,i){switch(t){case 8:return this.getUint8(e,i);case 16:return this.getUint16(e,i);case 32:return this.getUint32(e,i);case 64:return this.getUint64&&this.getUint64(e,i)}}toString(e){return this.dataView.toString(e,this.constructor.name)}ensureChunk(){}}function P(e,t){g(`${e} '${t}' was not loaded, try using full build of exifr.`)}class k extends Map{constructor(e){super(),this.kind=e}get(e,t){return this.has(e)||P(this.kind,e),t&&(e in t||function(e,t){g(`Unknown ${e} '${t}'.`)}(this.kind,e),t[e].enabled||P(this.kind,e)),super.get(e)}keyList(){return Array.from(this.keys())}}var w=new k(\"file parser\"),T=new k(\"segment parser\"),A=new k(\"file reader\");function D(e,n){return\"string\"==typeof e?O(e,n):t&&!i&&e instanceof HTMLImageElement?O(e.src,n):e instanceof Uint8Array||e instanceof ArrayBuffer||e instanceof DataView?new I(e):t&&e instanceof Blob?x(e,n,\"blob\",R):void g(\"Invalid input argument\")}function O(e,i){return(s=e).startsWith(\"data:\")||s.length>1e4?v(e,i,\"base64\"):n&&e.includes(\"://\")?x(e,i,\"url\",M):n?v(e,i,\"fs\"):t?x(e,i,\"url\",M):void g(\"Invalid input argument\");var s}async function x(e,t,i,n){return A.has(i)?v(e,t,i):n?async function(e,t){let i=await t(e);return new I(i)}(e,n):void g(`Parser ${i} is not loaded`)}async function v(e,t,i){let n=new(A.get(i))(e,t);return await n.read(),n}const M=e=>h(e).then((e=>e.arrayBuffer())),R=e=>new Promise(((t,i)=>{let n=new FileReader;n.onloadend=()=>t(n.result||new ArrayBuffer),n.onerror=i,n.readAsArrayBuffer(e)}));class L extends Map{get tagKeys(){return this.allKeys||(this.allKeys=Array.from(this.keys())),this.allKeys}get tagValues(){return this.allValues||(this.allValues=Array.from(this.values())),this.allValues}}function U(e,t,i){let n=new L;for(let[e,t]of i)n.set(e,t);if(Array.isArray(t))for(let i of t)e.set(i,n);else e.set(t,n);return n}function F(e,t,i){let n,s=e.get(t);for(n of i)s.set(n[0],n[1])}const E=new Map,B=new Map,N=new Map,G=[\"chunked\",\"firstChunkSize\",\"firstChunkSizeNode\",\"firstChunkSizeBrowser\",\"chunkSize\",\"chunkLimit\"],V=[\"jfif\",\"xmp\",\"icc\",\"iptc\",\"ihdr\"],z=[\"tiff\",...V],H=[\"ifd0\",\"ifd1\",\"exif\",\"gps\",\"interop\"],j=[...z,...H],W=[\"makerNote\",\"userComment\"],K=[\"translateKeys\",\"translateValues\",\"reviveValues\",\"multiSegment\"],X=[...K,\"sanitize\",\"mergeOutput\",\"silentErrors\"];class _{get translate(){return this.translateKeys||this.translateValues||this.reviveValues}}class Y extends _{get needed(){return this.enabled||this.deps.size>0}constructor(e,t,i,n){if(super(),c(this,\"enabled\",!1),c(this,\"skip\",new Set),c(this,\"pick\",new Set),c(this,\"deps\",new Set),c(this,\"translateKeys\",!1),c(this,\"translateValues\",!1),c(this,\"reviveValues\",!1),this.key=e,this.enabled=t,this.parse=this.enabled,this.applyInheritables(n),this.canBeFiltered=H.includes(e),this.canBeFiltered&&(this.dict=E.get(e)),void 0!==i)if(Array.isArray(i))this.parse=this.enabled=!0,this.canBeFiltered&&i.length>0&&this.translateTagSet(i,this.pick);else if(\"object\"==typeof i){if(this.enabled=!0,this.parse=!1!==i.parse,this.canBeFiltered){let{pick:e,skip:t}=i;e&&e.length>0&&this.translateTagSet(e,this.pick),t&&t.length>0&&this.translateTagSet(t,this.skip)}this.applyInheritables(i)}else!0===i||!1===i?this.parse=this.enabled=i:g(`Invalid options argument: ${i}`)}applyInheritables(e){let t,i;for(t of K)i=e[t],void 0!==i&&(this[t]=i)}translateTagSet(e,t){if(this.dict){let i,n,{tagKeys:s,tagValues:r}=this.dict;for(i of e)\"string\"==typeof i?(n=r.indexOf(i),-1===n&&(n=s.indexOf(Number(i))),-1!==n&&t.add(Number(s[n]))):t.add(i)}else for(let i of e)t.add(i)}finalizeFilters(){!this.enabled&&this.deps.size>0?(this.enabled=!0,ee(this.pick,this.deps)):this.enabled&&this.pick.size>0&&ee(this.pick,this.deps)}}var $={jfif:!1,tiff:!0,xmp:!1,icc:!1,iptc:!1,ifd0:!0,ifd1:!1,exif:!0,gps:!0,interop:!1,ihdr:void 0,makerNote:!1,userComment:!1,multiSegment:!1,skip:[],pick:[],translateKeys:!0,translateValues:!0,reviveValues:!0,sanitize:!0,mergeOutput:!0,silentErrors:!0,chunked:!0,firstChunkSize:void 0,firstChunkSizeNode:512,firstChunkSizeBrowser:65536,chunkSize:65536,chunkLimit:5},J=new Map;class q extends _{static useCached(e){let t=J.get(e);return void 0!==t||(t=new this(e),J.set(e,t)),t}constructor(e){super(),!0===e?this.setupFromTrue():void 0===e?this.setupFromUndefined():Array.isArray(e)?this.setupFromArray(e):\"object\"==typeof e?this.setupFromObject(e):g(`Invalid options argument ${e}`),void 0===this.firstChunkSize&&(this.firstChunkSize=t?this.firstChunkSizeBrowser:this.firstChunkSizeNode),this.mergeOutput&&(this.ifd1.enabled=!1),this.filterNestedSegmentTags(),this.traverseTiffDependencyTree(),this.checkLoadedPlugins()}setupFromUndefined(){let e;for(e of G)this[e]=$[e];for(e of X)this[e]=$[e];for(e of W)this[e]=$[e];for(e of j)this[e]=new Y(e,$[e],void 0,this)}setupFromTrue(){let e;for(e of G)this[e]=$[e];for(e of X)this[e]=$[e];for(e of W)this[e]=!0;for(e of j)this[e]=new Y(e,!0,void 0,this)}setupFromArray(e){let t;for(t of G)this[t]=$[t];for(t of X)this[t]=$[t];for(t of W)this[t]=$[t];for(t of j)this[t]=new Y(t,!1,void 0,this);this.setupGlobalFilters(e,void 0,H)}setupFromObject(e){let t;for(t of(H.ifd0=H.ifd0||H.image,H.ifd1=H.ifd1||H.thumbnail,Object.assign(this,e),G))this[t]=Z(e[t],$[t]);for(t of X)this[t]=Z(e[t],$[t]);for(t of W)this[t]=Z(e[t],$[t]);for(t of z)this[t]=new Y(t,$[t],e[t],this);for(t of H)this[t]=new Y(t,$[t],e[t],this.tiff);this.setupGlobalFilters(e.pick,e.skip,H,j),!0===e.tiff?this.batchEnableWithBool(H,!0):!1===e.tiff?this.batchEnableWithUserValue(H,e):Array.isArray(e.tiff)?this.setupGlobalFilters(e.tiff,void 0,H):\"object\"==typeof e.tiff&&this.setupGlobalFilters(e.tiff.pick,e.tiff.skip,H)}batchEnableWithBool(e,t){for(let i of e)this[i].enabled=t}batchEnableWithUserValue(e,t){for(let i of e){let e=t[i];this[i].enabled=!1!==e&&void 0!==e}}setupGlobalFilters(e,t,i,n=i){if(e&&e.length){for(let e of n)this[e].enabled=!1;let t=Q(e,i);for(let[e,i]of t)ee(this[e].pick,i),this[e].enabled=!0}else if(t&&t.length){let e=Q(t,i);for(let[t,i]of e)ee(this[t].skip,i)}}filterNestedSegmentTags(){let{ifd0:e,exif:t,xmp:i,iptc:n,icc:s}=this;this.makerNote?t.deps.add(37500):t.skip.add(37500),this.userComment?t.deps.add(37510):t.skip.add(37510),i.enabled||e.skip.add(700),n.enabled||e.skip.add(33723),s.enabled||e.skip.add(34675)}traverseTiffDependencyTree(){let{ifd0:e,exif:t,gps:i,interop:n}=this;n.needed&&(t.deps.add(40965),e.deps.add(40965)),t.needed&&e.deps.add(34665),i.needed&&e.deps.add(34853),this.tiff.enabled=H.some((e=>!0===this[e].enabled))||this.makerNote||this.userComment;for(let e of H)this[e].finalizeFilters()}get onlyTiff(){return!V.map((e=>this[e].enabled)).some((e=>!0===e))&&this.tiff.enabled}checkLoadedPlugins(){for(let e of z)this[e].enabled&&!T.has(e)&&P(\"segment parser\",e)}}function Q(e,t){let i,n,s,r,a=[];for(s of t){for(r of(i=E.get(s),n=[],i))(e.includes(r[0])||e.includes(r[1]))&&n.push(r[0]);n.length&&a.push([s,n])}return a}function Z(e,t){return void 0!==e?e:void 0!==t?t:void 0}function ee(e,t){for(let i of t)e.add(i)}c(q,\"default\",$);class te{constructor(e){c(this,\"parsers\",{}),c(this,\"output\",{}),c(this,\"errors\",[]),c(this,\"pushToErrors\",(e=>this.errors.push(e))),this.options=q.useCached(e)}async read(e){this.file=await D(e,this.options)}setup(){if(this.fileParser)return;let{file:e}=this,t=e.getUint16(0);for(let[i,n]of w)if(n.canHandle(e,t))return this.fileParser=new n(this.options,this.file,this.parsers),e[i]=!0;this.file.close&&this.file.close(),g(\"Unknown file format\")}async parse(){let{output:e,errors:t}=this;return this.setup(),this.options.silentErrors?(await this.executeParsers().catch(this.pushToErrors),t.push(...this.fileParser.errors)):await this.executeParsers(),this.file.close&&this.file.close(),this.options.silentErrors&&t.length>0&&(e.errors=t),f(e)}async executeParsers(){let{output:e}=this;await this.fileParser.parse();let t=Object.values(this.parsers).map((async t=>{let i=await t.parse();t.assignToOutput(e,i)}));this.options.silentErrors&&(t=t.map((e=>e.catch(this.pushToErrors)))),await Promise.all(t)}async extractThumbnail(){this.setup();let{options:e,file:t}=this,i=T.get(\"tiff\",e);var n;if(t.tiff?n={start:0,type:\"tiff\"}:t.jpeg&&(n=await this.fileParser.getOrFindSegment(\"tiff\")),void 0===n)return;let s=await this.fileParser.ensureSegmentChunk(n),r=this.parsers.tiff=new i(s,e,t),a=await r.extractThumbnail();return t.close&&t.close(),a}}async function ie(e,t){let i=new te(t);return await i.read(e),i.parse()}var ne=Object.freeze({__proto__:null,parse:ie,Exifr:te,fileParsers:w,segmentParsers:T,fileReaders:A,tagKeys:E,tagValues:B,tagRevivers:N,createDictionary:U,extendDictionary:F,fetchUrlAsArrayBuffer:M,readBlobAsArrayBuffer:R,chunkedProps:G,otherSegments:V,segments:z,tiffBlocks:H,segmentsAndBlocks:j,tiffExtractables:W,inheritables:K,allFormatters:X,Options:q});class se{constructor(e,t,i){c(this,\"errors\",[]),c(this,\"ensureSegmentChunk\",(async e=>{let t=e.start,i=e.size||65536;if(this.file.chunked)if(this.file.available(t,i))e.chunk=this.file.subarray(t,i);else try{e.chunk=await this.file.readChunk(t,i)}catch(t){g(`Couldn't read segment: ${JSON.stringify(e)}. ${t.message}`)}else this.file.byteLength>t+i?e.chunk=this.file.subarray(t,i):void 0===e.size?e.chunk=this.file.subarray(t):g(\"Segment unreachable: \"+JSON.stringify(e));return e.chunk})),this.extendOptions&&this.extendOptions(e),this.options=e,this.file=t,this.parsers=i}injectSegment(e,t){this.options[e].enabled&&this.createParser(e,t)}createParser(e,t){let i=new(T.get(e))(t,this.options,this.file);return this.parsers[e]=i}createParsers(e){for(let t of e){let{type:e,chunk:i}=t,n=this.options[e];if(n&&n.enabled){let t=this.parsers[e];t&&t.append||t||this.createParser(e,i)}}}async readSegments(e){let t=e.map(this.ensureSegmentChunk);await Promise.all(t)}}class re{static findPosition(e,t){let i=e.getUint16(t+2)+2,n=\"function\"==typeof this.headerLength?this.headerLength(e,t,i):this.headerLength,s=t+n,r=i-n;return{offset:t,length:i,headerLength:n,start:s,size:r,end:s+r}}static parse(e,t={}){return new this(e,new q({[this.type]:t}),e).parse()}normalizeInput(e){return e instanceof I?e:new I(e)}constructor(e,t={},i){c(this,\"errors\",[]),c(this,\"raw\",new Map),c(this,\"handleError\",(e=>{if(!this.options.silentErrors)throw e;this.errors.push(e.message)})),this.chunk=this.normalizeInput(e),this.file=i,this.type=this.constructor.type,this.globalOptions=this.options=t,this.localOptions=t[this.type],this.canTranslate=this.localOptions&&this.localOptions.translate}translate(){this.canTranslate&&(this.translated=this.translateBlock(this.raw,this.type))}get output(){return this.translated?this.translated:this.raw?Object.fromEntries(this.raw):void 0}translateBlock(e,t){let i=N.get(t),n=B.get(t),s=E.get(t),r=this.options[t],a=r.reviveValues&&!!i,o=r.translateValues&&!!n,l=r.translateKeys&&!!s,h={};for(let[t,r]of e)a&&i.has(t)?r=i.get(t)(r):o&&n.has(t)&&(r=this.translateValue(r,n.get(t))),l&&s.has(t)&&(t=s.get(t)||t),h[t]=r;return h}translateValue(e,t){return t[e]||t.DEFAULT||e}assignToOutput(e,t){this.assignObjectToOutput(e,this.constructor.type,t)}assignObjectToOutput(e,t,i){if(this.globalOptions.mergeOutput)return Object.assign(e,i);e[t]?Object.assign(e[t],i):e[t]=i}}c(re,\"headerLength\",4),c(re,\"type\",void 0),c(re,\"multiSegment\",!1),c(re,\"canHandle\",(()=>!1));function ae(e){return 192===e||194===e||196===e||219===e||221===e||218===e||254===e}function oe(e){return e>=224&&e<=239}function le(e,t,i){for(let[n,s]of T)if(s.canHandle(e,t,i))return n}class he extends se{constructor(...e){super(...e),c(this,\"appSegments\",[]),c(this,\"jpegSegments\",[]),c(this,\"unknownSegments\",[])}static canHandle(e,t){return 65496===t}async parse(){await this.findAppSegments(),await this.readSegments(this.appSegments),this.mergeMultiSegments(),this.createParsers(this.mergedAppSegments||this.appSegments)}setupSegmentFinderArgs(e){!0===e?(this.findAll=!0,this.wanted=new Set(T.keyList())):(e=void 0===e?T.keyList().filter((e=>this.options[e].enabled)):e.filter((e=>this.options[e].enabled&&T.has(e))),this.findAll=!1,this.remaining=new Set(e),this.wanted=new Set(e)),this.unfinishedMultiSegment=!1}async findAppSegments(e=0,t){this.setupSegmentFinderArgs(t);let{file:i,findAll:n,wanted:s,remaining:r}=this;if(!n&&this.file.chunked&&(n=Array.from(s).some((e=>{let t=T.get(e),i=this.options[e];return t.multiSegment&&i.multiSegment})),n&&await this.file.readWhole()),e=this.findAppSegmentsInRange(e,i.byteLength),!this.options.onlyTiff&&i.chunked){let t=!1;for(;r.size>0&&!t&&(i.canReadNextChunk||this.unfinishedMultiSegment);){let{nextChunkOffset:n}=i,s=this.appSegments.some((e=>!this.file.available(e.offset||e.start,e.length||e.size)));if(t=e>n&&!s?!await i.readNextChunk(e):!await i.readNextChunk(n),void 0===(e=this.findAppSegmentsInRange(e,i.byteLength)))return}}}findAppSegmentsInRange(e,t){t-=2;let i,n,s,r,a,o,{file:l,findAll:h,wanted:u,remaining:c,options:f}=this;for(;e<t;e++)if(255===l.getUint8(e))if(i=l.getUint8(e+1),oe(i)){if(n=l.getUint16(e+2),s=le(l,e,n),s&&u.has(s)&&(r=T.get(s),a=r.findPosition(l,e),o=f[s],a.type=s,this.appSegments.push(a),!h&&(r.multiSegment&&o.multiSegment?(this.unfinishedMultiSegment=a.chunkNumber<a.chunkCount,this.unfinishedMultiSegment||c.delete(s)):c.delete(s),0===c.size)))break;f.recordUnknownSegments&&(a=re.findPosition(l,e),a.marker=i,this.unknownSegments.push(a)),e+=n+1}else if(ae(i)){if(n=l.getUint16(e+2),218===i&&!1!==f.stopAfterSos)return;f.recordJpegSegments&&this.jpegSegments.push({offset:e,length:n,marker:i}),e+=n+1}return e}mergeMultiSegments(){if(!this.appSegments.some((e=>e.multiSegment)))return;let e=function(e,t){let i,n,s,r=new Map;for(let a=0;a<e.length;a++)i=e[a],n=i[t],r.has(n)?s=r.get(n):r.set(n,s=[]),s.push(i);return Array.from(r)}(this.appSegments,\"type\");this.mergedAppSegments=e.map((([e,t])=>{let i=T.get(e,this.options);if(i.handleMultiSegments){return{type:e,chunk:i.handleMultiSegments(t)}}return t[0]}))}getSegment(e){return this.appSegments.find((t=>t.type===e))}async getOrFindSegment(e){let t=this.getSegment(e);return void 0===t&&(await this.findAppSegments(0,[e]),t=this.getSegment(e)),t}}c(he,\"type\",\"jpeg\"),w.set(\"jpeg\",he);const ue=[void 0,1,1,2,4,8,1,1,2,4,8,4,8,4];class ce extends re{parseHeader(){var e=this.chunk.getUint16();18761===e?this.le=!0:19789===e&&(this.le=!1),this.chunk.le=this.le,this.headerParsed=!0}parseTags(e,t,i=new Map){let{pick:n,skip:s}=this.options[t];n=new Set(n);let r=n.size>0,a=0===s.size,o=this.chunk.getUint16(e);e+=2;for(let l=0;l<o;l++){let o=this.chunk.getUint16(e);if(r){if(n.has(o)&&(i.set(o,this.parseTag(e,o,t)),n.delete(o),0===n.size))break}else!a&&s.has(o)||i.set(o,this.parseTag(e,o,t));e+=12}return i}parseTag(e,t,i){let{chunk:n}=this,s=n.getUint16(e+2),r=n.getUint32(e+4),a=ue[s];if(a*r<=4?e+=8:e=n.getUint32(e+8),(s<1||s>13)&&g(`Invalid TIFF value type. block: ${i.toUpperCase()}, tag: ${t.toString(16)}, type: ${s}, offset ${e}`),e>n.byteLength&&g(`Invalid TIFF value offset. block: ${i.toUpperCase()}, tag: ${t.toString(16)}, type: ${s}, offset ${e} is outside of chunk size ${n.byteLength}`),1===s)return n.getUint8Array(e,r);if(2===s)return m(n.getString(e,r));if(7===s)return n.getUint8Array(e,r);if(1===r)return this.parseTagValue(s,e);{let t=new(function(e){switch(e){case 1:return Uint8Array;case 3:return Uint16Array;case 4:return Uint32Array;case 5:return Array;case 6:return Int8Array;case 8:return Int16Array;case 9:return Int32Array;case 10:return Array;case 11:return Float32Array;case 12:return Float64Array;default:return Array}}(s))(r),i=a;for(let n=0;n<r;n++)t[n]=this.parseTagValue(s,e),e+=i;return t}}parseTagValue(e,t){let{chunk:i}=this;switch(e){case 1:return i.getUint8(t);case 3:return i.getUint16(t);case 4:return i.getUint32(t);case 5:return i.getUint32(t)/i.getUint32(t+4);case 6:return i.getInt8(t);case 8:return i.getInt16(t);case 9:return i.getInt32(t);case 10:return i.getInt32(t)/i.getInt32(t+4);case 11:return i.getFloat(t);case 12:return i.getDouble(t);case 13:return i.getUint32(t);default:g(`Invalid tiff type ${e}`)}}}class fe extends ce{static canHandle(e,t){return 225===e.getUint8(t+1)&&1165519206===e.getUint32(t+4)&&0===e.getUint16(t+8)}async parse(){this.parseHeader();let{options:e}=this;return e.ifd0.enabled&&await this.parseIfd0Block(),e.exif.enabled&&await this.safeParse(\"parseExifBlock\"),e.gps.enabled&&await this.safeParse(\"parseGpsBlock\"),e.interop.enabled&&await this.safeParse(\"parseInteropBlock\"),e.ifd1.enabled&&await this.safeParse(\"parseThumbnailBlock\"),this.createOutput()}safeParse(e){let t=this[e]();return void 0!==t.catch&&(t=t.catch(this.handleError)),t}findIfd0Offset(){void 0===this.ifd0Offset&&(this.ifd0Offset=this.chunk.getUint32(4))}findIfd1Offset(){if(void 0===this.ifd1Offset){this.findIfd0Offset();let e=this.chunk.getUint16(this.ifd0Offset),t=this.ifd0Offset+2+12*e;this.ifd1Offset=this.chunk.getUint32(t)}}parseBlock(e,t){let i=new Map;return this[t]=i,this.parseTags(e,t,i),i}async parseIfd0Block(){if(this.ifd0)return;let{file:e}=this;this.findIfd0Offset(),this.ifd0Offset<8&&g(\"Malformed EXIF data\"),!e.chunked&&this.ifd0Offset>e.byteLength&&g(`IFD0 offset points to outside of file.\\nthis.ifd0Offset: ${this.ifd0Offset}, file.byteLength: ${e.byteLength}`),e.tiff&&await e.ensureChunk(this.ifd0Offset,S(this.options));let t=this.parseBlock(this.ifd0Offset,\"ifd0\");return 0!==t.size?(this.exifOffset=t.get(34665),this.interopOffset=t.get(40965),this.gpsOffset=t.get(34853),this.xmp=t.get(700),this.iptc=t.get(33723),this.icc=t.get(34675),this.options.sanitize&&(t.delete(34665),t.delete(40965),t.delete(34853),t.delete(700),t.delete(33723),t.delete(34675)),t):void 0}async parseExifBlock(){if(this.exif)return;if(this.ifd0||await this.parseIfd0Block(),void 0===this.exifOffset)return;this.file.tiff&&await this.file.ensureChunk(this.exifOffset,S(this.options));let e=this.parseBlock(this.exifOffset,\"exif\");return this.interopOffset||(this.interopOffset=e.get(40965)),this.makerNote=e.get(37500),this.userComment=e.get(37510),this.options.sanitize&&(e.delete(40965),e.delete(37500),e.delete(37510)),this.unpack(e,41728),this.unpack(e,41729),e}unpack(e,t){let i=e.get(t);i&&1===i.length&&e.set(t,i[0])}async parseGpsBlock(){if(this.gps)return;if(this.ifd0||await this.parseIfd0Block(),void 0===this.gpsOffset)return;let e=this.parseBlock(this.gpsOffset,\"gps\");return e&&e.has(2)&&e.has(4)&&(e.set(\"latitude\",de(...e.get(2),e.get(1))),e.set(\"longitude\",de(...e.get(4),e.get(3)))),e}async parseInteropBlock(){if(!this.interop&&(this.ifd0||await this.parseIfd0Block(),void 0!==this.interopOffset||this.exif||await this.parseExifBlock(),void 0!==this.interopOffset))return this.parseBlock(this.interopOffset,\"interop\")}async parseThumbnailBlock(e=!1){if(!this.ifd1&&!this.ifd1Parsed&&(!this.options.mergeOutput||e))return this.findIfd1Offset(),this.ifd1Offset>0&&(this.parseBlock(this.ifd1Offset,\"ifd1\"),this.ifd1Parsed=!0),this.ifd1}async extractThumbnail(){if(this.headerParsed||this.parseHeader(),this.ifd1Parsed||await this.parseThumbnailBlock(!0),void 0===this.ifd1)return;let e=this.ifd1.get(513),t=this.ifd1.get(514);return this.chunk.getUint8Array(e,t)}get image(){return this.ifd0}get thumbnail(){return this.ifd1}createOutput(){let e,t,i,n={};for(t of H)if(e=this[t],!p(e))if(i=this.canTranslate?this.translateBlock(e,t):Object.fromEntries(e),this.options.mergeOutput){if(\"ifd1\"===t)continue;Object.assign(n,i)}else n[t]=i;return this.makerNote&&(n.makerNote=this.makerNote),this.userComment&&(n.userComment=this.userComment),n}assignToOutput(e,t){if(this.globalOptions.mergeOutput)Object.assign(e,t);else for(let[i,n]of Object.entries(t))this.assignObjectToOutput(e,i,n)}}function de(e,t,i,n){var s=e+t/60+i/3600;return\"S\"!==n&&\"W\"!==n||(s*=-1),s}c(fe,\"type\",\"tiff\"),c(fe,\"headerLength\",10),T.set(\"tiff\",fe);var pe=Object.freeze({__proto__:null,default:ne,Exifr:te,fileParsers:w,segmentParsers:T,fileReaders:A,tagKeys:E,tagValues:B,tagRevivers:N,createDictionary:U,extendDictionary:F,fetchUrlAsArrayBuffer:M,readBlobAsArrayBuffer:R,chunkedProps:G,otherSegments:V,segments:z,tiffBlocks:H,segmentsAndBlocks:j,tiffExtractables:W,inheritables:K,allFormatters:X,Options:q,parse:ie});const ge={ifd0:!1,ifd1:!1,exif:!1,gps:!1,interop:!1,sanitize:!1,reviveValues:!0,translateKeys:!1,translateValues:!1,mergeOutput:!1},me=Object.assign({},ge,{firstChunkSize:4e4,gps:[1,2,3,4]});async function Se(e){let t=new te(me);await t.read(e);let i=await t.parse();if(i&&i.gps){let{latitude:e,longitude:t}=i.gps;return{latitude:e,longitude:t}}}const Ce=Object.assign({},ge,{tiff:!1,ifd1:!0,mergeOutput:!1});async function ye(e){let t=new te(Ce);await t.read(e);let i=await t.extractThumbnail();return i&&a?s.from(i):i}async function be(e){let t=await this.thumbnail(e);if(void 0!==t){let e=new Blob([t]);return URL.createObjectURL(e)}}const Ie=Object.assign({},ge,{firstChunkSize:4e4,ifd0:[274]});async function Pe(e){let t=new te(Ie);await t.read(e);let i=await t.parse();if(i&&i.ifd0)return i.ifd0[274]}const ke=Object.freeze({1:{dimensionSwapped:!1,scaleX:1,scaleY:1,deg:0,rad:0},2:{dimensionSwapped:!1,scaleX:-1,scaleY:1,deg:0,rad:0},3:{dimensionSwapped:!1,scaleX:1,scaleY:1,deg:180,rad:180*Math.PI/180},4:{dimensionSwapped:!1,scaleX:-1,scaleY:1,deg:180,rad:180*Math.PI/180},5:{dimensionSwapped:!0,scaleX:1,scaleY:-1,deg:90,rad:90*Math.PI/180},6:{dimensionSwapped:!0,scaleX:1,scaleY:1,deg:90,rad:90*Math.PI/180},7:{dimensionSwapped:!0,scaleX:1,scaleY:-1,deg:270,rad:270*Math.PI/180},8:{dimensionSwapped:!0,scaleX:1,scaleY:1,deg:270,rad:270*Math.PI/180}});let we=!0,Te=!0;if(\"object\"==typeof navigator){let e=navigator.userAgent;if(e.includes(\"iPad\")||e.includes(\"iPhone\")){let t=e.match(/OS (\\d+)_(\\d+)/);if(t){let[,e,i]=t,n=Number(e)+.1*Number(i);we=n<13.4,Te=!1}}else if(e.includes(\"OS X 10\")){let[,t]=e.match(/OS X 10[_.](\\d+)/);we=Te=Number(t)<15}if(e.includes(\"Chrome/\")){let[,t]=e.match(/Chrome\\/(\\d+)/);we=Te=Number(t)<81}else if(e.includes(\"Firefox/\")){let[,t]=e.match(/Firefox\\/(\\d+)/);we=Te=Number(t)<77}}async function Ae(e){let t=await Pe(e);return Object.assign({canvas:we,css:Te},ke[t])}class De extends I{constructor(...e){super(...e),c(this,\"ranges\",new Oe),0!==this.byteLength&&this.ranges.add(0,this.byteLength)}_tryExtend(e,t,i){if(0===e&&0===this.byteLength&&i){let e=new DataView(i.buffer||i,i.byteOffset,i.byteLength);this._swapDataView(e)}else{let i=e+t;if(i>this.byteLength){let{dataView:e}=this._extend(i);this._swapDataView(e)}}}_extend(e){let t;t=a?s.allocUnsafe(e):new Uint8Array(e);let i=new DataView(t.buffer,t.byteOffset,t.byteLength);return t.set(new Uint8Array(this.buffer,this.byteOffset,this.byteLength),0),{uintView:t,dataView:i}}subarray(e,t,i=!1){return t=t||this._lengthToEnd(e),i&&this._tryExtend(e,t),this.ranges.add(e,t),super.subarray(e,t)}set(e,t,i=!1){i&&this._tryExtend(t,e.byteLength,e);let n=super.set(e,t);return this.ranges.add(t,n.byteLength),n}async ensureChunk(e,t){this.chunked&&(this.ranges.available(e,t)||await this.readChunk(e,t))}available(e,t){return this.ranges.available(e,t)}}class Oe{constructor(){c(this,\"list\",[])}get length(){return this.list.length}add(e,t,i=0){let n=e+t,s=this.list.filter((t=>xe(e,t.offset,n)||xe(e,t.end,n)));if(s.length>0){e=Math.min(e,...s.map((e=>e.offset))),n=Math.max(n,...s.map((e=>e.end))),t=n-e;let i=s.shift();i.offset=e,i.length=t,i.end=n,this.list=this.list.filter((e=>!s.includes(e)))}else this.list.push({offset:e,length:t,end:n})}available(e,t){let i=e+t;return this.list.some((t=>t.offset<=e&&i<=t.end))}}function xe(e,t,i){return e<=t&&t<=i}class ve extends De{constructor(e,t){super(0),c(this,\"chunksRead\",0),this.input=e,this.options=t}async readWhole(){this.chunked=!1,await this.readChunk(this.nextChunkOffset)}async readChunked(){this.chunked=!0,await this.readChunk(0,this.options.firstChunkSize)}async readNextChunk(e=this.nextChunkOffset){if(this.fullyRead)return this.chunksRead++,!1;let t=this.options.chunkSize,i=await this.readChunk(e,t);return!!i&&i.byteLength===t}async readChunk(e,t){if(this.chunksRead++,0!==(t=this.safeWrapAddress(e,t)))return this._readChunk(e,t)}safeWrapAddress(e,t){return void 0!==this.size&&e+t>this.size?Math.max(0,this.size-e):t}get nextChunkOffset(){if(0!==this.ranges.list.length)return this.ranges.list[0].length}get canReadNextChunk(){return this.chunksRead<this.options.chunkLimit}get fullyRead(){return void 0!==this.size&&this.nextChunkOffset===this.size}read(){return this.options.chunked?this.readChunked():this.readWhole()}close(){}}A.set(\"blob\",class extends ve{async readWhole(){this.chunked=!1;let e=await R(this.input);this._swapArrayBuffer(e)}readChunked(){return this.chunked=!0,this.size=this.input.size,super.readChunked()}async _readChunk(e,t){let i=t?e+t:void 0,n=this.input.slice(e,i),s=await R(n);return this.set(s,e,!0)}});var Me=Object.freeze({__proto__:null,default:pe,Exifr:te,fileParsers:w,segmentParsers:T,fileReaders:A,tagKeys:E,tagValues:B,tagRevivers:N,createDictionary:U,extendDictionary:F,fetchUrlAsArrayBuffer:M,readBlobAsArrayBuffer:R,chunkedProps:G,otherSegments:V,segments:z,tiffBlocks:H,segmentsAndBlocks:j,tiffExtractables:W,inheritables:K,allFormatters:X,Options:q,parse:ie,gpsOnlyOptions:me,gps:Se,thumbnailOnlyOptions:Ce,thumbnail:ye,thumbnailUrl:be,orientationOnlyOptions:Ie,orientation:Pe,rotations:ke,get rotateCanvas(){return we},get rotateCss(){return Te},rotation:Ae});A.set(\"url\",class extends ve{async readWhole(){this.chunked=!1;let e=await M(this.input);e instanceof ArrayBuffer?this._swapArrayBuffer(e):e instanceof Uint8Array&&this._swapBuffer(e)}async _readChunk(e,t){let i=t?e+t-1:void 0,n=this.options.httpHeaders||{};(e||i)&&(n.range=`bytes=${[e,i].join(\"-\")}`);let s=await h(this.input,{headers:n}),r=await s.arrayBuffer(),a=r.byteLength;if(416!==s.status)return a!==t&&(this.size=e+a),this.set(r,e,!0)}});I.prototype.getUint64=function(e){let t=this.getUint32(e),i=this.getUint32(e+4);return t<1048575?t<<32|i:void 0!==typeof r?(console.warn(\"Using BigInt because of type 64uint but JS can only handle 53b numbers.\"),r(t)<<r(32)|r(i)):void g(\"Trying to read 64b value but JS can only handle 53b numbers.\")};class Re extends se{parseBoxes(e=0){let t=[];for(;e<this.file.byteLength-4;){let i=this.parseBoxHead(e);if(t.push(i),0===i.length)break;e+=i.length}return t}parseSubBoxes(e){e.boxes=this.parseBoxes(e.start)}findBox(e,t){return void 0===e.boxes&&this.parseSubBoxes(e),e.boxes.find((e=>e.kind===t))}parseBoxHead(e){let t=this.file.getUint32(e),i=this.file.getString(e+4,4),n=e+8;return 1===t&&(t=this.file.getUint64(e+8),n+=8),{offset:e,length:t,kind:i,start:n}}parseBoxFullHead(e){if(void 0!==e.version)return;let t=this.file.getUint32(e.start);e.version=t>>24,e.start+=4}}class Le extends Re{static canHandle(e,t){if(0!==t)return!1;let i=e.getUint16(2);if(i>50)return!1;let n=16,s=[];for(;n<i;)s.push(e.getString(n,4)),n+=4;return s.includes(this.type)}async parse(){let e=this.file.getUint32(0),t=this.parseBoxHead(e);for(;\"meta\"!==t.kind;)e+=t.length,await this.file.ensureChunk(e,16),t=this.parseBoxHead(e);await this.file.ensureChunk(t.offset,t.length),this.parseBoxFullHead(t),this.parseSubBoxes(t),this.options.icc.enabled&&await this.findIcc(t),this.options.tiff.enabled&&await this.findExif(t)}async registerSegment(e,t,i){await this.file.ensureChunk(t,i);let n=this.file.subarray(t,i);this.createParser(e,n)}async findIcc(e){let t=this.findBox(e,\"iprp\");if(void 0===t)return;let i=this.findBox(t,\"ipco\");if(void 0===i)return;let n=this.findBox(i,\"colr\");void 0!==n&&await this.registerSegment(\"icc\",n.offset+12,n.length)}async findExif(e){let t=this.findBox(e,\"iinf\");if(void 0===t)return;let i=this.findBox(e,\"iloc\");if(void 0===i)return;let n=this.findExifLocIdInIinf(t),s=this.findExtentInIloc(i,n);if(void 0===s)return;let[r,a]=s;await this.file.ensureChunk(r,a);let o=4+this.file.getUint32(r);r+=o,a-=o,await this.registerSegment(\"tiff\",r,a)}findExifLocIdInIinf(e){this.parseBoxFullHead(e);let t,i,n,s,r=e.start,a=this.file.getUint16(r);for(r+=2;a--;){if(t=this.parseBoxHead(r),this.parseBoxFullHead(t),i=t.start,t.version>=2&&(n=3===t.version?4:2,s=this.file.getString(i+n+2,4),\"Exif\"===s))return this.file.getUintBytes(i,n);r+=t.length}}get8bits(e){let t=this.file.getUint8(e);return[t>>4,15&t]}findExtentInIloc(e,t){this.parseBoxFullHead(e);let i=e.start,[n,s]=this.get8bits(i++),[r,a]=this.get8bits(i++),o=2===e.version?4:2,l=1===e.version||2===e.version?2:0,h=a+n+s,u=2===e.version?4:2,c=this.file.getUintBytes(i,u);for(i+=u;c--;){let e=this.file.getUintBytes(i,o);i+=o+l+2+r;let u=this.file.getUint16(i);if(i+=2,e===t)return u>1&&console.warn(\"ILOC box has more than one extent but we're only processing one\\nPlease create an issue at https://github.com/MikeKovarik/exifr with this file\"),[this.file.getUintBytes(i+a,n),this.file.getUintBytes(i+a+n,s)];i+=u*h}}}class Ue extends Le{}c(Ue,\"type\",\"heic\");class Fe extends Le{}c(Fe,\"type\",\"avif\"),w.set(\"heic\",Ue),w.set(\"avif\",Fe),U(E,[\"ifd0\",\"ifd1\"],[[256,\"ImageWidth\"],[257,\"ImageHeight\"],[258,\"BitsPerSample\"],[259,\"Compression\"],[262,\"PhotometricInterpretation\"],[270,\"ImageDescription\"],[271,\"Make\"],[272,\"Model\"],[273,\"StripOffsets\"],[274,\"Orientation\"],[277,\"SamplesPerPixel\"],[278,\"RowsPerStrip\"],[279,\"StripByteCounts\"],[282,\"XResolution\"],[283,\"YResolution\"],[284,\"PlanarConfiguration\"],[296,\"ResolutionUnit\"],[301,\"TransferFunction\"],[305,\"Software\"],[306,\"ModifyDate\"],[315,\"Artist\"],[316,\"HostComputer\"],[317,\"Predictor\"],[318,\"WhitePoint\"],[319,\"PrimaryChromaticities\"],[513,\"ThumbnailOffset\"],[514,\"ThumbnailLength\"],[529,\"YCbCrCoefficients\"],[530,\"YCbCrSubSampling\"],[531,\"YCbCrPositioning\"],[532,\"ReferenceBlackWhite\"],[700,\"ApplicationNotes\"],[33432,\"Copyright\"],[33723,\"IPTC\"],[34665,\"ExifIFD\"],[34675,\"ICC\"],[34853,\"GpsIFD\"],[330,\"SubIFD\"],[40965,\"InteropIFD\"],[40091,\"XPTitle\"],[40092,\"XPComment\"],[40093,\"XPAuthor\"],[40094,\"XPKeywords\"],[40095,\"XPSubject\"]]),U(E,\"exif\",[[33434,\"ExposureTime\"],[33437,\"FNumber\"],[34850,\"ExposureProgram\"],[34852,\"SpectralSensitivity\"],[34855,\"ISO\"],[34858,\"TimeZoneOffset\"],[34859,\"SelfTimerMode\"],[34864,\"SensitivityType\"],[34865,\"StandardOutputSensitivity\"],[34866,\"RecommendedExposureIndex\"],[34867,\"ISOSpeed\"],[34868,\"ISOSpeedLatitudeyyy\"],[34869,\"ISOSpeedLatitudezzz\"],[36864,\"ExifVersion\"],[36867,\"DateTimeOriginal\"],[36868,\"CreateDate\"],[36873,\"GooglePlusUploadCode\"],[36880,\"OffsetTime\"],[36881,\"OffsetTimeOriginal\"],[36882,\"OffsetTimeDigitized\"],[37121,\"ComponentsConfiguration\"],[37122,\"CompressedBitsPerPixel\"],[37377,\"ShutterSpeedValue\"],[37378,\"ApertureValue\"],[37379,\"BrightnessValue\"],[37380,\"ExposureCompensation\"],[37381,\"MaxApertureValue\"],[37382,\"SubjectDistance\"],[37383,\"MeteringMode\"],[37384,\"LightSource\"],[37385,\"Flash\"],[37386,\"FocalLength\"],[37393,\"ImageNumber\"],[37394,\"SecurityClassification\"],[37395,\"ImageHistory\"],[37396,\"SubjectArea\"],[37500,\"MakerNote\"],[37510,\"UserComment\"],[37520,\"SubSecTime\"],[37521,\"SubSecTimeOriginal\"],[37522,\"SubSecTimeDigitized\"],[37888,\"AmbientTemperature\"],[37889,\"Humidity\"],[37890,\"Pressure\"],[37891,\"WaterDepth\"],[37892,\"Acceleration\"],[37893,\"CameraElevationAngle\"],[40960,\"FlashpixVersion\"],[40961,\"ColorSpace\"],[40962,\"ExifImageWidth\"],[40963,\"ExifImageHeight\"],[40964,\"RelatedSoundFile\"],[41483,\"FlashEnergy\"],[41486,\"FocalPlaneXResolution\"],[41487,\"FocalPlaneYResolution\"],[41488,\"FocalPlaneResolutionUnit\"],[41492,\"SubjectLocation\"],[41493,\"ExposureIndex\"],[41495,\"SensingMethod\"],[41728,\"FileSource\"],[41729,\"SceneType\"],[41730,\"CFAPattern\"],[41985,\"CustomRendered\"],[41986,\"ExposureMode\"],[41987,\"WhiteBalance\"],[41988,\"DigitalZoomRatio\"],[41989,\"FocalLengthIn35mmFormat\"],[41990,\"SceneCaptureType\"],[41991,\"GainControl\"],[41992,\"Contrast\"],[41993,\"Saturation\"],[41994,\"Sharpness\"],[41996,\"SubjectDistanceRange\"],[42016,\"ImageUniqueID\"],[42032,\"OwnerName\"],[42033,\"SerialNumber\"],[42034,\"LensInfo\"],[42035,\"LensMake\"],[42036,\"LensModel\"],[42037,\"LensSerialNumber\"],[42080,\"CompositeImage\"],[42081,\"CompositeImageCount\"],[42082,\"CompositeImageExposureTimes\"],[42240,\"Gamma\"],[59932,\"Padding\"],[59933,\"OffsetSchema\"],[65e3,\"OwnerName\"],[65001,\"SerialNumber\"],[65002,\"Lens\"],[65100,\"RawFile\"],[65101,\"Converter\"],[65102,\"WhiteBalance\"],[65105,\"Exposure\"],[65106,\"Shadows\"],[65107,\"Brightness\"],[65108,\"Contrast\"],[65109,\"Saturation\"],[65110,\"Sharpness\"],[65111,\"Smoothness\"],[65112,\"MoireFilter\"],[40965,\"InteropIFD\"]]),U(E,\"gps\",[[0,\"GPSVersionID\"],[1,\"GPSLatitudeRef\"],[2,\"GPSLatitude\"],[3,\"GPSLongitudeRef\"],[4,\"GPSLongitude\"],[5,\"GPSAltitudeRef\"],[6,\"GPSAltitude\"],[7,\"GPSTimeStamp\"],[8,\"GPSSatellites\"],[9,\"GPSStatus\"],[10,\"GPSMeasureMode\"],[11,\"GPSDOP\"],[12,\"GPSSpeedRef\"],[13,\"GPSSpeed\"],[14,\"GPSTrackRef\"],[15,\"GPSTrack\"],[16,\"GPSImgDirectionRef\"],[17,\"GPSImgDirection\"],[18,\"GPSMapDatum\"],[19,\"GPSDestLatitudeRef\"],[20,\"GPSDestLatitude\"],[21,\"GPSDestLongitudeRef\"],[22,\"GPSDestLongitude\"],[23,\"GPSDestBearingRef\"],[24,\"GPSDestBearing\"],[25,\"GPSDestDistanceRef\"],[26,\"GPSDestDistance\"],[27,\"GPSProcessingMethod\"],[28,\"GPSAreaInformation\"],[29,\"GPSDateStamp\"],[30,\"GPSDifferential\"],[31,\"GPSHPositioningError\"]]),U(B,[\"ifd0\",\"ifd1\"],[[274,{1:\"Horizontal (normal)\",2:\"Mirror horizontal\",3:\"Rotate 180\",4:\"Mirror vertical\",5:\"Mirror horizontal and rotate 270 CW\",6:\"Rotate 90 CW\",7:\"Mirror horizontal and rotate 90 CW\",8:\"Rotate 270 CW\"}],[296,{1:\"None\",2:\"inches\",3:\"cm\"}]]);let Ee=U(B,\"exif\",[[34850,{0:\"Not defined\",1:\"Manual\",2:\"Normal program\",3:\"Aperture priority\",4:\"Shutter priority\",5:\"Creative program\",6:\"Action program\",7:\"Portrait mode\",8:\"Landscape mode\"}],[37121,{0:\"-\",1:\"Y\",2:\"Cb\",3:\"Cr\",4:\"R\",5:\"G\",6:\"B\"}],[37383,{0:\"Unknown\",1:\"Average\",2:\"CenterWeightedAverage\",3:\"Spot\",4:\"MultiSpot\",5:\"Pattern\",6:\"Partial\",255:\"Other\"}],[37384,{0:\"Unknown\",1:\"Daylight\",2:\"Fluorescent\",3:\"Tungsten (incandescent light)\",4:\"Flash\",9:\"Fine weather\",10:\"Cloudy weather\",11:\"Shade\",12:\"Daylight fluorescent (D 5700 - 7100K)\",13:\"Day white fluorescent (N 4600 - 5400K)\",14:\"Cool white fluorescent (W 3900 - 4500K)\",15:\"White fluorescent (WW 3200 - 3700K)\",17:\"Standard light A\",18:\"Standard light B\",19:\"Standard light C\",20:\"D55\",21:\"D65\",22:\"D75\",23:\"D50\",24:\"ISO studio tungsten\",255:\"Other\"}],[37385,{0:\"Flash did not fire\",1:\"Flash fired\",5:\"Strobe return light not detected\",7:\"Strobe return light detected\",9:\"Flash fired, compulsory flash mode\",13:\"Flash fired, compulsory flash mode, return light not detected\",15:\"Flash fired, compulsory flash mode, return light detected\",16:\"Flash did not fire, compulsory flash mode\",24:\"Flash did not fire, auto mode\",25:\"Flash fired, auto mode\",29:\"Flash fired, auto mode, return light not detected\",31:\"Flash fired, auto mode, return light detected\",32:\"No flash function\",65:\"Flash fired, red-eye reduction mode\",69:\"Flash fired, red-eye reduction mode, return light not detected\",71:\"Flash fired, red-eye reduction mode, return light detected\",73:\"Flash fired, compulsory flash mode, red-eye reduction mode\",77:\"Flash fired, compulsory flash mode, red-eye reduction mode, return light not detected\",79:\"Flash fired, compulsory flash mode, red-eye reduction mode, return light detected\",89:\"Flash fired, auto mode, red-eye reduction mode\",93:\"Flash fired, auto mode, return light not detected, red-eye reduction mode\",95:\"Flash fired, auto mode, return light detected, red-eye reduction mode\"}],[41495,{1:\"Not defined\",2:\"One-chip color area sensor\",3:\"Two-chip color area sensor\",4:\"Three-chip color area sensor\",5:\"Color sequential area sensor\",7:\"Trilinear sensor\",8:\"Color sequential linear sensor\"}],[41728,{1:\"Film Scanner\",2:\"Reflection Print Scanner\",3:\"Digital Camera\"}],[41729,{1:\"Directly photographed\"}],[41985,{0:\"Normal\",1:\"Custom\",2:\"HDR (no original saved)\",3:\"HDR (original saved)\",4:\"Original (for HDR)\",6:\"Panorama\",7:\"Portrait HDR\",8:\"Portrait\"}],[41986,{0:\"Auto\",1:\"Manual\",2:\"Auto bracket\"}],[41987,{0:\"Auto\",1:\"Manual\"}],[41990,{0:\"Standard\",1:\"Landscape\",2:\"Portrait\",3:\"Night\",4:\"Other\"}],[41991,{0:\"None\",1:\"Low gain up\",2:\"High gain up\",3:\"Low gain down\",4:\"High gain down\"}],[41996,{0:\"Unknown\",1:\"Macro\",2:\"Close\",3:\"Distant\"}],[42080,{0:\"Unknown\",1:\"Not a Composite Image\",2:\"General Composite Image\",3:\"Composite Image Captured While Shooting\"}]]);const Be={1:\"No absolute unit of measurement\",2:\"Inch\",3:\"Centimeter\"};Ee.set(37392,Be),Ee.set(41488,Be);const Ne={0:\"Normal\",1:\"Low\",2:\"High\"};function Ge(e){return\"object\"==typeof e&&void 0!==e.length?e[0]:e}function Ve(e){let t=Array.from(e).slice(1);return t[1]>15&&(t=t.map((e=>String.fromCharCode(e)))),\"0\"!==t[2]&&0!==t[2]||t.pop(),t.join(\".\")}function ze(e){if(\"string\"==typeof e){var[t,i,n,s,r,a]=e.trim().split(/[-: ]/g).map(Number),o=new Date(t,i-1,n);return Number.isNaN(s)||Number.isNaN(r)||Number.isNaN(a)||(o.setHours(s),o.setMinutes(r),o.setSeconds(a)),Number.isNaN(+o)?e:o}}function He(e){if(\"string\"==typeof e)return e;let t=[];if(0===e[1]&&0===e[e.length-1])for(let i=0;i<e.length;i+=2)t.push(je(e[i+1],e[i]));else for(let i=0;i<e.length;i+=2)t.push(je(e[i],e[i+1]));return m(String.fromCodePoint(...t))}function je(e,t){return e<<8|t}Ee.set(41992,Ne),Ee.set(41993,Ne),Ee.set(41994,Ne),U(N,[\"ifd0\",\"ifd1\"],[[50827,function(e){return\"string\"!=typeof e?b(e):e}],[306,ze],[40091,He],[40092,He],[40093,He],[40094,He],[40095,He]]),U(N,\"exif\",[[40960,Ve],[36864,Ve],[36867,ze],[36868,ze],[40962,Ge],[40963,Ge]]),U(N,\"gps\",[[0,e=>Array.from(e).join(\".\")],[7,e=>Array.from(e).join(\":\")]]);class We extends re{static canHandle(e,t){return 225===e.getUint8(t+1)&&1752462448===e.getUint32(t+4)&&\"http://ns.adobe.com/\"===e.getString(t+4,\"http://ns.adobe.com/\".length)}static headerLength(e,t){return\"http://ns.adobe.com/xmp/extension/\"===e.getString(t+4,\"http://ns.adobe.com/xmp/extension/\".length)?79:4+\"http://ns.adobe.com/xap/1.0/\".length+1}static findPosition(e,t){let i=super.findPosition(e,t);return i.multiSegment=i.extended=79===i.headerLength,i.multiSegment?(i.chunkCount=e.getUint8(t+72),i.chunkNumber=e.getUint8(t+76),0!==e.getUint8(t+77)&&i.chunkNumber++):(i.chunkCount=1/0,i.chunkNumber=-1),i}static handleMultiSegments(e){return e.map((e=>e.chunk.getString())).join(\"\")}normalizeInput(e){return\"string\"==typeof e?e:I.from(e).getString()}parse(e=this.chunk){if(!this.localOptions.parse)return e;e=function(e){let t={},i={};for(let e of Ze)t[e]=[],i[e]=0;return e.replace(et,((e,n,s)=>{if(\"<\"===n){let n=++i[s];return t[s].push(n),`${e}#${n}`}return`${e}#${t[s].pop()}`}))}(e);let t=Xe.findAll(e,\"rdf\",\"Description\");0===t.length&&t.push(new Xe(\"rdf\",\"Description\",void 0,e));let i,n={};for(let e of t)for(let t of e.properties)i=Je(t.ns,n),_e(t,i);return function(e){let t;for(let i in e)t=e[i]=f(e[i]),void 0===t&&delete e[i];return f(e)}(n)}assignToOutput(e,t){if(this.localOptions.parse)for(let[i,n]of Object.entries(t))switch(i){case\"tiff\":this.assignObjectToOutput(e,\"ifd0\",n);break;case\"exif\":this.assignObjectToOutput(e,\"exif\",n);break;case\"xmlns\":break;default:this.assignObjectToOutput(e,i,n)}else e.xmp=t}}c(We,\"type\",\"xmp\"),c(We,\"multiSegment\",!0),T.set(\"xmp\",We);class Ke{static findAll(e){return qe(e,/([a-zA-Z0-9-]+):([a-zA-Z0-9-]+)=(\"[^\"]*\"|'[^']*')/gm).map(Ke.unpackMatch)}static unpackMatch(e){let t=e[1],i=e[2],n=e[3].slice(1,-1);return n=Qe(n),new Ke(t,i,n)}constructor(e,t,i){this.ns=e,this.name=t,this.value=i}serialize(){return this.value}}class Xe{static findAll(e,t,i){if(void 0!==t||void 0!==i){t=t||\"[\\\\w\\\\d-]+\",i=i||\"[\\\\w\\\\d-]+\";var n=new RegExp(`<(${t}):(${i})(#\\\\d+)?((\\\\s+?[\\\\w\\\\d-:]+=(\"[^\"]*\"|'[^']*'))*\\\\s*)(\\\\/>|>([\\\\s\\\\S]*?)<\\\\/\\\\1:\\\\2\\\\3>)`,\"gm\")}else n=/<([\\w\\d-]+):([\\w\\d-]+)(#\\d+)?((\\s+?[\\w\\d-:]+=(\"[^\"]*\"|'[^']*'))*\\s*)(\\/>|>([\\s\\S]*?)<\\/\\1:\\2\\3>)/gm;return qe(e,n).map(Xe.unpackMatch)}static unpackMatch(e){let t=e[1],i=e[2],n=e[4],s=e[8];return new Xe(t,i,n,s)}constructor(e,t,i,n){this.ns=e,this.name=t,this.attrString=i,this.innerXml=n,this.attrs=Ke.findAll(i),this.children=Xe.findAll(n),this.value=0===this.children.length?Qe(n):void 0,this.properties=[...this.attrs,...this.children]}get isPrimitive(){return void 0!==this.value&&0===this.attrs.length&&0===this.children.length}get isListContainer(){return 1===this.children.length&&this.children[0].isList}get isList(){let{ns:e,name:t}=this;return\"rdf\"===e&&(\"Seq\"===t||\"Bag\"===t||\"Alt\"===t)}get isListItem(){return\"rdf\"===this.ns&&\"li\"===this.name}serialize(){if(0===this.properties.length&&void 0===this.value)return;if(this.isPrimitive)return this.value;if(this.isListContainer)return this.children[0].serialize();if(this.isList)return $e(this.children.map(Ye));if(this.isListItem&&1===this.children.length&&0===this.attrs.length)return this.children[0].serialize();let e={};for(let t of this.properties)_e(t,e);return void 0!==this.value&&(e.value=this.value),f(e)}}function _e(e,t){let i=e.serialize();void 0!==i&&(t[e.name]=i)}var Ye=e=>e.serialize(),$e=e=>1===e.length?e[0]:e,Je=(e,t)=>t[e]?t[e]:t[e]={};function qe(e,t){let i,n=[];if(!e)return n;for(;null!==(i=t.exec(e));)n.push(i);return n}function Qe(e){if(function(e){return null==e||\"null\"===e||\"undefined\"===e||\"\"===e||\"\"===e.trim()}(e))return;let t=Number(e);if(!Number.isNaN(t))return t;let i=e.toLowerCase();return\"true\"===i||\"false\"!==i&&e.trim()}const Ze=[\"rdf:li\",\"rdf:Seq\",\"rdf:Bag\",\"rdf:Alt\",\"rdf:Description\"],et=new RegExp(`(<|\\\\/)(${Ze.join(\"|\")})`,\"g\");var tt=Object.freeze({__proto__:null,default:Me,Exifr:te,fileParsers:w,segmentParsers:T,fileReaders:A,tagKeys:E,tagValues:B,tagRevivers:N,createDictionary:U,extendDictionary:F,fetchUrlAsArrayBuffer:M,readBlobAsArrayBuffer:R,chunkedProps:G,otherSegments:V,segments:z,tiffBlocks:H,segmentsAndBlocks:j,tiffExtractables:W,inheritables:K,allFormatters:X,Options:q,parse:ie,gpsOnlyOptions:me,gps:Se,thumbnailOnlyOptions:Ce,thumbnail:ye,thumbnailUrl:be,orientationOnlyOptions:Ie,orientation:Pe,rotations:ke,get rotateCanvas(){return we},get rotateCss(){return Te},rotation:Ae});const it=[\"xmp\",\"icc\",\"iptc\",\"tiff\"],nt=()=>{};async function st(e,t,i){let n=new q(t);n.chunked=!1,void 0===i&&\"string\"==typeof e&&(i=function(e){let t=e.toLowerCase().split(\".\").pop();if(function(e){return\"exif\"===e||\"tiff\"===e||\"tif\"===e}(t))return\"tiff\";if(it.includes(t))return t}(e));let s=await D(e,n);if(i){if(it.includes(i))return rt(i,s,n);g(\"Invalid segment type\")}else{if(function(e){let t=e.getString(0,50).trim();return t.includes(\"<?xpacket\")||t.includes(\"<x:\")}(s))return rt(\"xmp\",s,n);for(let[e]of T){if(!it.includes(e))continue;let t=await rt(e,s,n).catch(nt);if(t)return t}g(\"Unknown file format\")}}async function rt(e,t,i){let n=i[e];return n.enabled=!0,n.parse=!0,T.get(e).parse(t,n)}let at=l(\"fs\",(e=>e.promises));A.set(\"fs\",class extends ve{async readWhole(){this.chunked=!1,this.fs=await at;let e=await this.fs.readFile(this.input);this._swapBuffer(e)}async readChunked(){this.chunked=!0,this.fs=await at,await this.open(),await this.readChunk(0,this.options.firstChunkSize)}async open(){void 0===this.fh&&(this.fh=await this.fs.open(this.input,\"r\"),this.size=(await this.fh.stat(this.input)).size)}async _readChunk(e,t){void 0===this.fh&&await this.open(),e+t>this.size&&(t=this.size-e);var i=this.subarray(e,t,!0);return await this.fh.read(i.dataView,0,t,e),i}async close(){if(this.fh){let e=this.fh;this.fh=void 0,await e.close()}}});A.set(\"base64\",class extends ve{constructor(...e){super(...e),this.input=this.input.replace(/^data:([^;]+);base64,/gim,\"\"),this.size=this.input.length/4*3,this.input.endsWith(\"==\")?this.size-=2:this.input.endsWith(\"=\")&&(this.size-=1)}async _readChunk(e,t){let i,n,r=this.input;void 0===e?(e=0,i=0,n=0):(i=4*Math.floor(e/3),n=e-i/4*3),void 0===t&&(t=this.size);let o=e+t,l=i+4*Math.ceil(o/3);r=r.slice(i,l);let h=Math.min(t,this.size-e);if(a){let t=s.from(r,\"base64\").slice(n,n+h);return this.set(t,e,!0)}{let t=this.subarray(e,h,!0),i=atob(r),s=t.toUint8();for(let e=0;e<h;e++)s[e]=i.charCodeAt(n+e);return t}}});class ot extends se{static canHandle(e,t){return 18761===t||19789===t}extendOptions(e){let{ifd0:t,xmp:i,iptc:n,icc:s}=e;i.enabled&&t.deps.add(700),n.enabled&&t.deps.add(33723),s.enabled&&t.deps.add(34675),t.finalizeFilters()}async parse(){let{tiff:e,xmp:t,iptc:i,icc:n}=this.options;if(e.enabled||t.enabled||i.enabled||n.enabled){let e=Math.max(S(this.options),this.options.chunkSize);await this.file.ensureChunk(0,e),this.createParser(\"tiff\",this.file),this.parsers.tiff.parseHeader(),await this.parsers.tiff.parseIfd0Block(),this.adaptTiffPropAsSegment(\"xmp\"),this.adaptTiffPropAsSegment(\"iptc\"),this.adaptTiffPropAsSegment(\"icc\")}}adaptTiffPropAsSegment(e){if(this.parsers.tiff[e]){let t=this.parsers.tiff[e];this.injectSegment(e,t)}}}c(ot,\"type\",\"tiff\"),w.set(\"tiff\",ot);let lt=l(\"zlib\");const ht=[\"ihdr\",\"iccp\",\"text\",\"itxt\",\"exif\"];class ut extends se{constructor(...e){super(...e),c(this,\"catchError\",(e=>this.errors.push(e))),c(this,\"metaChunks\",[]),c(this,\"unknownChunks\",[])}static canHandle(e,t){return 35152===t&&2303741511===e.getUint32(0)&&218765834===e.getUint32(4)}async parse(){let{file:e}=this;await this.findPngChunksInRange(\"PNG\\r\\n\u001a\\n\".length,e.byteLength),await this.readSegments(this.metaChunks),this.findIhdr(),this.parseTextChunks(),await this.findExif().catch(this.catchError),await this.findXmp().catch(this.catchError),await this.findIcc().catch(this.catchError)}async findPngChunksInRange(e,t){let{file:i}=this;for(;e<t;){let t=i.getUint32(e),n=i.getUint32(e+4),s=i.getString(e+4,4).toLowerCase(),r=t+4+4+4,a={type:s,offset:e,length:r,start:e+4+4,size:t,marker:n};ht.includes(s)?this.metaChunks.push(a):this.unknownChunks.push(a),e+=r}}parseTextChunks(){let e=this.metaChunks.filter((e=>\"text\"===e.type));for(let t of e){let[e,i]=this.file.getString(t.start,t.size).split(\"\\0\");this.injectKeyValToIhdr(e,i)}}injectKeyValToIhdr(e,t){let i=this.parsers.ihdr;i&&i.raw.set(e,t)}findIhdr(){let e=this.metaChunks.find((e=>\"ihdr\"===e.type));e&&!1!==this.options.ihdr.enabled&&this.createParser(\"ihdr\",e.chunk)}async findExif(){let e=this.metaChunks.find((e=>\"exif\"===e.type));e&&this.injectSegment(\"tiff\",e.chunk)}async findXmp(){let e=this.metaChunks.filter((e=>\"itxt\"===e.type));for(let t of e){\"XML:com.adobe.xmp\"===t.chunk.getString(0,\"XML:com.adobe.xmp\".length)&&this.injectSegment(\"xmp\",t.chunk)}}async findIcc(){let e=this.metaChunks.find((e=>\"iccp\"===e.type));if(!e)return;let{chunk:t}=e,i=t.getUint8Array(0,81),s=0;for(;s<80&&0!==i[s];)s++;let r=s+2,a=t.getString(0,s);if(this.injectKeyValToIhdr(\"ProfileName\",a),n){let e=await lt,i=t.getUint8Array(r);i=e.inflateSync(i),this.injectSegment(\"icc\",i)}}}c(ut,\"type\",\"png\"),w.set(\"png\",ut),U(E,\"interop\",[[1,\"InteropIndex\"],[2,\"InteropVersion\"],[4096,\"RelatedImageFileFormat\"],[4097,\"RelatedImageWidth\"],[4098,\"RelatedImageHeight\"]]),F(E,\"ifd0\",[[11,\"ProcessingSoftware\"],[254,\"SubfileType\"],[255,\"OldSubfileType\"],[263,\"Thresholding\"],[264,\"CellWidth\"],[265,\"CellLength\"],[266,\"FillOrder\"],[269,\"DocumentName\"],[280,\"MinSampleValue\"],[281,\"MaxSampleValue\"],[285,\"PageName\"],[286,\"XPosition\"],[287,\"YPosition\"],[290,\"GrayResponseUnit\"],[297,\"PageNumber\"],[321,\"HalftoneHints\"],[322,\"TileWidth\"],[323,\"TileLength\"],[332,\"InkSet\"],[337,\"TargetPrinter\"],[18246,\"Rating\"],[18249,\"RatingPercent\"],[33550,\"PixelScale\"],[34264,\"ModelTransform\"],[34377,\"PhotoshopSettings\"],[50706,\"DNGVersion\"],[50707,\"DNGBackwardVersion\"],[50708,\"UniqueCameraModel\"],[50709,\"LocalizedCameraModel\"],[50736,\"DNGLensInfo\"],[50739,\"ShadowScale\"],[50740,\"DNGPrivateData\"],[33920,\"IntergraphMatrix\"],[33922,\"ModelTiePoint\"],[34118,\"SEMInfo\"],[34735,\"GeoTiffDirectory\"],[34736,\"GeoTiffDoubleParams\"],[34737,\"GeoTiffAsciiParams\"],[50341,\"PrintIM\"],[50721,\"ColorMatrix1\"],[50722,\"ColorMatrix2\"],[50723,\"CameraCalibration1\"],[50724,\"CameraCalibration2\"],[50725,\"ReductionMatrix1\"],[50726,\"ReductionMatrix2\"],[50727,\"AnalogBalance\"],[50728,\"AsShotNeutral\"],[50729,\"AsShotWhiteXY\"],[50730,\"BaselineExposure\"],[50731,\"BaselineNoise\"],[50732,\"BaselineSharpness\"],[50734,\"LinearResponseLimit\"],[50735,\"CameraSerialNumber\"],[50741,\"MakerNoteSafety\"],[50778,\"CalibrationIlluminant1\"],[50779,\"CalibrationIlluminant2\"],[50781,\"RawDataUniqueID\"],[50827,\"OriginalRawFileName\"],[50828,\"OriginalRawFileData\"],[50831,\"AsShotICCProfile\"],[50832,\"AsShotPreProfileMatrix\"],[50833,\"CurrentICCProfile\"],[50834,\"CurrentPreProfileMatrix\"],[50879,\"ColorimetricReference\"],[50885,\"SRawType\"],[50898,\"PanasonicTitle\"],[50899,\"PanasonicTitle2\"],[50931,\"CameraCalibrationSig\"],[50932,\"ProfileCalibrationSig\"],[50933,\"ProfileIFD\"],[50934,\"AsShotProfileName\"],[50936,\"ProfileName\"],[50937,\"ProfileHueSatMapDims\"],[50938,\"ProfileHueSatMapData1\"],[50939,\"ProfileHueSatMapData2\"],[50940,\"ProfileToneCurve\"],[50941,\"ProfileEmbedPolicy\"],[50942,\"ProfileCopyright\"],[50964,\"ForwardMatrix1\"],[50965,\"ForwardMatrix2\"],[50966,\"PreviewApplicationName\"],[50967,\"PreviewApplicationVersion\"],[50968,\"PreviewSettingsName\"],[50969,\"PreviewSettingsDigest\"],[50970,\"PreviewColorSpace\"],[50971,\"PreviewDateTime\"],[50972,\"RawImageDigest\"],[50973,\"OriginalRawFileDigest\"],[50981,\"ProfileLookTableDims\"],[50982,\"ProfileLookTableData\"],[51043,\"TimeCodes\"],[51044,\"FrameRate\"],[51058,\"TStop\"],[51081,\"ReelName\"],[51089,\"OriginalDefaultFinalSize\"],[51090,\"OriginalBestQualitySize\"],[51091,\"OriginalDefaultCropSize\"],[51105,\"CameraLabel\"],[51107,\"ProfileHueSatMapEncoding\"],[51108,\"ProfileLookTableEncoding\"],[51109,\"BaselineExposureOffset\"],[51110,\"DefaultBlackRender\"],[51111,\"NewRawImageDigest\"],[51112,\"RawToPreviewGain\"]]);let ct=[[273,\"StripOffsets\"],[279,\"StripByteCounts\"],[288,\"FreeOffsets\"],[289,\"FreeByteCounts\"],[291,\"GrayResponseCurve\"],[292,\"T4Options\"],[293,\"T6Options\"],[300,\"ColorResponseUnit\"],[320,\"ColorMap\"],[324,\"TileOffsets\"],[325,\"TileByteCounts\"],[326,\"BadFaxLines\"],[327,\"CleanFaxData\"],[328,\"ConsecutiveBadFaxLines\"],[330,\"SubIFD\"],[333,\"InkNames\"],[334,\"NumberofInks\"],[336,\"DotRange\"],[338,\"ExtraSamples\"],[339,\"SampleFormat\"],[340,\"SMinSampleValue\"],[341,\"SMaxSampleValue\"],[342,\"TransferRange\"],[343,\"ClipPath\"],[344,\"XClipPathUnits\"],[345,\"YClipPathUnits\"],[346,\"Indexed\"],[347,\"JPEGTables\"],[351,\"OPIProxy\"],[400,\"GlobalParametersIFD\"],[401,\"ProfileType\"],[402,\"FaxProfile\"],[403,\"CodingMethods\"],[404,\"VersionYear\"],[405,\"ModeNumber\"],[433,\"Decode\"],[434,\"DefaultImageColor\"],[435,\"T82Options\"],[437,\"JPEGTables\"],[512,\"JPEGProc\"],[515,\"JPEGRestartInterval\"],[517,\"JPEGLosslessPredictors\"],[518,\"JPEGPointTransforms\"],[519,\"JPEGQTables\"],[520,\"JPEGDCTables\"],[521,\"JPEGACTables\"],[559,\"StripRowCounts\"],[999,\"USPTOMiscellaneous\"],[18247,\"XP_DIP_XML\"],[18248,\"StitchInfo\"],[28672,\"SonyRawFileType\"],[28688,\"SonyToneCurve\"],[28721,\"VignettingCorrection\"],[28722,\"VignettingCorrParams\"],[28724,\"ChromaticAberrationCorrection\"],[28725,\"ChromaticAberrationCorrParams\"],[28726,\"DistortionCorrection\"],[28727,\"DistortionCorrParams\"],[29895,\"SonyCropTopLeft\"],[29896,\"SonyCropSize\"],[32781,\"ImageID\"],[32931,\"WangTag1\"],[32932,\"WangAnnotation\"],[32933,\"WangTag3\"],[32934,\"WangTag4\"],[32953,\"ImageReferencePoints\"],[32954,\"RegionXformTackPoint\"],[32955,\"WarpQuadrilateral\"],[32956,\"AffineTransformMat\"],[32995,\"Matteing\"],[32996,\"DataType\"],[32997,\"ImageDepth\"],[32998,\"TileDepth\"],[33300,\"ImageFullWidth\"],[33301,\"ImageFullHeight\"],[33302,\"TextureFormat\"],[33303,\"WrapModes\"],[33304,\"FovCot\"],[33305,\"MatrixWorldToScreen\"],[33306,\"MatrixWorldToCamera\"],[33405,\"Model2\"],[33421,\"CFARepeatPatternDim\"],[33422,\"CFAPattern2\"],[33423,\"BatteryLevel\"],[33424,\"KodakIFD\"],[33445,\"MDFileTag\"],[33446,\"MDScalePixel\"],[33447,\"MDColorTable\"],[33448,\"MDLabName\"],[33449,\"MDSampleInfo\"],[33450,\"MDPrepDate\"],[33451,\"MDPrepTime\"],[33452,\"MDFileUnits\"],[33589,\"AdventScale\"],[33590,\"AdventRevision\"],[33628,\"UIC1Tag\"],[33629,\"UIC2Tag\"],[33630,\"UIC3Tag\"],[33631,\"UIC4Tag\"],[33918,\"IntergraphPacketData\"],[33919,\"IntergraphFlagRegisters\"],[33921,\"INGRReserved\"],[34016,\"Site\"],[34017,\"ColorSequence\"],[34018,\"IT8Header\"],[34019,\"RasterPadding\"],[34020,\"BitsPerRunLength\"],[34021,\"BitsPerExtendedRunLength\"],[34022,\"ColorTable\"],[34023,\"ImageColorIndicator\"],[34024,\"BackgroundColorIndicator\"],[34025,\"ImageColorValue\"],[34026,\"BackgroundColorValue\"],[34027,\"PixelIntensityRange\"],[34028,\"TransparencyIndicator\"],[34029,\"ColorCharacterization\"],[34030,\"HCUsage\"],[34031,\"TrapIndicator\"],[34032,\"CMYKEquivalent\"],[34152,\"AFCP_IPTC\"],[34232,\"PixelMagicJBIGOptions\"],[34263,\"JPLCartoIFD\"],[34306,\"WB_GRGBLevels\"],[34310,\"LeafData\"],[34687,\"TIFF_FXExtensions\"],[34688,\"MultiProfiles\"],[34689,\"SharedData\"],[34690,\"T88Options\"],[34732,\"ImageLayer\"],[34750,\"JBIGOptions\"],[34856,\"Opto-ElectricConvFactor\"],[34857,\"Interlace\"],[34908,\"FaxRecvParams\"],[34909,\"FaxSubAddress\"],[34910,\"FaxRecvTime\"],[34929,\"FedexEDR\"],[34954,\"LeafSubIFD\"],[37387,\"FlashEnergy\"],[37388,\"SpatialFrequencyResponse\"],[37389,\"Noise\"],[37390,\"FocalPlaneXResolution\"],[37391,\"FocalPlaneYResolution\"],[37392,\"FocalPlaneResolutionUnit\"],[37397,\"ExposureIndex\"],[37398,\"TIFF-EPStandardID\"],[37399,\"SensingMethod\"],[37434,\"CIP3DataFile\"],[37435,\"CIP3Sheet\"],[37436,\"CIP3Side\"],[37439,\"StoNits\"],[37679,\"MSDocumentText\"],[37680,\"MSPropertySetStorage\"],[37681,\"MSDocumentTextPosition\"],[37724,\"ImageSourceData\"],[40965,\"InteropIFD\"],[40976,\"SamsungRawPointersOffset\"],[40977,\"SamsungRawPointersLength\"],[41217,\"SamsungRawByteOrder\"],[41218,\"SamsungRawUnknown\"],[41484,\"SpatialFrequencyResponse\"],[41485,\"Noise\"],[41489,\"ImageNumber\"],[41490,\"SecurityClassification\"],[41491,\"ImageHistory\"],[41494,\"TIFF-EPStandardID\"],[41995,\"DeviceSettingDescription\"],[42112,\"GDALMetadata\"],[42113,\"GDALNoData\"],[44992,\"ExpandSoftware\"],[44993,\"ExpandLens\"],[44994,\"ExpandFilm\"],[44995,\"ExpandFilterLens\"],[44996,\"ExpandScanner\"],[44997,\"ExpandFlashLamp\"],[46275,\"HasselbladRawImage\"],[48129,\"PixelFormat\"],[48130,\"Transformation\"],[48131,\"Uncompressed\"],[48132,\"ImageType\"],[48256,\"ImageWidth\"],[48257,\"ImageHeight\"],[48258,\"WidthResolution\"],[48259,\"HeightResolution\"],[48320,\"ImageOffset\"],[48321,\"ImageByteCount\"],[48322,\"AlphaOffset\"],[48323,\"AlphaByteCount\"],[48324,\"ImageDataDiscard\"],[48325,\"AlphaDataDiscard\"],[50215,\"OceScanjobDesc\"],[50216,\"OceApplicationSelector\"],[50217,\"OceIDNumber\"],[50218,\"OceImageLogic\"],[50255,\"Annotations\"],[50459,\"HasselbladExif\"],[50547,\"OriginalFileName\"],[50560,\"USPTOOriginalContentType\"],[50656,\"CR2CFAPattern\"],[50710,\"CFAPlaneColor\"],[50711,\"CFALayout\"],[50712,\"LinearizationTable\"],[50713,\"BlackLevelRepeatDim\"],[50714,\"BlackLevel\"],[50715,\"BlackLevelDeltaH\"],[50716,\"BlackLevelDeltaV\"],[50717,\"WhiteLevel\"],[50718,\"DefaultScale\"],[50719,\"DefaultCropOrigin\"],[50720,\"DefaultCropSize\"],[50733,\"BayerGreenSplit\"],[50737,\"ChromaBlurRadius\"],[50738,\"AntiAliasStrength\"],[50752,\"RawImageSegmentation\"],[50780,\"BestQualityScale\"],[50784,\"AliasLayerMetadata\"],[50829,\"ActiveArea\"],[50830,\"MaskedAreas\"],[50935,\"NoiseReductionApplied\"],[50974,\"SubTileBlockSize\"],[50975,\"RowInterleaveFactor\"],[51008,\"OpcodeList1\"],[51009,\"OpcodeList2\"],[51022,\"OpcodeList3\"],[51041,\"NoiseProfile\"],[51114,\"CacheVersion\"],[51125,\"DefaultUserCrop\"],[51157,\"NikonNEFInfo\"],[65024,\"KdcIFD\"]];F(E,\"ifd0\",ct),F(E,\"exif\",ct),U(B,\"gps\",[[23,{M:\"Magnetic North\",T:\"True North\"}],[25,{K:\"Kilometers\",M:\"Miles\",N:\"Nautical Miles\"}]]);class ft extends re{static canHandle(e,t){return 224===e.getUint8(t+1)&&1246120262===e.getUint32(t+4)&&0===e.getUint8(t+8)}parse(){return this.parseTags(),this.translate(),this.output}parseTags(){this.raw=new Map([[0,this.chunk.getUint16(0)],[2,this.chunk.getUint8(2)],[3,this.chunk.getUint16(3)],[5,this.chunk.getUint16(5)],[7,this.chunk.getUint8(7)],[8,this.chunk.getUint8(8)]])}}c(ft,\"type\",\"jfif\"),c(ft,\"headerLength\",9),T.set(\"jfif\",ft),U(E,\"jfif\",[[0,\"JFIFVersion\"],[2,\"ResolutionUnit\"],[3,\"XResolution\"],[5,\"YResolution\"],[7,\"ThumbnailWidth\"],[8,\"ThumbnailHeight\"]]);class dt extends re{parse(){return this.parseTags(),this.translate(),this.output}parseTags(){this.raw=new Map([[0,this.chunk.getUint32(0)],[4,this.chunk.getUint32(4)],[8,this.chunk.getUint8(8)],[9,this.chunk.getUint8(9)],[10,this.chunk.getUint8(10)],[11,this.chunk.getUint8(11)],[12,this.chunk.getUint8(12)],...Array.from(this.raw)])}}c(dt,\"type\",\"ihdr\"),T.set(\"ihdr\",dt),U(E,\"ihdr\",[[0,\"ImageWidth\"],[4,\"ImageHeight\"],[8,\"BitDepth\"],[9,\"ColorType\"],[10,\"Compression\"],[11,\"Filter\"],[12,\"Interlace\"]]),U(B,\"ihdr\",[[9,{0:\"Grayscale\",2:\"RGB\",3:\"Palette\",4:\"Grayscale with Alpha\",6:\"RGB with Alpha\",DEFAULT:\"Unknown\"}],[10,{0:\"Deflate/Inflate\",DEFAULT:\"Unknown\"}],[11,{0:\"Adaptive\",DEFAULT:\"Unknown\"}],[12,{0:\"Noninterlaced\",1:\"Adam7 Interlace\",DEFAULT:\"Unknown\"}]]);class pt extends re{static canHandle(e,t){return 226===e.getUint8(t+1)&&1229144927===e.getUint32(t+4)}static findPosition(e,t){let i=super.findPosition(e,t);return i.chunkNumber=e.getUint8(t+16),i.chunkCount=e.getUint8(t+17),i.multiSegment=i.chunkCount>1,i}static handleMultiSegments(e){return function(e){let t=function(e){let t=e[0].constructor,i=0;for(let t of e)i+=t.length;let n=new t(i),s=0;for(let t of e)n.set(t,s),s+=t.length;return n}(e.map((e=>e.chunk.toUint8())));return new I(t)}(e)}parse(){return this.raw=new Map,this.parseHeader(),this.parseTags(),this.translate(),this.output}parseHeader(){let{raw:e}=this;this.chunk.byteLength<84&&g(\"ICC header is too short\");for(let[t,i]of Object.entries(gt)){t=parseInt(t,10);let n=i(this.chunk,t);\"\\0\\0\\0\\0\"!==n&&e.set(t,n)}}parseTags(){let e,t,i,n,s,{raw:r}=this,a=this.chunk.getUint32(128),o=132,l=this.chunk.byteLength;for(;a--;){if(e=this.chunk.getString(o,4),t=this.chunk.getUint32(o+4),i=this.chunk.getUint32(o+8),n=this.chunk.getString(t,4),t+i>l)return void console.warn(\"reached the end of the first ICC chunk. Enable options.tiff.multiSegment to read all ICC segments.\");s=this.parseTag(n,t,i),void 0!==s&&\"\\0\\0\\0\\0\"!==s&&r.set(e,s),o+=12}}parseTag(e,t,i){switch(e){case\"desc\":return this.parseDesc(t);case\"mluc\":return this.parseMluc(t);case\"text\":return this.parseText(t,i);case\"sig \":return this.parseSig(t)}if(!(t+i>this.chunk.byteLength))return this.chunk.getUint8Array(t,i)}parseDesc(e){let t=this.chunk.getUint32(e+8)-1;return m(this.chunk.getString(e+12,t))}parseText(e,t){return m(this.chunk.getString(e+8,t-8))}parseSig(e){return m(this.chunk.getString(e+8,4))}parseMluc(e){let{chunk:t}=this,i=t.getUint32(e+8),n=t.getUint32(e+12),s=e+16,r=[];for(let a=0;a<i;a++){let i=t.getString(s+0,2),a=t.getString(s+2,2),o=t.getUint32(s+4),l=t.getUint32(s+8)+e,h=m(t.getUnicodeString(l,o));r.push({lang:i,country:a,text:h}),s+=n}return 1===i?r[0].text:r}translateValue(e,t){return\"string\"==typeof e?t[e]||t[e.toLowerCase()]||e:t[e]||e}}c(pt,\"type\",\"icc\"),c(pt,\"multiSegment\",!0),c(pt,\"headerLength\",18);const gt={4:mt,8:function(e,t){return[e.getUint8(t),e.getUint8(t+1)>>4,e.getUint8(t+1)%16].map((e=>e.toString(10))).join(\".\")},12:mt,16:mt,20:mt,24:function(e,t){const i=e.getUint16(t),n=e.getUint16(t+2)-1,s=e.getUint16(t+4),r=e.getUint16(t+6),a=e.getUint16(t+8),o=e.getUint16(t+10);return new Date(Date.UTC(i,n,s,r,a,o))},36:mt,40:mt,48:mt,52:mt,64:(e,t)=>e.getUint32(t),80:mt};function mt(e,t){return m(e.getString(t,4))}T.set(\"icc\",pt),U(E,\"icc\",[[4,\"ProfileCMMType\"],[8,\"ProfileVersion\"],[12,\"ProfileClass\"],[16,\"ColorSpaceData\"],[20,\"ProfileConnectionSpace\"],[24,\"ProfileDateTime\"],[36,\"ProfileFileSignature\"],[40,\"PrimaryPlatform\"],[44,\"CMMFlags\"],[48,\"DeviceManufacturer\"],[52,\"DeviceModel\"],[56,\"DeviceAttributes\"],[64,\"RenderingIntent\"],[68,\"ConnectionSpaceIlluminant\"],[80,\"ProfileCreator\"],[84,\"ProfileID\"],[\"Header\",\"ProfileHeader\"],[\"MS00\",\"WCSProfiles\"],[\"bTRC\",\"BlueTRC\"],[\"bXYZ\",\"BlueMatrixColumn\"],[\"bfd\",\"UCRBG\"],[\"bkpt\",\"MediaBlackPoint\"],[\"calt\",\"CalibrationDateTime\"],[\"chad\",\"ChromaticAdaptation\"],[\"chrm\",\"Chromaticity\"],[\"ciis\",\"ColorimetricIntentImageState\"],[\"clot\",\"ColorantTableOut\"],[\"clro\",\"ColorantOrder\"],[\"clrt\",\"ColorantTable\"],[\"cprt\",\"ProfileCopyright\"],[\"crdi\",\"CRDInfo\"],[\"desc\",\"ProfileDescription\"],[\"devs\",\"DeviceSettings\"],[\"dmdd\",\"DeviceModelDesc\"],[\"dmnd\",\"DeviceMfgDesc\"],[\"dscm\",\"ProfileDescriptionML\"],[\"fpce\",\"FocalPlaneColorimetryEstimates\"],[\"gTRC\",\"GreenTRC\"],[\"gXYZ\",\"GreenMatrixColumn\"],[\"gamt\",\"Gamut\"],[\"kTRC\",\"GrayTRC\"],[\"lumi\",\"Luminance\"],[\"meas\",\"Measurement\"],[\"meta\",\"Metadata\"],[\"mmod\",\"MakeAndModel\"],[\"ncl2\",\"NamedColor2\"],[\"ncol\",\"NamedColor\"],[\"ndin\",\"NativeDisplayInfo\"],[\"pre0\",\"Preview0\"],[\"pre1\",\"Preview1\"],[\"pre2\",\"Preview2\"],[\"ps2i\",\"PS2RenderingIntent\"],[\"ps2s\",\"PostScript2CSA\"],[\"psd0\",\"PostScript2CRD0\"],[\"psd1\",\"PostScript2CRD1\"],[\"psd2\",\"PostScript2CRD2\"],[\"psd3\",\"PostScript2CRD3\"],[\"pseq\",\"ProfileSequenceDesc\"],[\"psid\",\"ProfileSequenceIdentifier\"],[\"psvm\",\"PS2CRDVMSize\"],[\"rTRC\",\"RedTRC\"],[\"rXYZ\",\"RedMatrixColumn\"],[\"resp\",\"OutputResponse\"],[\"rhoc\",\"ReflectionHardcopyOrigColorimetry\"],[\"rig0\",\"PerceptualRenderingIntentGamut\"],[\"rig2\",\"SaturationRenderingIntentGamut\"],[\"rpoc\",\"ReflectionPrintOutputColorimetry\"],[\"sape\",\"SceneAppearanceEstimates\"],[\"scoe\",\"SceneColorimetryEstimates\"],[\"scrd\",\"ScreeningDesc\"],[\"scrn\",\"Screening\"],[\"targ\",\"CharTarget\"],[\"tech\",\"Technology\"],[\"vcgt\",\"VideoCardGamma\"],[\"view\",\"ViewingConditions\"],[\"vued\",\"ViewingCondDesc\"],[\"wtpt\",\"MediaWhitePoint\"]]);const St={\"4d2p\":\"Erdt Systems\",AAMA:\"Aamazing Technologies\",ACER:\"Acer\",ACLT:\"Acolyte Color Research\",ACTI:\"Actix Sytems\",ADAR:\"Adara Technology\",ADBE:\"Adobe\",ADI:\"ADI Systems\",AGFA:\"Agfa Graphics\",ALMD:\"Alps Electric\",ALPS:\"Alps Electric\",ALWN:\"Alwan Color Expertise\",AMTI:\"Amiable Technologies\",AOC:\"AOC International\",APAG:\"Apago\",APPL:\"Apple Computer\",AST:\"AST\",\"AT&T\":\"AT&T\",BAEL:\"BARBIERI electronic\",BRCO:\"Barco NV\",BRKP:\"Breakpoint\",BROT:\"Brother\",BULL:\"Bull\",BUS:\"Bus Computer Systems\",\"C-IT\":\"C-Itoh\",CAMR:\"Intel\",CANO:\"Canon\",CARR:\"Carroll Touch\",CASI:\"Casio\",CBUS:\"Colorbus PL\",CEL:\"Crossfield\",CELx:\"Crossfield\",CGS:\"CGS Publishing Technologies International\",CHM:\"Rochester Robotics\",CIGL:\"Colour Imaging Group, London\",CITI:\"Citizen\",CL00:\"Candela\",CLIQ:\"Color IQ\",CMCO:\"Chromaco\",CMiX:\"CHROMiX\",COLO:\"Colorgraphic Communications\",COMP:\"Compaq\",COMp:\"Compeq/Focus Technology\",CONR:\"Conrac Display Products\",CORD:\"Cordata Technologies\",CPQ:\"Compaq\",CPRO:\"ColorPro\",CRN:\"Cornerstone\",CTX:\"CTX International\",CVIS:\"ColorVision\",CWC:\"Fujitsu Laboratories\",DARI:\"Darius Technology\",DATA:\"Dataproducts\",DCP:\"Dry Creek Photo\",DCRC:\"Digital Contents Resource Center, Chung-Ang University\",DELL:\"Dell Computer\",DIC:\"Dainippon Ink and Chemicals\",DICO:\"Diconix\",DIGI:\"Digital\",\"DL&C\":\"Digital Light & Color\",DPLG:\"Doppelganger\",DS:\"Dainippon Screen\",DSOL:\"DOOSOL\",DUPN:\"DuPont\",EPSO:\"Epson\",ESKO:\"Esko-Graphics\",ETRI:\"Electronics and Telecommunications Research Institute\",EVER:\"Everex Systems\",EXAC:\"ExactCODE\",Eizo:\"Eizo\",FALC:\"Falco Data Products\",FF:\"Fuji Photo Film\",FFEI:\"FujiFilm Electronic Imaging\",FNRD:\"Fnord Software\",FORA:\"Fora\",FORE:\"Forefront Technology\",FP:\"Fujitsu\",FPA:\"WayTech Development\",FUJI:\"Fujitsu\",FX:\"Fuji Xerox\",GCC:\"GCC Technologies\",GGSL:\"Global Graphics Software\",GMB:\"Gretagmacbeth\",GMG:\"GMG\",GOLD:\"GoldStar Technology\",GOOG:\"Google\",GPRT:\"Giantprint\",GTMB:\"Gretagmacbeth\",GVC:\"WayTech Development\",GW2K:\"Sony\",HCI:\"HCI\",HDM:\"Heidelberger Druckmaschinen\",HERM:\"Hermes\",HITA:\"Hitachi America\",HP:\"Hewlett-Packard\",HTC:\"Hitachi\",HiTi:\"HiTi Digital\",IBM:\"IBM\",IDNT:\"Scitex\",IEC:\"Hewlett-Packard\",IIYA:\"Iiyama North America\",IKEG:\"Ikegami Electronics\",IMAG:\"Image Systems\",IMI:\"Ingram Micro\",INTC:\"Intel\",INTL:\"N/A (INTL)\",INTR:\"Intra Electronics\",IOCO:\"Iocomm International Technology\",IPS:\"InfoPrint Solutions Company\",IRIS:\"Scitex\",ISL:\"Ichikawa Soft Laboratory\",ITNL:\"N/A (ITNL)\",IVM:\"IVM\",IWAT:\"Iwatsu Electric\",Idnt:\"Scitex\",Inca:\"Inca Digital Printers\",Iris:\"Scitex\",JPEG:\"Joint Photographic Experts Group\",JSFT:\"Jetsoft Development\",JVC:\"JVC Information Products\",KART:\"Scitex\",KFC:\"KFC Computek Components\",KLH:\"KLH Computers\",KMHD:\"Konica Minolta\",KNCA:\"Konica\",KODA:\"Kodak\",KYOC:\"Kyocera\",Kart:\"Scitex\",LCAG:\"Leica\",LCCD:\"Leeds Colour\",LDAK:\"Left Dakota\",LEAD:\"Leading Technology\",LEXM:\"Lexmark International\",LINK:\"Link Computer\",LINO:\"Linotronic\",LITE:\"Lite-On\",Leaf:\"Leaf\",Lino:\"Linotronic\",MAGC:\"Mag Computronic\",MAGI:\"MAG Innovision\",MANN:\"Mannesmann\",MICN:\"Micron Technology\",MICR:\"Microtek\",MICV:\"Microvitec\",MINO:\"Minolta\",MITS:\"Mitsubishi Electronics America\",MITs:\"Mitsuba\",MNLT:\"Minolta\",MODG:\"Modgraph\",MONI:\"Monitronix\",MONS:\"Monaco Systems\",MORS:\"Morse Technology\",MOTI:\"Motive Systems\",MSFT:\"Microsoft\",MUTO:\"MUTOH INDUSTRIES\",Mits:\"Mitsubishi Electric\",NANA:\"NANAO\",NEC:\"NEC\",NEXP:\"NexPress Solutions\",NISS:\"Nissei Sangyo America\",NKON:\"Nikon\",NONE:\"none\",OCE:\"Oce Technologies\",OCEC:\"OceColor\",OKI:\"Oki\",OKID:\"Okidata\",OKIP:\"Okidata\",OLIV:\"Olivetti\",OLYM:\"Olympus\",ONYX:\"Onyx Graphics\",OPTI:\"Optiquest\",PACK:\"Packard Bell\",PANA:\"Matsushita Electric Industrial\",PANT:\"Pantone\",PBN:\"Packard Bell\",PFU:\"PFU\",PHIL:\"Philips Consumer Electronics\",PNTX:\"HOYA\",POne:\"Phase One A/S\",PREM:\"Premier Computer Innovations\",PRIN:\"Princeton Graphic Systems\",PRIP:\"Princeton Publishing Labs\",QLUX:\"Hong Kong\",QMS:\"QMS\",QPCD:\"QPcard AB\",QUAD:\"QuadLaser\",QUME:\"Qume\",RADI:\"Radius\",RDDx:\"Integrated Color Solutions\",RDG:\"Roland DG\",REDM:\"REDMS Group\",RELI:\"Relisys\",RGMS:\"Rolf Gierling Multitools\",RICO:\"Ricoh\",RNLD:\"Edmund Ronald\",ROYA:\"Royal\",RPC:\"Ricoh Printing Systems\",RTL:\"Royal Information Electronics\",SAMP:\"Sampo\",SAMS:\"Samsung\",SANT:\"Jaime Santana Pomares\",SCIT:\"Scitex\",SCRN:\"Dainippon Screen\",SDP:\"Scitex\",SEC:\"Samsung\",SEIK:\"Seiko Instruments\",SEIk:\"Seikosha\",SGUY:\"ScanGuy.com\",SHAR:\"Sharp Laboratories\",SICC:\"International Color Consortium\",SONY:\"Sony\",SPCL:\"SpectraCal\",STAR:\"Star\",STC:\"Sampo Technology\",Scit:\"Scitex\",Sdp:\"Scitex\",Sony:\"Sony\",TALO:\"Talon Technology\",TAND:\"Tandy\",TATU:\"Tatung\",TAXA:\"TAXAN America\",TDS:\"Tokyo Denshi Sekei\",TECO:\"TECO Information Systems\",TEGR:\"Tegra\",TEKT:\"Tektronix\",TI:\"Texas Instruments\",TMKR:\"TypeMaker\",TOSB:\"Toshiba\",TOSH:\"Toshiba\",TOTK:\"TOTOKU ELECTRIC\",TRIU:\"Triumph\",TSBT:\"Toshiba\",TTX:\"TTX Computer Products\",TVM:\"TVM Professional Monitor\",TW:\"TW Casper\",ULSX:\"Ulead Systems\",UNIS:\"Unisys\",UTZF:\"Utz Fehlau & Sohn\",VARI:\"Varityper\",VIEW:\"Viewsonic\",VISL:\"Visual communication\",VIVO:\"Vivo Mobile Communication\",WANG:\"Wang\",WLBR:\"Wilbur Imaging\",WTG2:\"Ware To Go\",WYSE:\"WYSE Technology\",XERX:\"Xerox\",XRIT:\"X-Rite\",ZRAN:\"Zoran\",Zebr:\"Zebra Technologies\",appl:\"Apple Computer\",bICC:\"basICColor\",berg:\"bergdesign\",ceyd:\"Integrated Color Solutions\",clsp:\"MacDermid ColorSpan\",ds:\"Dainippon Screen\",dupn:\"DuPont\",ffei:\"FujiFilm Electronic Imaging\",flux:\"FluxData\",iris:\"Scitex\",kart:\"Scitex\",lcms:\"Little CMS\",lino:\"Linotronic\",none:\"none\",ob4d:\"Erdt Systems\",obic:\"Medigraph\",quby:\"Qubyx Sarl\",scit:\"Scitex\",scrn:\"Dainippon Screen\",sdp:\"Scitex\",siwi:\"SIWI GRAFIKA\",yxym:\"YxyMaster\"},Ct={scnr:\"Scanner\",mntr:\"Monitor\",prtr:\"Printer\",link:\"Device Link\",abst:\"Abstract\",spac:\"Color Space Conversion Profile\",nmcl:\"Named Color\",cenc:\"ColorEncodingSpace profile\",mid:\"MultiplexIdentification profile\",mlnk:\"MultiplexLink profile\",mvis:\"MultiplexVisualization profile\",nkpf:\"Nikon Input Device Profile (NON-STANDARD!)\"};U(B,\"icc\",[[4,St],[12,Ct],[40,Object.assign({},St,Ct)],[48,St],[80,St],[64,{0:\"Perceptual\",1:\"Relative Colorimetric\",2:\"Saturation\",3:\"Absolute Colorimetric\"}],[\"tech\",{amd:\"Active Matrix Display\",crt:\"Cathode Ray Tube Display\",kpcd:\"Photo CD\",pmd:\"Passive Matrix Display\",dcam:\"Digital Camera\",dcpj:\"Digital Cinema Projector\",dmpc:\"Digital Motion Picture Camera\",dsub:\"Dye Sublimation Printer\",epho:\"Electrophotographic Printer\",esta:\"Electrostatic Printer\",flex:\"Flexography\",fprn:\"Film Writer\",fscn:\"Film Scanner\",grav:\"Gravure\",ijet:\"Ink Jet Printer\",imgs:\"Photo Image Setter\",mpfr:\"Motion Picture Film Recorder\",mpfs:\"Motion Picture Film Scanner\",offs:\"Offset Lithography\",pjtv:\"Projection Television\",rpho:\"Photographic Paper Printer\",rscn:\"Reflective Scanner\",silk:\"Silkscreen\",twax:\"Thermal Wax Printer\",vidc:\"Video Camera\",vidm:\"Video Monitor\"}]]);class yt extends re{static canHandle(e,t,i){return 237===e.getUint8(t+1)&&\"Photoshop\"===e.getString(t+4,9)&&void 0!==this.containsIptc8bim(e,t,i)}static headerLength(e,t,i){let n,s=this.containsIptc8bim(e,t,i);if(void 0!==s)return n=e.getUint8(t+s+7),n%2!=0&&(n+=1),0===n&&(n=4),s+8+n}static containsIptc8bim(e,t,i){for(let n=0;n<i;n++)if(this.isIptcSegmentHead(e,t+n))return n}static isIptcSegmentHead(e,t){return 56===e.getUint8(t)&&943868237===e.getUint32(t)&&1028===e.getUint16(t+4)}parse(){let{raw:e}=this,t=this.chunk.byteLength-1,i=!1;for(let n=0;n<t;n++)if(28===this.chunk.getUint8(n)&&2===this.chunk.getUint8(n+1)){i=!0;let t=this.chunk.getUint16(n+3),s=this.chunk.getUint8(n+2),r=this.chunk.getLatin1String(n+5,t);e.set(s,this.pluralizeValue(e.get(s),r)),n+=4+t}else if(i)break;return this.translate(),this.output}pluralizeValue(e,t){return void 0!==e?e instanceof Array?(e.push(t),e):[e,t]:t}}c(yt,\"type\",\"iptc\"),c(yt,\"translateValues\",!1),c(yt,\"reviveValues\",!1),T.set(\"iptc\",yt),U(E,\"iptc\",[[0,\"ApplicationRecordVersion\"],[3,\"ObjectTypeReference\"],[4,\"ObjectAttributeReference\"],[5,\"ObjectName\"],[7,\"EditStatus\"],[8,\"EditorialUpdate\"],[10,\"Urgency\"],[12,\"SubjectReference\"],[15,\"Category\"],[20,\"SupplementalCategories\"],[22,\"FixtureIdentifier\"],[25,\"Keywords\"],[26,\"ContentLocationCode\"],[27,\"ContentLocationName\"],[30,\"ReleaseDate\"],[35,\"ReleaseTime\"],[37,\"ExpirationDate\"],[38,\"ExpirationTime\"],[40,\"SpecialInstructions\"],[42,\"ActionAdvised\"],[45,\"ReferenceService\"],[47,\"ReferenceDate\"],[50,\"ReferenceNumber\"],[55,\"DateCreated\"],[60,\"TimeCreated\"],[62,\"DigitalCreationDate\"],[63,\"DigitalCreationTime\"],[65,\"OriginatingProgram\"],[70,\"ProgramVersion\"],[75,\"ObjectCycle\"],[80,\"Byline\"],[85,\"BylineTitle\"],[90,\"City\"],[92,\"Sublocation\"],[95,\"State\"],[100,\"CountryCode\"],[101,\"Country\"],[103,\"OriginalTransmissionReference\"],[105,\"Headline\"],[110,\"Credit\"],[115,\"Source\"],[116,\"CopyrightNotice\"],[118,\"Contact\"],[120,\"Caption\"],[121,\"LocalCaption\"],[122,\"Writer\"],[125,\"RasterizedCaption\"],[130,\"ImageType\"],[131,\"ImageOrientation\"],[135,\"LanguageIdentifier\"],[150,\"AudioType\"],[151,\"AudioSamplingRate\"],[152,\"AudioSamplingResolution\"],[153,\"AudioDuration\"],[154,\"AudioOutcue\"],[184,\"JobID\"],[185,\"MasterDocumentID\"],[186,\"ShortDocumentID\"],[187,\"UniqueDocumentID\"],[188,\"OwnerID\"],[200,\"ObjectPreviewFileFormat\"],[201,\"ObjectPreviewFileVersion\"],[202,\"ObjectPreviewData\"],[221,\"Prefs\"],[225,\"ClassifyState\"],[228,\"SimilarityIndex\"],[230,\"DocumentNotes\"],[231,\"DocumentHistory\"],[232,\"ExifCameraInfo\"],[255,\"CatalogSets\"]]),U(B,\"iptc\",[[10,{0:\"0 (reserved)\",1:\"1 (most urgent)\",2:\"2\",3:\"3\",4:\"4\",5:\"5 (normal urgency)\",6:\"6\",7:\"7\",8:\"8 (least urgent)\",9:\"9 (user-defined priority)\"}],[75,{a:\"Morning\",b:\"Both Morning and Evening\",p:\"Evening\"}],[131,{L:\"Landscape\",P:\"Portrait\",S:\"Square\"}]]);/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (tt);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/exifr/dist/full.esm.mjs\n");

/***/ })

};
;