"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@googlemaps";
exports.ids = ["vendor-chunks/@googlemaps"];
exports.modules = {

/***/ "(ssr)/./node_modules/@googlemaps/js-api-loader/dist/index.mjs":
/*!***************************************************************!*\
  !*** ./node_modules/@googlemaps/js-api-loader/dist/index.mjs ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DEFAULT_ID: () => (/* binding */ DEFAULT_ID),\n/* harmony export */   Loader: () => (/* binding */ Loader),\n/* harmony export */   LoaderStatus: () => (/* binding */ LoaderStatus)\n/* harmony export */ });\n/******************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n/* global Reflect, Promise, SuppressedError, Symbol, Iterator */\r\n\r\n\r\nfunction __awaiter(thisArg, _arguments, P, generator) {\r\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n}\r\n\r\ntypeof SuppressedError === \"function\" ? SuppressedError : function (error, suppressed, message) {\r\n    var e = new Error(message);\r\n    return e.name = \"SuppressedError\", e.error = error, e.suppressed = suppressed, e;\r\n};\n\nfunction getDefaultExportFromCjs (x) {\n\treturn x && x.__esModule && Object.prototype.hasOwnProperty.call(x, 'default') ? x['default'] : x;\n}\n\nvar fastDeepEqual;\nvar hasRequiredFastDeepEqual;\n\nfunction requireFastDeepEqual () {\n\tif (hasRequiredFastDeepEqual) return fastDeepEqual;\n\thasRequiredFastDeepEqual = 1;\n\n\t// do not edit .js files directly - edit src/index.jst\n\n\n\n\tfastDeepEqual = function equal(a, b) {\n\t  if (a === b) return true;\n\n\t  if (a && b && typeof a == 'object' && typeof b == 'object') {\n\t    if (a.constructor !== b.constructor) return false;\n\n\t    var length, i, keys;\n\t    if (Array.isArray(a)) {\n\t      length = a.length;\n\t      if (length != b.length) return false;\n\t      for (i = length; i-- !== 0;)\n\t        if (!equal(a[i], b[i])) return false;\n\t      return true;\n\t    }\n\n\n\n\t    if (a.constructor === RegExp) return a.source === b.source && a.flags === b.flags;\n\t    if (a.valueOf !== Object.prototype.valueOf) return a.valueOf() === b.valueOf();\n\t    if (a.toString !== Object.prototype.toString) return a.toString() === b.toString();\n\n\t    keys = Object.keys(a);\n\t    length = keys.length;\n\t    if (length !== Object.keys(b).length) return false;\n\n\t    for (i = length; i-- !== 0;)\n\t      if (!Object.prototype.hasOwnProperty.call(b, keys[i])) return false;\n\n\t    for (i = length; i-- !== 0;) {\n\t      var key = keys[i];\n\n\t      if (!equal(a[key], b[key])) return false;\n\t    }\n\n\t    return true;\n\t  }\n\n\t  // true if both NaN, false otherwise\n\t  return a!==a && b!==b;\n\t};\n\treturn fastDeepEqual;\n}\n\nvar fastDeepEqualExports = requireFastDeepEqual();\nvar isEqual = /*@__PURE__*/getDefaultExportFromCjs(fastDeepEqualExports);\n\n/**\n * Copyright 2019 Google LLC. All Rights Reserved.\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at.\n *\n *      Http://www.apache.org/licenses/LICENSE-2.0.\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nconst DEFAULT_ID = \"__googleMapsScriptId\";\n/**\n * The status of the [[Loader]].\n */\nvar LoaderStatus;\n(function (LoaderStatus) {\n    LoaderStatus[LoaderStatus[\"INITIALIZED\"] = 0] = \"INITIALIZED\";\n    LoaderStatus[LoaderStatus[\"LOADING\"] = 1] = \"LOADING\";\n    LoaderStatus[LoaderStatus[\"SUCCESS\"] = 2] = \"SUCCESS\";\n    LoaderStatus[LoaderStatus[\"FAILURE\"] = 3] = \"FAILURE\";\n})(LoaderStatus || (LoaderStatus = {}));\n/**\n * [[Loader]] makes it easier to add Google Maps JavaScript API to your application\n * dynamically using\n * [Promises](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise).\n * It works by dynamically creating and appending a script node to the the\n * document head and wrapping the callback function so as to return a promise.\n *\n * ```\n * const loader = new Loader({\n *   apiKey: \"\",\n *   version: \"weekly\",\n *   libraries: [\"places\"]\n * });\n *\n * loader.load().then((google) => {\n *   const map = new google.maps.Map(...)\n * })\n * ```\n */\nclass Loader {\n    /**\n     * Creates an instance of Loader using [[LoaderOptions]]. No defaults are set\n     * using this library, instead the defaults are set by the Google Maps\n     * JavaScript API server.\n     *\n     * ```\n     * const loader = Loader({apiKey, version: 'weekly', libraries: ['places']});\n     * ```\n     */\n    constructor({ apiKey, authReferrerPolicy, channel, client, id = DEFAULT_ID, language, libraries = [], mapIds, nonce, region, retries = 3, url = \"https://maps.googleapis.com/maps/api/js\", version, }) {\n        this.callbacks = [];\n        this.done = false;\n        this.loading = false;\n        this.errors = [];\n        this.apiKey = apiKey;\n        this.authReferrerPolicy = authReferrerPolicy;\n        this.channel = channel;\n        this.client = client;\n        this.id = id || DEFAULT_ID; // Do not allow empty string\n        this.language = language;\n        this.libraries = libraries;\n        this.mapIds = mapIds;\n        this.nonce = nonce;\n        this.region = region;\n        this.retries = retries;\n        this.url = url;\n        this.version = version;\n        if (Loader.instance) {\n            if (!isEqual(this.options, Loader.instance.options)) {\n                throw new Error(`Loader must not be called again with different options. ${JSON.stringify(this.options)} !== ${JSON.stringify(Loader.instance.options)}`);\n            }\n            return Loader.instance;\n        }\n        Loader.instance = this;\n    }\n    get options() {\n        return {\n            version: this.version,\n            apiKey: this.apiKey,\n            channel: this.channel,\n            client: this.client,\n            id: this.id,\n            libraries: this.libraries,\n            language: this.language,\n            region: this.region,\n            mapIds: this.mapIds,\n            nonce: this.nonce,\n            url: this.url,\n            authReferrerPolicy: this.authReferrerPolicy,\n        };\n    }\n    get status() {\n        if (this.errors.length) {\n            return LoaderStatus.FAILURE;\n        }\n        if (this.done) {\n            return LoaderStatus.SUCCESS;\n        }\n        if (this.loading) {\n            return LoaderStatus.LOADING;\n        }\n        return LoaderStatus.INITIALIZED;\n    }\n    get failed() {\n        return this.done && !this.loading && this.errors.length >= this.retries + 1;\n    }\n    /**\n     * CreateUrl returns the Google Maps JavaScript API script url given the [[LoaderOptions]].\n     *\n     * @ignore\n     * @deprecated\n     */\n    createUrl() {\n        let url = this.url;\n        url += `?callback=__googleMapsCallback&loading=async`;\n        if (this.apiKey) {\n            url += `&key=${this.apiKey}`;\n        }\n        if (this.channel) {\n            url += `&channel=${this.channel}`;\n        }\n        if (this.client) {\n            url += `&client=${this.client}`;\n        }\n        if (this.libraries.length > 0) {\n            url += `&libraries=${this.libraries.join(\",\")}`;\n        }\n        if (this.language) {\n            url += `&language=${this.language}`;\n        }\n        if (this.region) {\n            url += `&region=${this.region}`;\n        }\n        if (this.version) {\n            url += `&v=${this.version}`;\n        }\n        if (this.mapIds) {\n            url += `&map_ids=${this.mapIds.join(\",\")}`;\n        }\n        if (this.authReferrerPolicy) {\n            url += `&auth_referrer_policy=${this.authReferrerPolicy}`;\n        }\n        return url;\n    }\n    deleteScript() {\n        const script = document.getElementById(this.id);\n        if (script) {\n            script.remove();\n        }\n    }\n    /**\n     * Load the Google Maps JavaScript API script and return a Promise.\n     * @deprecated, use importLibrary() instead.\n     */\n    load() {\n        return this.loadPromise();\n    }\n    /**\n     * Load the Google Maps JavaScript API script and return a Promise.\n     *\n     * @ignore\n     * @deprecated, use importLibrary() instead.\n     */\n    loadPromise() {\n        return new Promise((resolve, reject) => {\n            this.loadCallback((err) => {\n                if (!err) {\n                    resolve(window.google);\n                }\n                else {\n                    reject(err.error);\n                }\n            });\n        });\n    }\n    importLibrary(name) {\n        this.execute();\n        return google.maps.importLibrary(name);\n    }\n    /**\n     * Load the Google Maps JavaScript API script with a callback.\n     * @deprecated, use importLibrary() instead.\n     */\n    loadCallback(fn) {\n        this.callbacks.push(fn);\n        this.execute();\n    }\n    /**\n     * Set the script on document.\n     */\n    setScript() {\n        var _a, _b;\n        if (document.getElementById(this.id)) {\n            // TODO wrap onerror callback for cases where the script was loaded elsewhere\n            this.callback();\n            return;\n        }\n        const params = {\n            key: this.apiKey,\n            channel: this.channel,\n            client: this.client,\n            libraries: this.libraries.length && this.libraries,\n            v: this.version,\n            mapIds: this.mapIds,\n            language: this.language,\n            region: this.region,\n            authReferrerPolicy: this.authReferrerPolicy,\n        };\n        // keep the URL minimal:\n        Object.keys(params).forEach(\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        (key) => !params[key] && delete params[key]);\n        if (!((_b = (_a = window === null || window === void 0 ? void 0 : window.google) === null || _a === void 0 ? void 0 : _a.maps) === null || _b === void 0 ? void 0 : _b.importLibrary)) {\n            // tweaked copy of https://developers.google.com/maps/documentation/javascript/load-maps-js-api#dynamic-library-import\n            // which also sets the base url, the id, and the nonce\n            /* eslint-disable */\n            ((g) => {\n                // @ts-ignore\n                let h, a, k, p = \"The Google Maps JavaScript API\", c = \"google\", l = \"importLibrary\", q = \"__ib__\", m = document, b = window;\n                // @ts-ignore\n                b = b[c] || (b[c] = {});\n                // @ts-ignore\n                const d = b.maps || (b.maps = {}), r = new Set(), e = new URLSearchParams(), u = () => \n                // @ts-ignore\n                h || (h = new Promise((f, n) => __awaiter(this, void 0, void 0, function* () {\n                    var _a;\n                    yield (a = m.createElement(\"script\"));\n                    a.id = this.id;\n                    e.set(\"libraries\", [...r] + \"\");\n                    // @ts-ignore\n                    for (k in g)\n                        e.set(k.replace(/[A-Z]/g, (t) => \"_\" + t[0].toLowerCase()), g[k]);\n                    e.set(\"callback\", c + \".maps.\" + q);\n                    a.src = this.url + `?` + e;\n                    d[q] = f;\n                    a.onerror = () => (h = n(Error(p + \" could not load.\")));\n                    // @ts-ignore\n                    a.nonce = this.nonce || ((_a = m.querySelector(\"script[nonce]\")) === null || _a === void 0 ? void 0 : _a.nonce) || \"\";\n                    m.head.append(a);\n                })));\n                // @ts-ignore\n                d[l] ? console.warn(p + \" only loads once. Ignoring:\", g) : (d[l] = (f, ...n) => r.add(f) && u().then(() => d[l](f, ...n)));\n            })(params);\n            /* eslint-enable */\n        }\n        // While most libraries populate the global namespace when loaded via bootstrap params,\n        // this is not the case for \"marker\" when used with the inline bootstrap loader\n        // (and maybe others in the future). So ensure there is an importLibrary for each:\n        const libraryPromises = this.libraries.map((library) => this.importLibrary(library));\n        // ensure at least one library, to kick off loading...\n        if (!libraryPromises.length) {\n            libraryPromises.push(this.importLibrary(\"core\"));\n        }\n        Promise.all(libraryPromises).then(() => this.callback(), (error) => {\n            const event = new ErrorEvent(\"error\", { error }); // for backwards compat\n            this.loadErrorCallback(event);\n        });\n    }\n    /**\n     * Reset the loader state.\n     */\n    reset() {\n        this.deleteScript();\n        this.done = false;\n        this.loading = false;\n        this.errors = [];\n        this.onerrorEvent = null;\n    }\n    resetIfRetryingFailed() {\n        if (this.failed) {\n            this.reset();\n        }\n    }\n    loadErrorCallback(e) {\n        this.errors.push(e);\n        if (this.errors.length <= this.retries) {\n            const delay = this.errors.length * Math.pow(2, this.errors.length);\n            console.error(`Failed to load Google Maps script, retrying in ${delay} ms.`);\n            setTimeout(() => {\n                this.deleteScript();\n                this.setScript();\n            }, delay);\n        }\n        else {\n            this.onerrorEvent = e;\n            this.callback();\n        }\n    }\n    callback() {\n        this.done = true;\n        this.loading = false;\n        this.callbacks.forEach((cb) => {\n            cb(this.onerrorEvent);\n        });\n        this.callbacks = [];\n    }\n    execute() {\n        this.resetIfRetryingFailed();\n        if (this.loading) {\n            // do nothing but wait\n            return;\n        }\n        if (this.done) {\n            this.callback();\n        }\n        else {\n            // short circuit and warn if google.maps is already loaded\n            if (window.google && window.google.maps && window.google.maps.version) {\n                console.warn(\"Google Maps already loaded outside @googlemaps/js-api-loader. \" +\n                    \"This may result in undesirable behavior as options and script parameters may not match.\");\n                this.callback();\n                return;\n            }\n            this.loading = true;\n            this.setScript();\n        }\n    }\n}\n\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@googlemaps/js-api-loader/dist/index.mjs\n");

/***/ })

};
;