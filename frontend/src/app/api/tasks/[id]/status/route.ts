import { NextRequest, NextResponse } from 'next/server'
import { createServerSupabaseClient } from '@/lib/auth-server'

// GET /api/tasks/[id]/status - Fast status check
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  const startTime = Date.now()
  
  try {
    const supabase = await createServerSupabaseClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const taskId = params.id

    // Ultra-fast query - only status and timestamp
    const { data: task, error } = await supabase
      .from('tasks')
      .select('id, status, updated_at')
      .eq('id', taskId)
      .single()

    if (error) {
      console.error('❌ Error fetching task status:', error)
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    const responseTime = Date.now() - startTime
    console.log(`⚡ Task status fetched in ${responseTime}ms`)

    return NextResponse.json({
      status: task.status,
      updated_at: task.updated_at
    })

  } catch (error) {
    console.error('💥 Task status fetch error:', error)
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to fetch task status' },
      { status: 500 }
    )
  }
}

// PUT /api/tasks/[id]/status - Ultra-fast status update
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  const startTime = Date.now()
  
  try {
    const supabase = await createServerSupabaseClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const taskId = params.id
    const { status, ...additionalUpdates } = await request.json()

    console.log('⚡ Ultra-fast status update:', taskId, status)

    // Build update payload with automatic timestamps
    const updatePayload: any = {
      status,
      updated_at: new Date().toISOString(),
      ...additionalUpdates
    }

    // Add status-specific timestamps
    if (status === 'in_progress') {
      updatePayload.started_at = new Date().toISOString()
    } else if (status === 'completed') {
      updatePayload.completed_at = new Date().toISOString()
    }

    // Single atomic update with minimal response
    const { data: task, error } = await supabase
      .from('tasks')
      .update(updatePayload)
      .eq('id', taskId)
      .select('id, status, updated_at, title')
      .single()

    if (error) {
      console.error('❌ Error updating task status:', error)
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    const responseTime = Date.now() - startTime
    console.log(`⚡ Task status updated in ${responseTime}ms:`, task.title)

    // Fire async side effects without blocking response
    if (status) {
      setImmediate(async () => {
        try {
          // Async notification
          const { createTaskUpdateNotification } = await import('@/lib/notification-triggers')
          await createTaskUpdateNotification(taskId, 'pending', status, user.id)
          
          // Async status completion handling
          if (status === 'completed') {
            const { handleTaskStatusChange } = await import('@/lib/status-completion')
            await handleTaskStatusChange(task as any, status)
          }
        } catch (error) {
          console.error('⚠️ Async side effect failed:', error)
        }
      })
    }

    return NextResponse.json({
      id: task.id,
      status: task.status,
      title: task.title,
      updated_at: task.updated_at
    })

  } catch (error) {
    console.error('💥 Fast status update error:', error)
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to update task status' },
      { status: 500 }
    )
  }
}
