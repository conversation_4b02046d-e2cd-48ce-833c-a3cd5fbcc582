'use client'

import { useState, useEffect, useRef } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Calendar } from '@/components/ui/calendar'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { useClients, useProjects, useSchedules } from '@/hooks/useApi'
import { extractLocationFromMapsUrl } from '@/lib/maps-utils'
import { Loader as GoogleMapsLoader } from '@googlemaps/js-api-loader'
import {
  MapPin,
  Users,
  FolderOpen,
  Calendar as CalendarIcon,
  Search,
  Filter,
  Download,
  Layers,
  Maximize,
  Refresh<PERSON><PERSON>,
  AlertCircle,
  CheckCircle,
  Clock,
  XCircle,
  Eye,
  Share,
  ExternalLink
} from 'lucide-react'
import { format } from 'date-fns'
import type { Client, Project, Schedule } from '@/types'

interface MapLocation {
  id: string
  type: 'client' | 'project' | 'schedule'
  name: string
  location: string
  coordinates?: { lat: number; lng: number }
  client?: Client
  project?: Project
  schedule?: Schedule
  value?: number
  status?: string
  date?: string
}

interface MapFilters {
  clientIds: string[]
  projectIds: string[]
  scheduleIds: string[]
  dateRange: {
    from?: Date
    to?: Date
  }
  searchTerm: string
}

interface MapMetrics {
  totalLocations: number
  totalValue: number
  clientsCount: number
  projectsCount: number
  schedulesCount: number
}

export default function MapPage() {
  // Data hooks
  const { data: clients = [], loading: clientsLoading } = useClients()
  const { data: projects = [], loading: projectsLoading } = useProjects()
  const { data: schedules = [], loading: schedulesLoading } = useSchedules()

  // State
  const [filters, setFilters] = useState<MapFilters>({
    clientIds: [],
    projectIds: [],
    scheduleIds: [],
    dateRange: {},
    searchTerm: ''
  })
  const [mapLocations, setMapLocations] = useState<MapLocation[]>([])
  const [selectedLocation, setSelectedLocation] = useState<MapLocation | null>(null)
  const [mapType, setMapType] = useState<'heatmap' | 'markers'>('markers')
  const [isFullscreen, setIsFullscreen] = useState(false)
  const [mapLoading, setMapLoading] = useState(true)
  const [mapError, setMapError] = useState<string | null>(null)

  // Map refs
  const mapRef = useRef<HTMLDivElement>(null)
  const mapInstanceRef = useRef<google.maps.Map | null>(null)
  const markersRef = useRef<google.maps.Marker[]>([])
  const heatmapRef = useRef<google.maps.visualization.HeatmapLayer | null>(null)

  // Calculate metrics based on filtered data
  const metrics: MapMetrics = {
    totalLocations: mapLocations.length,
    totalValue: mapLocations.reduce((sum, loc) => sum + (loc.value || 0), 0),
    clientsCount: new Set(mapLocations.map(loc => loc.client?.id).filter(Boolean)).size,
    projectsCount: new Set(mapLocations.map(loc => loc.project?.id).filter(Boolean)).size,
    schedulesCount: mapLocations.filter(loc => loc.type === 'schedule').length
  }

  // Get filtered projects based on selected clients
  const filteredProjects = projects.filter(project => 
    filters.clientIds.length === 0 || filters.clientIds.includes(project.client_id)
  )

  // Get filtered schedules based on selected projects
  const filteredSchedules = schedules.filter(schedule => 
    filters.projectIds.length === 0 || filters.projectIds.includes(schedule.project_id)
  )

  // Initialize Google Maps
  useEffect(() => {
    const initMap = async () => {
      if (!mapRef.current) return

      try {
        setMapLoading(true)
        setMapError(null)

        const loader = new GoogleMapsLoader({
          apiKey: process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY || '',
          version: 'weekly',
          libraries: ['visualization', 'places']
        })

        const google = await loader.load()
        
        const map = new google.maps.Map(mapRef.current, {
          center: { lat: 20.5937, lng: 78.9629 }, // Center of India
          zoom: 5,
          mapTypeId: google.maps.MapTypeId.ROADMAP,
          styles: [
            {
              featureType: 'poi',
              elementType: 'labels',
              stylers: [{ visibility: 'off' }]
            }
          ]
        })

        mapInstanceRef.current = map
        setMapLoading(false)
      } catch (error) {
        console.error('Error loading Google Maps:', error)
        setMapError('Failed to load Google Maps. Please check your API key.')
        setMapLoading(false)
      }
    }

    initMap()
  }, [])

  // Update map markers and heatmap
  useEffect(() => {
    if (!mapInstanceRef.current || mapLoading) return

    // Clear existing markers
    markersRef.current.forEach(marker => marker.setMap(null))
    markersRef.current = []

    // Clear existing heatmap
    if (heatmapRef.current) {
      heatmapRef.current.setMap(null)
      heatmapRef.current = null
    }

    if (mapType === 'markers') {
      // Add markers for each location
      mapLocations.forEach(location => {
        if (!location.coordinates) return

        const marker = new google.maps.Marker({
          position: location.coordinates,
          map: mapInstanceRef.current,
          title: location.name,
          icon: {
            path: google.maps.SymbolPath.CIRCLE,
            scale: 8,
            fillColor: getMarkerColor(location),
            fillOpacity: 0.8,
            strokeColor: '#ffffff',
            strokeWeight: 2,
          }
        })

        // Add click listener
        marker.addListener('click', () => {
          setSelectedLocation(location)

          // Center map on clicked marker
          mapInstanceRef.current?.panTo(location.coordinates!)
        })

        // Add hover info window
        const infoWindow = new google.maps.InfoWindow({
          content: `
            <div class="p-2">
              <h3 class="font-semibold">${location.name}</h3>
              <p class="text-sm text-gray-600">${location.location}</p>
              ${location.client ? `<p class="text-xs">Client: ${location.client.name}</p>` : ''}
              ${location.value ? `<p class="text-xs">Value: ₹${location.value.toLocaleString()}</p>` : ''}
              ${location.status ? `<p class="text-xs">Status: ${location.status}</p>` : ''}
            </div>
          `
        })

        marker.addListener('mouseover', () => {
          infoWindow.open(mapInstanceRef.current, marker)
        })

        marker.addListener('mouseout', () => {
          infoWindow.close()
        })

        markersRef.current.push(marker)
      })
    } else if (mapType === 'heatmap') {
      // Create heatmap data
      const heatmapData = mapLocations
        .filter(location => location.coordinates)
        .map(location => ({
          location: new google.maps.LatLng(location.coordinates!.lat, location.coordinates!.lng),
          weight: location.value ? Math.log(location.value + 1) : 1
        }))

      if (heatmapData.length > 0) {
        heatmapRef.current = new google.maps.visualization.HeatmapLayer({
          data: heatmapData,
          map: mapInstanceRef.current,
          radius: 50,
          opacity: 0.8
        })
      }
    }

    // Adjust map bounds to fit all markers
    if (mapLocations.length > 0 && mapLocations.some(l => l.coordinates)) {
      const bounds = new google.maps.LatLngBounds()
      mapLocations.forEach(location => {
        if (location.coordinates) {
          bounds.extend(location.coordinates)
        }
      })
      mapInstanceRef.current.fitBounds(bounds)

      // Ensure minimum zoom level
      const listener = google.maps.event.addListener(mapInstanceRef.current, 'bounds_changed', () => {
        if (mapInstanceRef.current!.getZoom()! > 15) {
          mapInstanceRef.current!.setZoom(15)
        }
        google.maps.event.removeListener(listener)
      })
    }
  }, [mapLocations, mapType, mapLoading])

  // Get marker color based on location type and status
  const getMarkerColor = (location: MapLocation): string => {
    if (location.type === 'schedule') {
      switch (location.status) {
        case 'completed': return '#10b981' // green
        case 'scheduled': return '#f59e0b' // amber
        case 'in_progress': return '#3b82f6' // blue
        case 'cancelled': return '#ef4444' // red
        default: return '#6b7280' // gray
      }
    } else if (location.type === 'project') {
      switch (location.status) {
        case 'active': return '#3b82f6' // blue
        case 'completed': return '#10b981' // green
        case 'on_hold': return '#f59e0b' // amber
        case 'cancelled': return '#ef4444' // red
        default: return '#6b7280' // gray
      }
    }
    return '#8b5cf6' // purple for clients
  }

  // Process location data
  useEffect(() => {
    const processLocations = async () => {
      const locations: MapLocation[] = []

      // Add project locations
      for (const project of projects) {
        if (project.location && shouldIncludeProject(project)) {
          const coordinates = await extractCoordinatesFromLocation(project.location, project.google_maps_link)
          
          locations.push({
            id: `project-${project.id}`,
            type: 'project',
            name: project.name,
            location: project.location,
            coordinates,
            client: project.client,
            project,
            value: project.total_amount,
            status: project.status
          })
        }
      }

      // Add schedule locations
      for (const schedule of schedules) {
        if (schedule.location && shouldIncludeSchedule(schedule)) {
          const coordinates = await extractCoordinatesFromLocation(schedule.location, schedule.google_maps_link)
          
          locations.push({
            id: `schedule-${schedule.id}`,
            type: 'schedule',
            name: `${schedule.project?.name} - ${schedule.custom_id}`,
            location: schedule.location,
            coordinates,
            client: schedule.project?.client,
            project: schedule.project,
            schedule,
            value: schedule.amount,
            status: schedule.status,
            date: schedule.scheduled_date
          })
        }
      }

      setMapLocations(locations)
    }

    if (!clientsLoading && !projectsLoading && !schedulesLoading) {
      processLocations()
    }
  }, [clients, projects, schedules, filters, clientsLoading, projectsLoading, schedulesLoading])

  // Helper functions for filtering
  const shouldIncludeProject = (project: Project): boolean => {
    if (filters.clientIds.length > 0 && !filters.clientIds.includes(project.client_id)) return false
    if (filters.projectIds.length > 0 && !filters.projectIds.includes(project.id)) return false
    if (filters.searchTerm) {
      const searchLower = filters.searchTerm.toLowerCase()
      if (!project.name.toLowerCase().includes(searchLower) && 
          !project.location?.toLowerCase().includes(searchLower)) return false
    }
    return true
  }

  const shouldIncludeSchedule = (schedule: Schedule): boolean => {
    if (filters.projectIds.length > 0 && !filters.projectIds.includes(schedule.project_id)) return false
    if (filters.scheduleIds.length > 0 && !filters.scheduleIds.includes(schedule.id)) return false
    if (filters.dateRange.from || filters.dateRange.to) {
      const scheduleDate = new Date(schedule.scheduled_date)
      if (filters.dateRange.from && scheduleDate < filters.dateRange.from) return false
      if (filters.dateRange.to && scheduleDate > filters.dateRange.to) return false
    }
    if (filters.searchTerm) {
      const searchLower = filters.searchTerm.toLowerCase()
      if (!schedule.custom_id.toLowerCase().includes(searchLower) && 
          !schedule.location?.toLowerCase().includes(searchLower) &&
          !schedule.project?.name.toLowerCase().includes(searchLower)) return false
    }
    return true
  }

  // Extract coordinates from location string or Google Maps link
  const extractCoordinatesFromLocation = async (location: string, mapsLink?: string): Promise<{ lat: number; lng: number } | undefined> => {
    if (mapsLink) {
      try {
        const locationInfo = await extractLocationFromMapsUrl(mapsLink)
        if (locationInfo?.coordinates) {
          return locationInfo.coordinates
        }
      } catch (error) {
        console.warn('Failed to extract coordinates from maps link:', error)
      }
    }
    return undefined
  }

  const loading = clientsLoading || projectsLoading || schedulesLoading

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[600px]">
        <div className="text-center">
          <RefreshCw className="w-8 h-8 animate-spin mx-auto mb-4 text-muted-foreground" />
          <p className="text-muted-foreground">Loading map data...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold">Map & Locations</h1>
          <p className="text-muted-foreground">Visualize clients, projects, and schedules geographically</p>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline" size="sm">
            <Download className="w-4 h-4 mr-2" />
            Export
          </Button>
          <Button variant="outline" size="sm">
            <Layers className="w-4 h-4 mr-2" />
            Layers
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setIsFullscreen(!isFullscreen)}
          >
            <Maximize className="w-4 h-4 mr-2" />
            {isFullscreen ? 'Exit' : 'Fullscreen'}
          </Button>
        </div>
      </div>

      {/* Top Metrics Section */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <MapPin className="w-5 h-5 text-blue-600" />
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Locations</p>
                <p className="text-2xl font-bold">{metrics.totalLocations}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <FolderOpen className="w-5 h-5 text-green-600" />
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Value</p>
                <p className="text-2xl font-bold">₹{(metrics.totalValue / 100000).toFixed(1)}L</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Users className="w-5 h-5 text-purple-600" />
              <div>
                <p className="text-sm font-medium text-muted-foreground">Clients</p>
                <p className="text-2xl font-bold">{metrics.clientsCount}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <FolderOpen className="w-5 h-5 text-orange-600" />
              <div>
                <p className="text-sm font-medium text-muted-foreground">Projects</p>
                <p className="text-2xl font-bold">{metrics.projectsCount}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <CalendarIcon className="w-5 h-5 text-red-600" />
              <div>
                <p className="text-sm font-medium text-muted-foreground">Schedules</p>
                <p className="text-2xl font-bold">{metrics.schedulesCount}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Filter className="w-5 h-5" />
            <span>Filters</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {/* Client Filter */}
            <div className="space-y-2">
              <label className="text-sm font-medium">Client</label>
              <Select
                value={filters.clientIds.length === 1 ? filters.clientIds[0] : ''}
                onValueChange={(value) => {
                  if (value === 'all') {
                    setFilters(prev => ({ ...prev, clientIds: [], projectIds: [], scheduleIds: [] }))
                  } else {
                    setFilters(prev => ({ ...prev, clientIds: [value], projectIds: [], scheduleIds: [] }))
                  }
                }}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select client" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Clients</SelectItem>
                  {clients.map(client => (
                    <SelectItem key={client.id} value={client.id}>
                      {client.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Project Filter */}
            <div className="space-y-2">
              <label className="text-sm font-medium">Project</label>
              <Select
                value={filters.projectIds.length === 1 ? filters.projectIds[0] : ''}
                onValueChange={(value) => {
                  if (value === 'all') {
                    setFilters(prev => ({ ...prev, projectIds: [], scheduleIds: [] }))
                  } else {
                    setFilters(prev => ({ ...prev, projectIds: [value], scheduleIds: [] }))
                  }
                }}
                disabled={filters.clientIds.length === 0}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select project" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Projects</SelectItem>
                  {filteredProjects.map(project => (
                    <SelectItem key={project.id} value={project.id}>
                      {project.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Schedule Filter */}
            <div className="space-y-2">
              <label className="text-sm font-medium">Schedule</label>
              <Select
                value={filters.scheduleIds.length === 1 ? filters.scheduleIds[0] : ''}
                onValueChange={(value) => {
                  if (value === 'all') {
                    setFilters(prev => ({ ...prev, scheduleIds: [] }))
                  } else {
                    setFilters(prev => ({ ...prev, scheduleIds: [value] }))
                  }
                }}
                disabled={filters.projectIds.length === 0}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select schedule" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Schedules</SelectItem>
                  {filteredSchedules.map(schedule => (
                    <SelectItem key={schedule.id} value={schedule.id}>
                      {schedule.custom_id} - {format(new Date(schedule.scheduled_date), 'MMM dd')}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Search Filter */}
            <div className="space-y-2">
              <label className="text-sm font-medium">Search</label>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                <Input
                  placeholder="Search locations..."
                  value={filters.searchTerm}
                  onChange={(e) => setFilters(prev => ({ ...prev, searchTerm: e.target.value }))}
                  className="pl-10"
                />
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Map Visualization Section */}
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        <div className="lg:col-span-3">
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <CardTitle className="flex items-center space-x-2">
                  <MapPin className="w-5 h-5" />
                  <span>Map Visualization</span>
                </CardTitle>
                <div className="flex items-center space-x-2">
                  <Button
                    variant={mapType === 'markers' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setMapType('markers')}
                  >
                    Markers
                  </Button>
                  <Button
                    variant={mapType === 'heatmap' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setMapType('heatmap')}
                  >
                    Heatmap
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="relative">
                {mapLoading && (
                  <div className="absolute inset-0 bg-background/80 backdrop-blur-sm z-10 flex items-center justify-center">
                    <div className="text-center">
                      <RefreshCw className="w-8 h-8 animate-spin mx-auto mb-2 text-muted-foreground" />
                      <p className="text-sm text-muted-foreground">Loading Google Maps...</p>
                    </div>
                  </div>
                )}

                {mapError && (
                  <div className="absolute inset-0 bg-background/80 backdrop-blur-sm z-10 flex items-center justify-center">
                    <div className="text-center">
                      <AlertCircle className="w-8 h-8 mx-auto mb-2 text-destructive" />
                      <p className="text-sm text-destructive mb-2">{mapError}</p>
                      <Button size="sm" onClick={() => window.location.reload()}>
                        Retry
                      </Button>
                    </div>
                  </div>
                )}

                <div
                  ref={mapRef}
                  className="w-full h-[600px] rounded-lg border bg-muted"
                  style={{ minHeight: '600px' }}
                />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Location Details Sidebar */}
        <div className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Location Details</CardTitle>
            </CardHeader>
            <CardContent>
              {selectedLocation ? (
                <div className="space-y-4">
                  <div>
                    <h3 className="font-semibold text-lg">{selectedLocation.name}</h3>
                    <p className="text-sm text-muted-foreground flex items-center mt-1">
                      <MapPin className="w-4 h-4 mr-1" />
                      {selectedLocation.location}
                    </p>
                  </div>

                  <Separator />

                  <div className="space-y-3">
                    {selectedLocation.client && (
                      <div>
                        <label className="text-sm font-medium text-muted-foreground">Client</label>
                        <p className="text-sm">{selectedLocation.client.name}</p>
                      </div>
                    )}

                    {selectedLocation.project && (
                      <div>
                        <label className="text-sm font-medium text-muted-foreground">Project</label>
                        <p className="text-sm">{selectedLocation.project.name}</p>
                      </div>
                    )}

                    {selectedLocation.schedule && (
                      <div>
                        <label className="text-sm font-medium text-muted-foreground">Schedule</label>
                        <p className="text-sm">{selectedLocation.schedule.custom_id}</p>
                        {selectedLocation.date && (
                          <p className="text-xs text-muted-foreground">
                            {format(new Date(selectedLocation.date), 'PPP')}
                          </p>
                        )}
                      </div>
                    )}

                    {selectedLocation.status && (
                      <div>
                        <label className="text-sm font-medium text-muted-foreground">Status</label>
                        <div className="flex items-center space-x-2 mt-1">
                          {selectedLocation.status === 'completed' && <CheckCircle className="w-4 h-4 text-green-600" />}
                          {selectedLocation.status === 'active' && <Clock className="w-4 h-4 text-blue-600" />}
                          {selectedLocation.status === 'scheduled' && <Clock className="w-4 h-4 text-orange-600" />}
                          {selectedLocation.status === 'cancelled' && <XCircle className="w-4 h-4 text-red-600" />}
                          <Badge variant={
                            selectedLocation.status === 'completed' ? 'default' :
                            selectedLocation.status === 'active' ? 'secondary' :
                            selectedLocation.status === 'scheduled' ? 'outline' : 'destructive'
                          }>
                            {selectedLocation.status}
                          </Badge>
                        </div>
                      </div>
                    )}

                    {selectedLocation.value && selectedLocation.value > 0 && (
                      <div>
                        <label className="text-sm font-medium text-muted-foreground">Value</label>
                        <p className="text-sm font-semibold">₹{selectedLocation.value.toLocaleString()}</p>
                      </div>
                    )}
                  </div>

                  <Separator />

                  {/* Quick Actions */}
                  <div className="space-y-2">
                    <h4 className="text-sm font-medium">Quick Actions</h4>
                    <div className="grid grid-cols-2 gap-2">
                      <Button size="sm" variant="outline" className="text-xs">
                        <Eye className="w-3 h-3 mr-1" />
                        View Details
                      </Button>
                      <Button size="sm" variant="outline" className="text-xs">
                        <ExternalLink className="w-3 h-3 mr-1" />
                        Open Maps
                      </Button>
                      <Button size="sm" variant="outline" className="text-xs">
                        <Share className="w-3 h-3 mr-1" />
                        Share
                      </Button>
                      <Button size="sm" variant="outline" className="text-xs">
                        <CalendarIcon className="w-3 h-3 mr-1" />
                        Calendar
                      </Button>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="text-center py-8">
                  <MapPin className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                  <p className="text-sm text-muted-foreground">
                    Click on a location marker to view details
                  </p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Location Summary */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Summary</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-muted-foreground">Visible Locations</span>
                  <span className="text-sm font-medium">{mapLocations.length}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-muted-foreground">Projects</span>
                  <span className="text-sm font-medium">
                    {mapLocations.filter(l => l.type === 'project').length}
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-muted-foreground">Schedules</span>
                  <span className="text-sm font-medium">
                    {mapLocations.filter(l => l.type === 'schedule').length}
                  </span>
                </div>
                <Separator />
                <div className="flex justify-between items-center">
                  <span className="text-sm text-muted-foreground">Total Value</span>
                  <span className="text-sm font-semibold">
                    ₹{metrics.totalValue.toLocaleString()}
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
