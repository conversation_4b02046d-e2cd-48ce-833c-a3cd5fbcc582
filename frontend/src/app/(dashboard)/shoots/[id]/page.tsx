'use client'

import { useState, useEffect } from 'react'
import { usePara<PERSON>, useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Modal } from '@/components/ui/modal'
import { ScheduleForm } from '@/components/forms/ScheduleForm'
import { TaskForm } from '@/components/forms/TaskForm'
import { EnhancedTaskCard } from '@/components/ui/enhanced-task-card'
import { ShootCompletionForm, type ShootCompletionData } from '@/components/ui/schedule-completion-form'

import { schedulesApi, tasksApi, projectsApi } from '@/lib/api'
import { useAuth } from '@/contexts/AuthContext'
import {
  ArrowLeft,
  Calendar,
  Clock,
  User,
  MapPin,
  Building,
  Edit,
  CheckCircle,
  AlertCircle,
  XCircle,
  Plus,
  ExternalLink,
  FileText,
  Battery,
  Zap
} from 'lucide-react'
import toast from 'react-hot-toast'
import type { Shoot, Task } from '@/types'
import Link from 'next/link'

export default function ShootDetailsPage() {
  const params = useParams()
  const router = useRouter()
  const { user } = useAuth()
  const shootId = params.id as string

  const [shoot, setShoot] = useState<Shoot | null>(null)
  const [tasks, setTasks] = useState<Task[]>([])
  const [loading, setLoading] = useState(true)
  const [isEditModalOpen, setIsEditModalOpen] = useState(false)
  const [isAddTaskModalOpen, setIsAddTaskModalOpen] = useState(false)
  const [completingShoot, setCompletingShoot] = useState<Shoot | null>(null)


  const fetchShootData = async () => {
    try {
      setLoading(true)
      const [scheduleData, tasksData] = await Promise.all([
        schedulesApi.getById(shootId),
        tasksApi.getAll()
      ])

      setShoot(scheduleData)

      if (!scheduleData) {
        toast.error('Schedule not found')
        return
      }

      // Filter tasks related to this shoot:
      // 1. Tasks specifically assigned to this shoot (shoot_id matches)
      // 2. Project-level tasks that don't have a shoot_id (default tasks)
      const scheduleTasks = tasksData
        .filter(task =>
          task.shoot_id === shootId ||
          (task.project_id === scheduleData.project_id && !task.shoot_id)
        )
        .sort((a, b) => {
          // Enhanced sorting: shoot tasks first, then project tasks
          const aIsProjectTask = !a.shoot_id
          const bIsProjectTask = !b.shoot_id

          // If one is project task and other is shoot task, shoot task comes first
          if (aIsProjectTask && !bIsProjectTask) return 1
          if (!aIsProjectTask && bIsProjectTask) return -1

          // Both are same type, sort by order field
          const aOrder = a.order ?? (aIsProjectTask ? 1000 : 0)
          const bOrder = b.order ?? (bIsProjectTask ? 1000 : 0)

          if (aOrder !== bOrder) {
            return aOrder - bOrder
          }

          // Fallback to creation time
          return new Date(a.created_at).getTime() - new Date(b.created_at).getTime()
        })

      // If no tasks exist for this project and we have client type, create default tasks
      if (scheduleTasks.length === 0 && scheduleData.project?.client?.client_type) {
        console.log('Creating default tasks for client type:', scheduleData.project.client.client_type)
        try {
          await projectsApi.createDefaultTasks(
            scheduleData.project_id,
            scheduleData.project.client.client_type,
            scheduleData.scheduled_date,
            shootId // Pass the schedule ID for schedule-based tasks
          )

          console.log('Default tasks created, refetching...')
          // Refetch tasks after creating default ones
          const updatedTasksData = await tasksApi.getAll()
          const updatedScheduleTasks = updatedTasksData.filter(task =>
            task.shoot_id === shootId ||
            (task.project_id === scheduleData.project_id && !task.shoot_id)
          )
          console.log('Updated tasks:', updatedScheduleTasks)
          setTasks(updatedScheduleTasks)
          toast.success('Default tasks created for this project')
        } catch (taskError: any) {
          console.error('Failed to create default tasks:', taskError)
          toast.error('Failed to create default tasks: ' + (taskError?.message || 'Unknown error'))
          setTasks(scheduleTasks) // Set empty tasks if creation fails
        }
      } else {
        console.log('Not creating tasks. scheduleTasks.length:', scheduleTasks.length, 'client_type:', scheduleData.project?.client?.client_type)
        console.log('Full schedule data:', JSON.stringify(scheduleData, null, 2))
        setTasks(scheduleTasks)
      }
    } catch (error: any) {
      toast.error('Failed to load schedule details')
      console.error('Error fetching schedule data:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    if (shootId) {
      fetchShootData()
    }
  }, [shootId])

  const handleEditSuccess = () => {
    setIsEditModalOpen(false)
    fetchShootData()
  }

  const handleAddTaskSuccess = () => {
    setIsAddTaskModalOpen(false)
    fetchShootData()
  }

  const handleTaskStatusChange = async (task: Task, newStatus: Task['status']) => {
    try {
      await tasksApi.update(task.id, { status: newStatus })
      fetchShootData()
      toast.success('Task status updated successfully')
    } catch (error: any) {
      toast.error(error.message || 'Failed to update task status')
    }
  }

  const handleTaskInlineUpdate = async (taskId: string, updates: Partial<Task>) => {
    try {
      await tasksApi.update(taskId, updates)
      fetchShootData()
      toast.success('Task updated successfully')
    } catch (error: any) {
      toast.error(error.message || 'Failed to update task')
    }
  }

  const handleDeleteTask = async (task: Task) => {
    try {
      await tasksApi.delete(task.id)
      fetchShootData()
      toast.success('Task deleted successfully')
    } catch (error: any) {
      toast.error(error.message || 'Failed to delete task')
    }
  }

  const handleCompleteShoot = async (completionData: ShootCompletionData) => {
    if (!completingShoot) return

    try {
      const updates = {
        status: 'completed',
        actual_date: new Date().toISOString(),
        device_used: completionData.device_used,
        battery_percentage: completionData.battery_percentage,
        photos_taken: completionData.photos_taken,
        completion_notes: completionData.completion_notes
      }

      // Complete the schedule first
      await schedulesApi.update(completingShoot.id, updates as any)

      // Then complete all schedule-related tasks
      await projectsApi.completeShootTasks(completingShoot.id)

      toast.success('Schedule and all related tasks completed successfully')
      setCompletingShoot(null)
      fetchShootData()
    } catch (error: any) {
      toast.error(error.message || 'Failed to complete schedule')
    }
  }



  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'scheduled':
        return <Clock className="w-5 h-5 text-blue-500" />
      case 'completed':
        return <CheckCircle className="w-5 h-5 text-green-500" />
      case 'cancelled':
        return <XCircle className="w-5 h-5 text-red-500" />
      case 'rescheduled':
        return <AlertCircle className="w-5 h-5 text-yellow-500" />
      default:
        return <Clock className="w-5 h-5 text-gray-500" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'scheduled':
        return 'text-blue-600 bg-blue-100 border-blue-200'
      case 'completed':
        return 'text-green-600 bg-green-100 border-green-200'
      case 'cancelled':
        return 'text-red-600 bg-red-100 border-red-200'
      case 'rescheduled':
        return 'text-yellow-600 bg-yellow-100 border-yellow-200'
      default:
        return 'text-gray-600 bg-gray-100 border-gray-200'
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading shoot details...</p>
        </div>
      </div>
    )
  }

  if (!shoot) {
    return (
      <div className="text-center py-12">
        <AlertCircle className="w-12 h-12 text-red-500 mx-auto mb-4" />
        <h2 className="text-xl font-semibold text-gray-900 mb-2">Shoot Not Found</h2>
        <p className="text-gray-600 mb-4">The shoot you're looking for doesn't exist or has been deleted.</p>
        <Button onClick={() => router.push('/shoots')}>
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back to Shoots
        </Button>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant="outline"
            size="sm"
            onClick={() => router.push('/shoots')}
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Shoots
          </Button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">{shoot.project?.name}</h1>
            <p className="text-gray-600">{shoot.project?.client?.name}</p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <div className={`flex items-center px-3 py-1 rounded-full text-sm font-medium border ${getStatusColor(shoot.status)}`}>
            {getStatusIcon(shoot.status)}
            <span className="ml-2 capitalize">{shoot.status}</span>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setIsEditModalOpen(true)}
          >
            <Edit className="w-4 h-4 mr-2" />
            Edit Shoot
          </Button>
          {shoot.status === 'scheduled' && (
            <div className="text-sm text-muted-foreground">
              Complete this shoot through the Tasks page
            </div>
          )}
        </div>
      </div>

      {/* Shoot Details Card */}
      <div className="bg-card border border-border rounded-lg p-6">
        <h2 className="text-lg font-semibold text-foreground mb-4">Shoot Information</h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {/* Date & Time */}
          <div className="space-y-3">
            <div className="flex items-center text-sm text-muted-foreground">
              <Calendar className="w-4 h-4 mr-2" />
              <span>Scheduled Date</span>
            </div>
            <p className="font-medium">
              {new Date(shoot.scheduled_date).toLocaleDateString('en-US', {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric'
              })}
            </p>
            <p className="text-sm text-muted-foreground">
              {new Date(shoot.scheduled_date).toLocaleTimeString('en-US', {
                hour: '2-digit',
                minute: '2-digit'
              })}
            </p>
          </div>

          {/* Pilot */}
          {shoot.pilot && (
            <div className="space-y-3">
              <div className="flex items-center text-sm text-muted-foreground">
                <User className="w-4 h-4 mr-2" />
                <span>Assigned Pilot</span>
              </div>
              <p className="font-medium">{shoot.pilot.name}</p>
              <p className="text-sm text-muted-foreground">{shoot.pilot.email}</p>
            </div>
          )}

          {/* Location */}
          {shoot.location && (
            <div className="space-y-3">
              <div className="flex items-center text-sm text-muted-foreground">
                <MapPin className="w-4 h-4 mr-2" />
                <span>Location</span>
              </div>
              <p className="font-medium">{shoot.location}</p>
              {shoot.google_maps_link && (
                <a
                  href={shoot.google_maps_link}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-flex items-center text-sm text-blue-600 hover:text-blue-700"
                >
                  <ExternalLink className="w-3 h-3 mr-1" />
                  View on Maps
                </a>
              )}
            </div>
          )}

          {/* Amount */}
          <div className="space-y-3">
            <div className="flex items-center text-sm text-muted-foreground">
              <span>Amount</span>
            </div>
            <p className="font-medium text-lg">₹{shoot.amount.toLocaleString()}</p>
          </div>

          {/* Completion Details */}
          {shoot.status === 'completed' && (
            <>
              {shoot.device_used && (
                <div className="space-y-3">
                  <div className="flex items-center text-sm text-muted-foreground">
                    <Zap className="w-4 h-4 mr-2" />
                    <span>Device Used</span>
                  </div>
                  <p className="font-medium">{shoot.device_used}</p>
                </div>
              )}

              {shoot.battery_count && (
                <div className="space-y-3">
                  <div className="flex items-center text-sm text-muted-foreground">
                    <Battery className="w-4 h-4 mr-2" />
                    <span>Batteries Used</span>
                  </div>
                  <p className="font-medium">{shoot.battery_count}</p>
                </div>
              )}
            </>
          )}
        </div>

        {/* Notes */}
        {shoot.notes && (
          <div className="mt-6 pt-6 border-t border-border">
            <div className="flex items-center text-sm text-muted-foreground mb-2">
              <FileText className="w-4 h-4 mr-2" />
              <span>Notes</span>
            </div>
            <p className="text-foreground">{shoot.notes}</p>
          </div>
        )}

        {/* Completion Notes */}
        {shoot.completion_notes && (
          <div className="mt-4">
            <div className="flex items-center text-sm text-muted-foreground mb-2">
              <CheckCircle className="w-4 h-4 mr-2" />
              <span>Completion Notes</span>
            </div>
            <p className="text-foreground">{shoot.completion_notes}</p>
          </div>
        )}
      </div>

      {/* Tasks Section */}
      <div className="bg-card border border-border rounded-lg p-6">
        <div className="flex justify-between items-center mb-4">
          <div>
            <h2 className="text-lg font-semibold text-foreground">Related Tasks</h2>
            <p className="text-sm text-muted-foreground">
              Tasks for this shoot and project-level tasks
            </p>
          </div>
          {(user?.role === 'admin' || user?.role === 'manager') && (
            <Button
              onClick={() => setIsAddTaskModalOpen(true)}
              size="sm"
            >
              <Plus className="w-4 h-4 mr-2" />
              Add Task
            </Button>
          )}
        </div>

        {/* Only show tasks section for admin and accounts roles */}
        {(user?.role === 'admin' || user?.role === 'manager') ? (
          tasks.length === 0 ? (
            <div className="text-center py-8">
              <CheckCircle className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
              <p className="text-muted-foreground">No tasks found for this shoot or project.</p>
              <p className="text-sm text-muted-foreground mt-2">
                Add tasks to track specific activities for this shoot.
              </p>
            </div>
          ) : (
            <div className="space-y-3">
              {tasks.map((task) => (
                <EnhancedTaskCard
                  key={task.id}
                  task={task}
                  onDelete={handleDeleteTask}
                  onStatusChange={handleTaskStatusChange}
                  onInlineUpdate={handleTaskInlineUpdate}
                  onCompleteSchedule={setCompletingShoot}
                  schedule={shoot}
                  compact={true}
                  showRoleFilter={false} // No role filtering needed since only admin/accounts can see this
                  currentUserRole={user?.role}
                />
              ))}
            </div>
          )
        ) : (
          <div className="text-center py-8">
            <CheckCircle className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
            <p className="text-muted-foreground">Task management is available for admin and accounts roles only.</p>
            <p className="text-sm text-muted-foreground mt-2">
              Contact your administrator for task-related queries.
            </p>
          </div>
        )}
      </div>

      {/* Edit Shoot Modal */}
      <Modal
        isOpen={isEditModalOpen}
        onClose={() => setIsEditModalOpen(false)}
        title="Edit Shoot"
        size="xl"
      >
        <ScheduleForm
          schedule={shoot}
          onSuccess={handleEditSuccess}
          onCancel={() => setIsEditModalOpen(false)}
        />
      </Modal>

      {/* Add Task Modal */}
      <Modal
        isOpen={isAddTaskModalOpen}
        onClose={() => setIsAddTaskModalOpen(false)}
        title="Add Shoot Task"
        size="lg"
      >
        <TaskForm
          onSuccess={handleAddTaskSuccess}
          onCancel={() => setIsAddTaskModalOpen(false)}
          preselectedProjectId={shoot.project_id}
          preselectedShootId={shoot.id}
        />
      </Modal>

      {/* Shoot Completion Form */}
      <ShootCompletionForm
        schedule={completingShoot}
        shootTask={tasks.find(task => task.title.toLowerCase() === 'shoot')}
        isOpen={!!completingShoot}
        onClose={() => setCompletingShoot(null)}
        onComplete={handleCompleteShoot}
      />

    </div>
  )
}
