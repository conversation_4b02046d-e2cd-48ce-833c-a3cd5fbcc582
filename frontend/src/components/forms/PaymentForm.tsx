'use client'

import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { useCreatePayment, useUpdatePayment, useProjects } from '@/hooks/useApi'
import toast from 'react-hot-toast'
import type { Payment } from '@/types'

const paymentSchema = z.object({
  project_id: z.string().optional(),
  amount: z.string().min(1, 'Amount is required').refine(
    (val) => !isNaN(Number(val)) && Number(val) > 0,
    'Amount must be a positive number'
  ),
  payment_date: z.string().min(1, 'Payment date is required'),
  payment_method: z.enum(['cash', 'bank_transfer', 'credit_card', 'cheque', 'other']),
  reference_number: z.string().optional(),
  notes: z.string().optional(),
  auto_distribute: z.boolean().optional(),
})

type PaymentFormData = z.infer<typeof paymentSchema>

interface PaymentFormProps {
  payment?: Payment
  onSuccess?: (payment: Payment) => void
  onCancel?: () => void
  preselectedProjectId?: string
  clientId?: string
}

export function PaymentForm({ payment, onSuccess, onCancel, preselectedProjectId, clientId }: PaymentFormProps) {
  const isEditing = !!payment
  const { createPayment, loading: createLoading } = useCreatePayment()
  const { updatePayment, loading: updateLoading } = useUpdatePayment()
  const { data: projects, loading: projectsLoading } = useProjects()
  const loading = createLoading || updateLoading

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    watch,
  } = useForm<PaymentFormData>({
    resolver: zodResolver(paymentSchema),
    defaultValues: payment ? {
      project_id: payment.project_id,
      amount: payment.amount.toString(),
      payment_date: new Date(payment.payment_date).toISOString().slice(0, 10),
      payment_method: payment.payment_method,
      reference_number: payment.reference_number || '',
      notes: payment.notes || '',
    } : {
      project_id: preselectedProjectId || '',
      amount: '',
      payment_date: new Date().toISOString().slice(0, 10),
      payment_method: 'bank_transfer',
      reference_number: '',
      notes: '',
    },
  })

  const selectedProjectId = watch('project_id')
  const autoDistribute = watch('auto_distribute')
  const amount = watch('amount')

  // Filter projects by client if clientId is provided
  const filteredProjects = projects?.filter(project =>
    clientId ? project.client_id === clientId : true
  ) || []

  const selectedProject = filteredProjects.find(p => p.id === selectedProjectId)

  // Get completed projects with pending amounts for auto-distribution
  const completedProjectsWithPending = filteredProjects
    .filter(p => p.status === 'completed' && p.amount_pending > 0)
    .sort((a, b) => new Date(a.end_date || a.created_at).getTime() - new Date(b.end_date || b.created_at).getTime())

  const onSubmit = async (data: PaymentFormData) => {
    try {
      const paymentAmount = parseFloat(data.amount)

      // If auto-distribute is enabled or no project is selected, distribute to completed projects
      if (data.auto_distribute || !data.project_id) {
        if (completedProjectsWithPending.length === 0) {
          toast.error('No completed projects with pending payments found')
          return
        }

        let remainingAmount = paymentAmount
        const paymentsToCreate = []

        // Distribute payment across completed projects with pending amounts
        for (const project of completedProjectsWithPending) {
          if (remainingAmount <= 0) break

          const amountForThisProject = Math.min(remainingAmount, project.amount_pending)

          paymentsToCreate.push({
            project_id: project.id,
            amount: amountForThisProject,
            payment_date: data.payment_date,
            payment_method: data.payment_method,
            reference_number: data.reference_number || null,
            notes: data.notes ? `${data.notes} (Auto-distributed)` : 'Auto-distributed payment',
          })

          remainingAmount -= amountForThisProject
        }

        // Create all payments
        const results = []
        for (const paymentData of paymentsToCreate) {
          const result = await createPayment(paymentData)
          results.push(result)
        }

        if (remainingAmount > 0) {
          toast.success(`Payment distributed successfully. ₹${remainingAmount.toLocaleString()} excess amount could not be allocated.`)
        } else {
          toast.success(`Payment of ₹${paymentAmount.toLocaleString()} distributed across ${paymentsToCreate.length} project(s)`)
        }

        reset()
        onSuccess?.(results[0]) // Return first payment for callback
      } else {
        // Regular single project payment
        const paymentData = {
          ...data,
          amount: paymentAmount,
          reference_number: data.reference_number || null,
          notes: data.notes || null,
        }

        let result: Payment
        if (isEditing) {
          result = await updatePayment(payment.id, paymentData)
          toast.success('Payment updated successfully')
        } else {
          result = await createPayment(paymentData)
          toast.success('Payment recorded successfully')
          reset()
        }

        onSuccess?.(result)
      }
    } catch (error: any) {
      toast.error(error.message || 'Failed to save payment')
    }
  }

  if (projectsLoading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
      {/* Auto-distribute option */}
      {!isEditing && completedProjectsWithPending.length > 0 && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              id="auto_distribute"
              {...register('auto_distribute')}
              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <Label htmlFor="auto_distribute" className="text-sm font-medium text-blue-900">
              Auto-distribute to completed projects with pending payments
            </Label>
          </div>
          <p className="text-xs text-blue-700 mt-1">
            {completedProjectsWithPending.length} completed project(s) with pending payments found
          </p>
        </div>
      )}

      <div>
        <Label htmlFor="project_id">
          Project {!autoDistribute && !isEditing ? '*' : '(Optional when auto-distributing)'}
        </Label>
        <select
          id="project_id"
          {...register('project_id')}
          disabled={autoDistribute}
          className="mt-1 flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <option value="">
            {autoDistribute ? 'Auto-distribute across completed projects' : 'Select a project'}
          </option>
          {filteredProjects.map((project) => (
            <option key={project.id} value={project.id}>
              {project.name} - {clientId ? '' : `${project.client?.name} - `}
              Status: {project.status} -
              Pending: ₹{project.amount_pending.toLocaleString()}
            </option>
          ))}
        </select>
        {errors.project_id && !autoDistribute && (
          <p className="text-sm text-red-600 dark:text-red-400 mt-1">{errors.project_id.message}</p>
        )}
      </div>

      {/* Auto-distribution preview */}
      {autoDistribute && amount && completedProjectsWithPending.length > 0 && (
        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
          <h4 className="font-medium text-green-900 mb-2">Payment Distribution Preview</h4>
          <div className="space-y-2 text-sm">
            {(() => {
              let remainingAmount = parseFloat(amount) || 0
              const distributions = []

              for (const project of completedProjectsWithPending) {
                if (remainingAmount <= 0) break
                const amountForProject = Math.min(remainingAmount, project.amount_pending)
                distributions.push({ project, amount: amountForProject })
                remainingAmount -= amountForProject
              }

              return (
                <>
                  {distributions.map(({ project, amount: distAmount }) => (
                    <div key={project.id} className="flex justify-between items-center py-1 border-b border-green-200 last:border-b-0">
                      <span className="text-green-700">{project.name}</span>
                      <span className="font-medium text-green-900">₹{distAmount.toLocaleString()}</span>
                    </div>
                  ))}
                  {remainingAmount > 0 && (
                    <div className="flex justify-between items-center py-1 text-orange-600">
                      <span>Excess amount (cannot allocate)</span>
                      <span className="font-medium">₹{remainingAmount.toLocaleString()}</span>
                    </div>
                  )}
                </>
              )
            })()}
          </div>
        </div>
      )}

      {selectedProject && !autoDistribute && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h4 className="font-medium text-blue-900 mb-2">Project Financial Summary</h4>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-blue-700">Total Amount:</span>
              <span className="font-medium ml-2">₹{selectedProject.total_amount.toLocaleString()}</span>
            </div>
            <div>
              <span className="text-blue-700">Received:</span>
              <span className="font-medium ml-2 text-green-600">₹{selectedProject.amount_received.toLocaleString()}</span>
            </div>
            <div>
              <span className="text-blue-700">Pending:</span>
              <span className="font-medium ml-2 text-orange-600">₹{selectedProject.amount_pending.toLocaleString()}</span>
            </div>
            <div>
              <span className="text-blue-700">GST:</span>
              <span className="font-medium ml-2">{selectedProject.gst_inclusive ? 'Inclusive' : 'Exclusive'}</span>
            </div>
          </div>
        </div>
      )}

      <div>
        <Label htmlFor="amount">Amount (₹) *</Label>
        <Input
          id="amount"
          type="number"
          step="0.01"
          min="0"
          {...register('amount')}
          placeholder="Enter payment amount"
          className="mt-1"
        />
        {errors.amount && (
          <p className="text-sm text-red-600 dark:text-red-400 mt-1">{errors.amount.message}</p>
        )}
      </div>

      <div>
        <Label htmlFor="payment_date">Payment Date *</Label>
        <Input
          id="payment_date"
          type="date"
          {...register('payment_date')}
          className="mt-1"
        />
        {errors.payment_date && (
          <p className="text-sm text-red-600 dark:text-red-400 mt-1">{errors.payment_date.message}</p>
        )}
      </div>

      <div>
        <Label htmlFor="payment_method">Payment Method *</Label>
        <select
          id="payment_method"
          {...register('payment_method')}
          className="mt-1 flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
        >
          <option value="bank_transfer">Bank Transfer</option>
          <option value="cash">Cash</option>
          <option value="credit_card">Credit Card</option>
          <option value="cheque">Cheque</option>
          <option value="other">Other</option>
        </select>
        {errors.payment_method && (
          <p className="text-sm text-red-600 dark:text-red-400 mt-1">{errors.payment_method.message}</p>
        )}
      </div>

      <div>
        <Label htmlFor="reference_number">Reference Number</Label>
        <Input
          id="reference_number"
          {...register('reference_number')}
          placeholder="Transaction ID, Cheque number, etc."
          className="mt-1"
        />
        {errors.reference_number && (
          <p className="text-sm text-red-600 dark:text-red-400 mt-1">{errors.reference_number.message}</p>
        )}
      </div>

      <div>
        <Label htmlFor="notes">Notes</Label>
        <textarea
          id="notes"
          {...register('notes')}
          placeholder="Additional notes about this payment"
          className="mt-1 flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
          rows={3}
        />
        {errors.notes && (
          <p className="text-sm text-red-600 dark:text-red-400 mt-1">{errors.notes.message}</p>
        )}
      </div>

      <div className="flex gap-3 pt-4">
        <Button
          type="submit"
          disabled={loading}
          className="flex-1"
        >
          {loading ? 'Saving...' : isEditing ? 'Update Payment' : 'Record Payment'}
        </Button>
        {onCancel && (
          <Button
            type="button"
            variant="outline"
            onClick={onCancel}
            disabled={loading}
          >
            Cancel
          </Button>
        )}
      </div>
    </form>
  )
}
