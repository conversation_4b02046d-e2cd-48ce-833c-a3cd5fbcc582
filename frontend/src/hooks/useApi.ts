import { useState, useEffect } from 'react'
import {
  users<PERSON><PERSON>,
  clientsApi,
  contactPersonsApi,
  projectsApi,
  schedulesApi,
  shootsApi,
  expensesApi,
  tasksApi,
  paymentsApi,
  dashboardApi
} from '@/lib/api'
import type {
  Client,
  ContactPerson,
  Project,
  Schedule,
  Shoot,
  Expense,
  Task,
  Payment,
  DashboardStats
} from '@/types'

// Common error message extractor
function getErrorMessage(error: unknown, fallback: string) {
  if (error instanceof Error) return error.message
  if (typeof error === 'string') return error
  try {
    return JSON.stringify(error)
  } catch {
    return fallback
  }
}

// Generic hook for API calls with fallback data
function useApiCall<T>(apiCall: () => Promise<T>, dependencies: any[] = [], fallbackData: T | null = null) {
  const [data, setData] = useState<T | null>(fallbackData)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const refetch = async () => {
    try {
      setLoading(true)
      setError(null)
      const result = await apiCall()
      setData(result)
    } catch (error: unknown) {
      const message = getErrorMessage(error, 'API call failed, using fallback data')
      console.warn('API call failed, using fallback data:', message)
      setData(fallbackData)
      setError(null) // Don't show error if we have fallback data
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    refetch()
  }, dependencies)

  return { data, loading, error, refetch }
}

// Generic hook for API calls with URL
export function useApi<T>(url: string) {
  const [data, setData] = useState<T | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const refetch = async () => {
    try {
      setLoading(true)
      setError(null)
      const response = await fetch(url)
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }
      const result = await response.json()
      setData(result)
    } catch (error: unknown) {
      setError(getErrorMessage(error, 'An error occurred'))
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    refetch()
  }, [url])

  return { data, loading, error, refetch }
}

// Users hooks
export function useUsers() {
  return useApiCall(() => usersApi.getAll())
}

export function useUsersByRole(role: string) {
  return useApiCall(() => usersApi.getByRole(role as any), [role])
}

// Clients hooks
export function useClients() {
  return useApiCall(() => clientsApi.getAll(), [], [])
}

export function useClient(id: string) {
  return useApiCall(() => clientsApi.getById(id), [id])
}

// Contact Persons hooks
export function useContactPersons(clientId: string) {
  return useApiCall(() => contactPersonsApi.getByClientId(clientId), [clientId])
}

// Projects hooks
export function useProjects() {
  return useApiCall(() => projectsApi.getAll(), [], [])
}

export function useProject(id: string) {
  return useApiCall(() => projectsApi.getById(id), [id])
}

// Schedules hooks
export function useSchedules() {
  return useApiCall(() => schedulesApi.getAll(), [], [])
}

export function useSchedule(id: string) {
  return useApiCall(() => schedulesApi.getById(id), [id])
}

export function useUpcomingSchedules() {
  return useApiCall(() => schedulesApi.getUpcoming(), [], [])
}

// Shoots hooks (backward compatibility)
export function useShoots() {
  return useApiCall(() => shootsApi.getAll())
}

export function useShoot(id: string) {
  return useApiCall(() => shootsApi.getById(id), [id])
}

export function useUpcomingShoots() {
  return useApiCall(() => shootsApi.getUpcoming())
}

// Tasks hooks
export function useTasks() {
  return useApiCall(() => tasksApi.getAll(), [], [])
}

export function useMyTasks() {
  return useApiCall(() => tasksApi.getMyTasks())
}

// Dashboard hooks
export function useDashboardStats() {
  return useApiCall(() => dashboardApi.getStats())
}

// Mutation hooks for create/update/delete operations
export function useCreateClient() {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const createClient = async (clientData: any) => {
    try {
      setLoading(true)
      setError(null)
      const result = await clientsApi.create(clientData)
      return result
    } catch (error: unknown) {
      setError(getErrorMessage(error, 'Failed to create client'))
      throw error
    } finally {
      setLoading(false)
    }
  }

  return { createClient, loading, error }
}

export function useUpdateClient() {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const updateClient = async (id: string, updates: any) => {
    try {
      setLoading(true)
      setError(null)
      const result = await clientsApi.update(id, updates)
      return result
    } catch (error: unknown) {
      setError(getErrorMessage(error, 'Failed to update client'))
      throw error
    } finally {
      setLoading(false)
    }
  }

  return { updateClient, loading, error }
}

export function useDeleteClient() {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const deleteClient = async (id: string) => {
    try {
      setLoading(true)
      setError(null)
      await clientsApi.delete(id)
    } catch (error: unknown) {
      setError(getErrorMessage(error, 'Failed to delete client'))
      throw error
    } finally {
      setLoading(false)
    }
  }

  return { deleteClient, loading, error }
}

export function useCreateProject() {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const createProject = async (projectData: any) => {
    try {
      setLoading(true)
      setError(null)
      const result = await projectsApi.create(projectData)
      return result
    } catch (error: unknown) {
      setError(getErrorMessage(error, 'Failed to create project'))
      throw error
    } finally {
      setLoading(false)
    }
  }

  return { createProject, loading, error }
}

export function useUpdateProject() {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const updateProject = async (id: string, updates: any) => {
    try {
      setLoading(true)
      setError(null)
      const result = await projectsApi.update(id, updates)
      return result
    } catch (error: unknown) {
      setError(getErrorMessage(error, 'Failed to update project'))
      throw error
    } finally {
      setLoading(false)
    }
  }

  return { updateProject, loading, error }
}

export function useCreateSchedule() {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const createScheduleWithVendors = async (scheduleData: any, vendors: any) => {
    try {
      setLoading(true)
      setError(null)

      // Call the server-side API endpoint instead of client-side method
      const response = await fetch('/api/schedules', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          schedule: scheduleData,
          vendors: vendors
        })
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to create schedule')
      }

      const result = await response.json()
      return result
    } catch (error: unknown) {
      setError(getErrorMessage(error, 'Failed to create schedule'))
      throw error
    } finally {
      setLoading(false)
    }
  }

  return { createScheduleWithVendors, loading, error }
}

// Keep for backward compatibility
export function useCreateShoot() {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const createShoot = async (shootData: any) => {
    try {
      setLoading(true)
      setError(null)
      const result = await shootsApi.create(shootData)
      return result
    } catch (error: unknown) {
      setError(getErrorMessage(error, 'Failed to create shoot'))
      throw error
    } finally {
      setLoading(false)
    }
  }

  return { createShoot, loading, error }
}

export function useUpdateSchedule() {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const updateSchedule = async (id: string, updates: any) => {
    try {
      setLoading(true)
      setError(null)
      const result = await schedulesApi.update(id, updates)
      return result
    } catch (error: unknown) {
      setError(getErrorMessage(error, 'Failed to update schedule'))
      throw error
    } finally {
      setLoading(false)
    }
  }

  const updateScheduleWithVendors = async (id: string, updates: any, vendors: any) => {
    try {
      setLoading(true)
      setError(null)
      const result = await schedulesApi.updateWithVendors(id, updates, vendors)
      return result
    } catch (error: unknown) {
      setError(getErrorMessage(error, 'Failed to update schedule'))
      throw error
    } finally {
      setLoading(false)
    }
  }

  return { updateSchedule, updateScheduleWithVendors, loading, error }
}

// Keep for backward compatibility
export function useUpdateShoot() {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const updateShoot = async (id: string, updates: any) => {
    try {
      setLoading(true)
      setError(null)
      const result = await shootsApi.update(id, updates)
      return result
    } catch (error: unknown) {
      setError(getErrorMessage(error, 'Failed to update shoot'))
      throw error
    } finally {
      setLoading(false)
    }
  }

  return { updateShoot, loading, error }
}

export function useCreateExpense() {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const createExpense = async (expenseData: any) => {
    try {
      setLoading(true)
      setError(null)
      const result = await expensesApi.create(expenseData)
      return result
    } catch (error: unknown) {
      setError(getErrorMessage(error, 'Failed to create expense'))
      throw error
    } finally {
      setLoading(false)
    }
  }

  return { createExpense, loading, error }
}

export function useCreateTask() {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const createTask = async (taskData: any) => {
    try {
      setLoading(true)
      setError(null)
      const result = await tasksApi.create(taskData)
      return result
    } catch (error: unknown) {
      setError(getErrorMessage(error, 'Failed to create task'))
      throw error
    } finally {
      setLoading(false)
    }
  }

  return { createTask, loading, error }
}

export function useUpdateTask() {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const updateTask = async (id: string, updates: any) => {
    try {
      setLoading(true)
      setError(null)
      const result = await tasksApi.update(id, updates)
      return result
    } catch (error: unknown) {
      setError(getErrorMessage(error, 'Failed to update task'))
      throw error
    } finally {
      setLoading(false)
    }
  }

  return { updateTask, loading, error }
}

export function usePayments() {
  const [data, setData] = useState<Payment[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const fetchPayments = async () => {
    try {
      setLoading(true)
      setError(null)
      const result = await paymentsApi.getAll()
      setData(result)
    } catch (error: unknown) {
      console.warn('Payments API failed, using empty array:', getErrorMessage(error, 'Unknown error'))
      setData([])
      setError(null)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchPayments()
  }, [])

  return { data, loading, error, refetch: fetchPayments }
}

export function useExpenses() {
  const [data, setData] = useState<Expense[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const fetchExpenses = async () => {
    try {
      setLoading(true)
      setError(null)
      const result = await expensesApi.getAll()
      setData(result)
    } catch (error: unknown) {
      console.warn('Expenses API failed, using empty array:', getErrorMessage(error, 'Unknown error'))
      setData([])
      setError(null)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchExpenses()
  }, [])

  return { data, loading, error, refetch: fetchExpenses }
}

export function useCreatePayment() {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const createPayment = async (paymentData: any) => {
    try {
      setLoading(true)
      setError(null)
      const result = await paymentsApi.create(paymentData)
      return result
    } catch (error: unknown) {
      setError(getErrorMessage(error, 'Failed to create payment'))
      throw error
    } finally {
      setLoading(false)
    }
  }

  return { createPayment, loading, error }
}

export function useUpdatePayment() {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const updatePayment = async (id: string, updates: any) => {
    try {
      setLoading(true)
      setError(null)
      const result = await paymentsApi.update(id, updates)
      return result
    } catch (error: unknown) {
      setError(getErrorMessage(error, 'Failed to update payment'))
      throw error
    } finally {
      setLoading(false)
    }
  }

  return { updatePayment, loading, error }
}

export function useUpdateExpense() {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const updateExpense = async (id: string, updates: any) => {
    try {
      setLoading(true)
      setError(null)
      const result = await expensesApi.update(id, updates)
      return result
    } catch (error: unknown) {
      setError(getErrorMessage(error, 'Failed to update expense'))
      throw error
    } finally {
      setLoading(false)
    }
  }

  return { updateExpense, loading, error }
}

export function useDeletePayment() {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const deletePayment = async (id: string) => {
    try {
      setLoading(true)
      setError(null)
      await paymentsApi.delete(id)
    } catch (error: unknown) {
      setError(getErrorMessage(error, 'Failed to delete payment'))
      throw error
    } finally {
      setLoading(false)
    }
  }

  return { deletePayment, loading, error }
}

export function useDeleteExpense() {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const deleteExpense = async (id: string) => {
    try {
      setLoading(true)
      setError(null)
      await expensesApi.delete(id)
    } catch (error: unknown) {
      setError(getErrorMessage(error, 'Failed to delete expense'))
      throw error
    } finally {
      setLoading(false)
    }
  }

  return { deleteExpense, loading, error }
}

export function useDeleteProject() {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const deleteProject = async (id: string) => {
    try {
      setLoading(true)
      setError(null)
      await projectsApi.delete(id)
    } catch (error: unknown) {
      setError(getErrorMessage(error, 'Failed to delete project'))
      throw error
    } finally {
      setLoading(false)
    }
  }

  return { deleteProject, loading, error }
}

export function useDeleteSchedule() {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const deleteSchedule = async (id: string) => {
    try {
      setLoading(true)
      setError(null)
      await schedulesApi.delete(id)
    } catch (error: unknown) {
      setError(getErrorMessage(error, 'Failed to delete schedule'))
      throw error
    } finally {
      setLoading(false)
    }
  }

  return { deleteSchedule, loading, error }
}

// Keep for backward compatibility
export function useDeleteShoot() {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const deleteShoot = async (id: string) => {
    try {
      setLoading(true)
      setError(null)
      await shootsApi.delete(id)
    } catch (error: unknown) {
      setError(getErrorMessage(error, 'Failed to delete shoot'))
      throw error
    } finally {
      setLoading(false)
    }
  }

  return { deleteShoot, loading, error }
}

export function useDeleteTask() {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const deleteTask = async (id: string) => {
    try {
      setLoading(true)
      setError(null)
      await tasksApi.delete(id)
    } catch (error: unknown) {
      setError(getErrorMessage(error, 'Failed to delete task'))
      throw error
    } finally {
      setLoading(false)
    }
  }

  return { deleteTask, loading, error }
}
