import { createClientSupabaseClient } from './auth'
import type { Task, Shoot, Project } from '@/types'

const supabase = createClientSupabaseClient()

/**
 * Check if all shoot-based tasks for a specific shoot are completed
 * and automatically complete the shoot if they are
 */
export async function checkAndCompleteShoot(shootId: string): Promise<void> {
  try {
    console.log('Checking shoot completion for shoot:', shootId)

    // Get the shoot details
    const { data: shoot, error: shootError } = await supabase
      .from('shoots')
      .select('id, status, project_id')
      .eq('id', shootId)
      .single()

    if (shootError || !shoot) {
      console.error('Error fetching shoot:', shootError)
      return
    }

    // Skip if shoot is already completed
    if (shoot.status === 'completed') {
      console.log('Shoot is already completed')
      return
    }

    // Get all shoot-based tasks for this shoot
    const { data: shootTasks, error: tasksError } = await supabase
      .from('tasks')
      .select('id, title, status')
      .eq('shoot_id', shootId)
      .neq('status', 'cancelled') // Exclude cancelled tasks

    if (tasksError) {
      console.error('Error fetching shoot tasks:', tasksError)
      return
    }

    if (!shootTasks || shootTasks.length === 0) {
      console.log('No shoot-based tasks found for shoot:', shootId)
      return
    }

    // Check if all shoot-based tasks are completed
    const allCompleted = shootTasks.every(task => task.status === 'completed')
    console.log('Shoot tasks status:', shootTasks.map(t => ({ title: t.title, status: t.status })))
    console.log('All shoot tasks completed:', allCompleted)

    if (allCompleted) {
      // Automatically complete the shoot
      const { error: updateError } = await supabase
        .from('shoots')
        .update({ 
          status: 'completed',
          actual_date: new Date().toISOString()
        })
        .eq('id', shootId)

      if (updateError) {
        console.error('Error updating shoot status:', updateError)
        return
      }

      console.log('Shoot automatically completed:', shootId)

      // Now check if the project should be completed
      await checkAndCompleteProject(shoot.project_id)
    }
  } catch (error) {
    console.error('Error in checkAndCompleteShoot:', error)
  }
}

/**
 * Check if all shoots and project-level tasks for a project are completed
 * and automatically complete the project if they are
 */
export async function checkAndCompleteProject(projectId: string): Promise<void> {
  try {
    console.log('Checking project completion for project:', projectId)

    // Get the project details
    const { data: project, error: projectError } = await supabase
      .from('projects')
      .select('id, status')
      .eq('id', projectId)
      .single()

    if (projectError || !project) {
      console.error('Error fetching project:', projectError)
      return
    }

    // Skip if project is already completed
    if (project.status === 'completed') {
      console.log('Project is already completed')
      return
    }

    // Get all shoots for this project
    const { data: shoots, error: shootsError } = await supabase
      .from('shoots')
      .select('id, status')
      .eq('project_id', projectId)
      .neq('status', 'cancelled') // Exclude cancelled shoots

    if (shootsError) {
      console.error('Error fetching project shoots:', shootsError)
      return
    }

    // Check if all shoots are completed
    const allShootsCompleted = shoots?.every(shoot => shoot.status === 'completed') ?? true
    console.log('Project shoots status:', shoots?.map(s => ({ id: s.id, status: s.status })))
    console.log('All shoots completed:', allShootsCompleted)

    if (!allShootsCompleted) {
      console.log('Not all shoots are completed yet')
      return
    }

    // Get all project-level tasks (tasks without shoot_id)
    const { data: projectTasks, error: projectTasksError } = await supabase
      .from('tasks')
      .select('id, title, status')
      .eq('project_id', projectId)
      .is('shoot_id', null) // Project-level tasks have no shoot_id
      .neq('status', 'cancelled') // Exclude cancelled tasks

    if (projectTasksError) {
      console.error('Error fetching project tasks:', projectTasksError)
      return
    }

    // Check if all project-level tasks are completed
    const allProjectTasksCompleted = projectTasks?.every(task => task.status === 'completed') ?? true
    console.log('Project tasks status:', projectTasks?.map(t => ({ title: t.title, status: t.status })))
    console.log('All project tasks completed:', allProjectTasksCompleted)

    if (allShootsCompleted && allProjectTasksCompleted) {
      // Automatically complete the project
      const { error: updateError } = await supabase
        .from('projects')
        .update({ status: 'completed' })
        .eq('id', projectId)

      if (updateError) {
        console.error('Error updating project status:', updateError)
        return
      }

      console.log('Project automatically completed:', projectId)
    }
  } catch (error) {
    console.error('Error in checkAndCompleteProject:', error)
  }
}

/**
 * Handle task status change and trigger automatic completion checks
 */
export async function handleTaskStatusChange(task: Task, newStatus: Task['status']): Promise<void> {
  try {
    console.log('Handling task status change:', { taskId: task.id, title: task.title, newStatus })

    // If task is completed, check for automatic completion
    if (newStatus === 'completed') {
      // If it's a shoot-based task, check shoot completion
      if (task.shoot_id) {
        await checkAndCompleteShoot(task.shoot_id)
      } 
      // If it's a project-level task, check project completion
      else if (task.project_id) {
        await checkAndCompleteProject(task.project_id)
      }
    }
  } catch (error) {
    console.error('Error in handleTaskStatusChange:', error)
  }
}
